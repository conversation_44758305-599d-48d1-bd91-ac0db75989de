<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Referral;
use App\Models\InvestmentPackage;
use App\Models\Transaction;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ReferralInvestmentIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create investment packages
        InvestmentPackage::factory()->create([
            'name' => 'Bronze',
            'price' => 1000.00,
            'daily_income' => 50.00,
            'duration_days' => 60,
            'total_return' => 3000.00,
            'is_active' => true,
        ]);
    }

    public function test_referral_bonuses_are_distributed_on_investment_purchase()
    {
        // Create user hierarchy: grandReferrer -> referrer -> investor
        $grandReferrer = User::factory()->create([
            'balance' => 0,
            'total_earnings' => 0,
        ]);

        $referrer = User::factory()->create([
            'balance' => 0,
            'total_earnings' => 0,
            'referred_by' => $grandReferrer->id,
        ]);

        $investor = User::factory()->create([
            'balance' => 2000.00,
            'referred_by' => $referrer->id,
        ]);

        // Create referral relationships
        Referral::createReferralRelationships($investor, $referrer);

        $package = InvestmentPackage::first();

        // Make investment purchase
        $response = $this->actingAs($investor)->postJson('/api/investments/purchase', [
            'package_id' => $package->id,
        ]);

        $response->assertStatus(201);

        // Check that referrer received level 1 bonus (30% of 1000 = 300)
        $referrer->refresh();
        $this->assertEquals(300.00, $referrer->balance);
        $this->assertEquals(300.00, $referrer->total_earnings);

        // Check that grand referrer received level 2 bonus (5% of 1000 = 50)
        $grandReferrer->refresh();
        $this->assertEquals(50.00, $grandReferrer->balance);
        $this->assertEquals(50.00, $grandReferrer->total_earnings);

        // Check referral records were updated
        $level1Referral = Referral::where('referee_id', $investor->id)
            ->where('level', 1)
            ->first();
        $this->assertEquals(300.00, $level1Referral->total_earned);

        $level2Referral = Referral::where('referee_id', $investor->id)
            ->where('level', 2)
            ->first();
        $this->assertEquals(50.00, $level2Referral->total_earned);

        // Check referral bonus transactions were created
        $referrerTransaction = Transaction::where('user_id', $referrer->id)
            ->where('type', 'referral_bonus')
            ->first();
        $this->assertNotNull($referrerTransaction);
        $this->assertEquals(300.00, $referrerTransaction->amount);
        $this->assertEquals('completed', $referrerTransaction->status);

        $grandReferrerTransaction = Transaction::where('user_id', $grandReferrer->id)
            ->where('type', 'referral_bonus')
            ->first();
        $this->assertNotNull($grandReferrerTransaction);
        $this->assertEquals(50.00, $grandReferrerTransaction->amount);
        $this->assertEquals('completed', $grandReferrerTransaction->status);
    }

    public function test_only_level_1_bonus_distributed_when_no_grand_referrer()
    {
        $referrer = User::factory()->create([
            'balance' => 0,
            'total_earnings' => 0,
        ]);

        $investor = User::factory()->create([
            'balance' => 2000.00,
            'referred_by' => $referrer->id,
        ]);

        // Create referral relationships (only level 1 since no grand referrer)
        Referral::createReferralRelationships($investor, $referrer);

        $package = InvestmentPackage::first();

        // Make investment purchase
        $response = $this->actingAs($investor)->postJson('/api/investments/purchase', [
            'package_id' => $package->id,
        ]);

        $response->assertStatus(201);

        // Check that referrer received level 1 bonus
        $referrer->refresh();
        $this->assertEquals(300.00, $referrer->balance);

        // Check only one referral relationship exists
        $referrals = Referral::where('referee_id', $investor->id)->get();
        $this->assertCount(1, $referrals);
        $this->assertEquals(1, $referrals->first()->level);

        // Check only one referral bonus transaction exists
        $bonusTransactions = Transaction::where('type', 'referral_bonus')->get();
        $this->assertCount(1, $bonusTransactions);
    }

    public function test_no_referral_bonuses_for_user_without_referrer()
    {
        $investor = User::factory()->create([
            'balance' => 2000.00,
            'referred_by' => null, // No referrer
        ]);

        $package = InvestmentPackage::first();

        // Make investment purchase
        $response = $this->actingAs($investor)->postJson('/api/investments/purchase', [
            'package_id' => $package->id,
        ]);

        $response->assertStatus(201);

        // Check no referral relationships exist
        $referrals = Referral::where('referee_id', $investor->id)->get();
        $this->assertCount(0, $referrals);

        // Check no referral bonus transactions exist
        $bonusTransactions = Transaction::where('type', 'referral_bonus')->get();
        $this->assertCount(0, $bonusTransactions);
    }

    public function test_multiple_investments_accumulate_referral_bonuses()
    {
        $referrer = User::factory()->create([
            'balance' => 0,
            'total_earnings' => 0,
        ]);

        $investor = User::factory()->create([
            'balance' => 5000.00,
            'referred_by' => $referrer->id,
        ]);

        // Create referral relationships
        Referral::createReferralRelationships($investor, $referrer);

        $package = InvestmentPackage::first();

        // Make first investment
        $this->actingAs($investor)->postJson('/api/investments/purchase', [
            'package_id' => $package->id,
        ]);

        // Make second investment
        $this->actingAs($investor)->postJson('/api/investments/purchase', [
            'package_id' => $package->id,
        ]);

        // Check that referrer received bonuses from both investments
        $referrer->refresh();
        $this->assertEquals(600.00, $referrer->balance); // 300 + 300
        $this->assertEquals(600.00, $referrer->total_earnings);

        // Check referral record shows accumulated earnings
        $referral = Referral::where('referee_id', $investor->id)
            ->where('level', 1)
            ->first();
        $this->assertEquals(600.00, $referral->total_earned);

        // Check two referral bonus transactions exist
        $bonusTransactions = Transaction::where('user_id', $referrer->id)
            ->where('type', 'referral_bonus')
            ->get();
        $this->assertCount(2, $bonusTransactions);
        $this->assertEquals(300.00, $bonusTransactions->first()->amount);
        $this->assertEquals(300.00, $bonusTransactions->last()->amount);
    }
}
