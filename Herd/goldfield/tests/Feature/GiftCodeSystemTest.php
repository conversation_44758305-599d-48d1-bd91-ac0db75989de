<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\DailyGiftCode;
use App\Models\GiftCodeClaim;
use App\Models\Transaction;
use Carbon\Carbon;

class GiftCodeSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Set a fixed time for testing
        Carbon::setTestNow('2025-07-25 18:00:00'); // 6:00 PM
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow(); // Reset time
        parent::tearDown();
    }

    public function test_can_generate_daily_gift_code()
    {
        $giftCode = DailyGiftCode::createTodaysCode(150.00);

        $this->assertDatabaseHas('daily_gift_codes', [
            'id' => $giftCode->id,
            'amount' => '150.00',
            'valid_date' => Carbon::today()->format('Y-m-d') . ' 00:00:00',
            'is_active' => 1,
        ]);

        $this->assertTrue(str_starts_with($giftCode->code, 'GOLD'));
        $this->assertEquals(12, strlen($giftCode->code)); // GOLD + 8 characters
    }

    public function test_gift_code_is_claimable_after_530_pm()
    {
        $giftCode = DailyGiftCode::createTodaysCode();

        // Test at 6:00 PM (after 5:30 PM)
        Carbon::setTestNow('2025-07-25 18:00:00');
        $this->assertTrue($giftCode->isClaimable());

        // Test at 5:00 PM (before 5:30 PM)
        Carbon::setTestNow('2025-07-25 17:00:00');
        $this->assertFalse($giftCode->isClaimable());

        // Test at exactly 5:30 PM
        Carbon::setTestNow('2025-07-25 17:30:00');
        $this->assertTrue($giftCode->isClaimable());
    }

    public function test_gift_code_not_claimable_on_different_day()
    {
        $giftCode = DailyGiftCode::createTodaysCode();

        // Test on next day
        Carbon::setTestNow('2025-07-26 18:00:00');
        $this->assertFalse($giftCode->isClaimable());
    }

    public function test_user_can_claim_gift_code_successfully()
    {
        $user = User::factory()->create(['balance' => 500.00]);
        $giftCode = DailyGiftCode::createTodaysCode(100.00);

        $response = $this->actingAs($user)
            ->postJson('/api/gift-codes/claim');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Gift code claimed successfully!',
                'data' => [
                    'amount' => 100.00,
                    'new_balance' => 600.00,
                ]
            ]);

        // Check database records
        $this->assertDatabaseHas('gift_code_claims', [
            'user_id' => $user->id,
            'gift_code_id' => $giftCode->id,
            'amount' => 100.00,
        ]);

        $this->assertDatabaseHas('transactions', [
            'user_id' => $user->id,
            'type' => 'gift_code',
            'amount' => 100.00,
            'status' => 'completed',
        ]);

        // Check user balance updated
        $this->assertEquals(600.00, $user->fresh()->balance);
        $this->assertEquals(100.00, $user->fresh()->total_earnings);

        // Check gift code usage count updated
        $this->assertEquals(1, $giftCode->fresh()->used_count);
    }

    public function test_user_cannot_claim_gift_code_before_530_pm()
    {
        Carbon::setTestNow('2025-07-25 17:00:00'); // 5:00 PM

        $user = User::factory()->create();
        $giftCode = DailyGiftCode::createTodaysCode();

        $response = $this->actingAs($user)
            ->postJson('/api/gift-codes/claim');

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Gift code is not yet available. Please wait until 5:30 PM.'
            ]);

        $this->assertDatabaseMissing('gift_code_claims', [
            'user_id' => $user->id,
            'gift_code_id' => $giftCode->id,
        ]);
    }

    public function test_user_cannot_claim_gift_code_twice()
    {
        $user = User::factory()->create(['balance' => 500.00]);
        $giftCode = DailyGiftCode::createTodaysCode(100.00);

        // First claim - should succeed
        $response1 = $this->actingAs($user)
            ->postJson('/api/gift-codes/claim');
        $response1->assertStatus(200);

        // Second claim - should fail
        $response2 = $this->actingAs($user)
            ->postJson('/api/gift-codes/claim');

        $response2->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'You have already claimed today\'s gift code'
            ]);

        // Should only have one claim record
        $this->assertEquals(1, GiftCodeClaim::where('user_id', $user->id)->count());
        $this->assertEquals(600.00, $user->fresh()->balance); // Only one bonus applied
    }

    public function test_gift_code_cannot_be_claimed_when_limit_reached()
    {
        $user = User::factory()->create();
        $giftCode = DailyGiftCode::createTodaysCode();
        $giftCode->update(['usage_limit' => 1, 'used_count' => 1]);

        $response = $this->actingAs($user)
            ->postJson('/api/gift-codes/claim');

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Gift code usage limit has been reached'
            ]);
    }

    public function test_cannot_claim_when_no_gift_code_exists()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->postJson('/api/gift-codes/claim');

        $response->assertStatus(404)
            ->assertJson([
                'success' => false,
                'message' => 'No gift code available for today'
            ]);
    }

    public function test_can_get_todays_gift_code_info()
    {
        $user = User::factory()->create();
        $giftCode = DailyGiftCode::createTodaysCode(150.00);

        $response = $this->actingAs($user)
            ->getJson('/api/gift-codes/today');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'code' => $giftCode->code,
                    'amount' => 150.00,
                    'valid_date' => Carbon::today()->format('Y-m-d'),
                    'is_claimable' => true,
                    'has_claimed' => false,
                    'claims_remaining' => 1000,
                    'gift_time' => '17:30:00',
                ]
            ]);
    }

    public function test_gift_code_info_shows_claimed_status()
    {
        $user = User::factory()->create();
        $giftCode = DailyGiftCode::createTodaysCode();

        // Claim the gift code
        GiftCodeClaim::create([
            'user_id' => $user->id,
            'gift_code_id' => $giftCode->id,
            'amount' => $giftCode->amount,
        ]);

        $response = $this->actingAs($user)
            ->getJson('/api/gift-codes/today');

        $response->assertStatus(200)
            ->assertJsonPath('data.has_claimed', true);
    }

    public function test_can_get_gift_code_claim_history()
    {
        $user = User::factory()->create();
        $giftCode1 = DailyGiftCode::create([
            'code' => 'GOLD12345678',
            'amount' => 100.00,
            'valid_date' => Carbon::yesterday(),
            'is_active' => true,
            'usage_limit' => 1000,
            'used_count' => 1,
        ]);

        $claim = GiftCodeClaim::create([
            'user_id' => $user->id,
            'gift_code_id' => $giftCode1->id,
            'amount' => 100.00,
        ]);

        $response = $this->actingAs($user)
            ->getJson('/api/gift-codes/history');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'data' => [
                        [
                            'id' => $claim->id,
                            'amount' => '100.00',
                            'gift_code' => [
                                'code' => 'GOLD12345678',
                                'valid_date' => Carbon::yesterday()->format('Y-m-d') . 'T00:00:00.000000Z',
                            ]
                        ]
                    ]
                ]
            ]);
    }

    public function test_gift_code_generation_command()
    {
        $this->artisan('gift-code:generate', ['--amount' => 200])
            ->expectsOutput('Daily gift code generated successfully!')
            ->assertExitCode(0);

        $this->assertDatabaseHas('daily_gift_codes', [
            'amount' => '200.00',
            'valid_date' => Carbon::today()->format('Y-m-d') . ' 00:00:00',
            'is_active' => 1,
        ]);
    }

    public function test_gift_code_generation_command_handles_existing_code()
    {
        // Create existing gift code
        DailyGiftCode::createTodaysCode(100.00);

        $this->artisan('gift-code:generate', ['--amount' => 200])
            ->expectsOutput('Gift code for today already exists:')
            ->assertExitCode(0);

        // Should still have the original amount, not updated
        $this->assertDatabaseHas('daily_gift_codes', [
            'amount' => '100.00',
            'valid_date' => Carbon::today()->format('Y-m-d') . ' 00:00:00',
        ]);
    }

    public function test_gift_code_unique_code_generation()
    {
        $codes = [];
        for ($i = 0; $i < 10; $i++) {
            $code = DailyGiftCode::generateCode();
            $this->assertNotContains($code, $codes);
            $codes[] = $code;
            $this->assertTrue(str_starts_with($code, 'GOLD'));
            $this->assertEquals(12, strlen($code));
        }
    }

    public function test_has_been_claimed_by_user_method()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $giftCode = DailyGiftCode::createTodaysCode();

        $this->assertFalse($giftCode->hasBeenClaimedByUser($user1->id));
        $this->assertFalse($giftCode->hasBeenClaimedByUser($user2->id));

        // User 1 claims
        GiftCodeClaim::create([
            'user_id' => $user1->id,
            'gift_code_id' => $giftCode->id,
            'amount' => $giftCode->amount,
        ]);

        $this->assertTrue($giftCode->hasBeenClaimedByUser($user1->id));
        $this->assertFalse($giftCode->hasBeenClaimedByUser($user2->id));
    }

    public function test_has_reached_limit_method()
    {
        $giftCode = DailyGiftCode::createTodaysCode();
        $giftCode->update(['usage_limit' => 5, 'used_count' => 4]);

        $this->assertFalse($giftCode->hasReachedLimit());

        $giftCode->update(['used_count' => 5]);
        $this->assertTrue($giftCode->hasReachedLimit());

        $giftCode->update(['used_count' => 6]);
        $this->assertTrue($giftCode->hasReachedLimit());
    }
}
