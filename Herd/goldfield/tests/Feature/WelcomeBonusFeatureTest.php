<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Transaction;
use App\Services\WelcomeBonusService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WelcomeBonusFeatureTest extends TestCase
{
    use RefreshDatabase;

    public function test_new_user_registration_automatically_credits_welcome_bonus()
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
        ]);

        $response->assertRedirect('/dashboard');

        // Check user was created with welcome bonus
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals(1500.00, $user->balance);
        $this->assertEquals(1500.00, $user->total_earnings);
        $this->assertTrue($user->welcome_bonus_claimed);

        // Check welcome bonus transaction was created
        $transaction = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_WELCOME_BONUS)
            ->first();

        $this->assertNotNull($transaction);
        $this->assertEquals(1500.00, $transaction->amount);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertEquals('Welcome bonus for new user registration', $transaction->description);
    }

    public function test_welcome_bonus_is_credited_with_referral_registration()
    {
        $referrer = User::factory()->create([
            'referral_code' => 'GF12345678'
        ]);

        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
            'referral_code' => 'GF12345678',
        ]);

        $response->assertRedirect('/dashboard');

        // Check user was created with welcome bonus and referrer
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals($referrer->id, $user->referred_by);
        $this->assertEquals(1500.00, $user->balance);
        $this->assertEquals(1500.00, $user->total_earnings);
        $this->assertTrue($user->welcome_bonus_claimed);

        // Check welcome bonus transaction was created
        $transaction = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_WELCOME_BONUS)
            ->first();

        $this->assertNotNull($transaction);
        $this->assertEquals(1500.00, $transaction->amount);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
    }

    public function test_welcome_bonus_service_can_be_used_independently()
    {
        $user = User::factory()->create([
            'balance' => 0,
            'total_earnings' => 0,
            'welcome_bonus_claimed' => false,
        ]);

        $welcomeBonusService = new WelcomeBonusService();
        $result = $welcomeBonusService->creditWelcomeBonus($user);

        $this->assertTrue($result);

        // Check user was updated
        $user->refresh();
        $this->assertEquals(1500.00, $user->balance);
        $this->assertEquals(1500.00, $user->total_earnings);
        $this->assertTrue($user->welcome_bonus_claimed);

        // Check transaction was created
        $transaction = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_WELCOME_BONUS)
            ->first();

        $this->assertNotNull($transaction);
        $this->assertEquals(1500.00, $transaction->amount);
    }

    public function test_welcome_bonus_prevents_duplicate_credits()
    {
        // Create user with welcome bonus already claimed
        $user = User::factory()->create([
            'balance' => 1500.00,
            'total_earnings' => 1500.00,
            'welcome_bonus_claimed' => true,
        ]);

        // Create existing welcome bonus transaction
        Transaction::factory()->welcomeBonus()->create([
            'user_id' => $user->id,
            'amount' => 1500.00,
        ]);

        $welcomeBonusService = new WelcomeBonusService();
        $result = $welcomeBonusService->creditWelcomeBonus($user);

        $this->assertFalse($result);

        // Check user balance didn't change
        $user->refresh();
        $this->assertEquals(1500.00, $user->balance);
        $this->assertEquals(1500.00, $user->total_earnings);

        // Check only one welcome bonus transaction exists
        $transactionCount = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_WELCOME_BONUS)
            ->count();

        $this->assertEquals(1, $transactionCount);
    }

    public function test_welcome_bonus_eligibility_check()
    {
        $welcomeBonusService = new WelcomeBonusService();

        // Test eligible user
        $eligibleUser = User::factory()->create([
            'welcome_bonus_claimed' => false,
        ]);

        $this->assertTrue($welcomeBonusService->isEligibleForWelcomeBonus($eligibleUser));

        // Test ineligible user (already claimed)
        $ineligibleUser = User::factory()->create([
            'welcome_bonus_claimed' => true,
        ]);

        $this->assertFalse($welcomeBonusService->isEligibleForWelcomeBonus($ineligibleUser));

        // Test ineligible user (transaction exists)
        $userWithTransaction = User::factory()->create([
            'welcome_bonus_claimed' => false,
        ]);

        Transaction::factory()->welcomeBonus()->create([
            'user_id' => $userWithTransaction->id,
        ]);

        $this->assertFalse($welcomeBonusService->isEligibleForWelcomeBonus($userWithTransaction));
    }

    public function test_welcome_bonus_amount_is_configurable()
    {
        $welcomeBonusService = new WelcomeBonusService();
        $amount = $welcomeBonusService->getWelcomeBonusAmount();

        $this->assertEquals(1500.00, $amount);
        $this->assertIsFloat($amount);
    }

    public function test_can_retrieve_welcome_bonus_transaction()
    {
        $user = User::factory()->create();
        $welcomeBonusService = new WelcomeBonusService();

        // Initially no transaction
        $this->assertNull($welcomeBonusService->getWelcomeBonusTransaction($user));

        // Credit welcome bonus
        $welcomeBonusService->creditWelcomeBonus($user);

        // Now transaction should exist
        $transaction = $welcomeBonusService->getWelcomeBonusTransaction($user);
        $this->assertNotNull($transaction);
        $this->assertEquals(Transaction::TYPE_WELCOME_BONUS, $transaction->type);
        $this->assertEquals(1500.00, $transaction->amount);
    }

    public function test_registration_rollback_on_welcome_bonus_failure()
    {
        // This test ensures that if welcome bonus fails, the entire registration is rolled back
        // We'll simulate this by creating a scenario where the transaction would fail

        $initialUserCount = User::count();
        $initialTransactionCount = Transaction::count();

        // Try to register a user
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
        ]);

        // Registration should succeed
        $response->assertRedirect('/dashboard');

        // Check user was created
        $this->assertEquals($initialUserCount + 1, User::count());
        $this->assertEquals($initialTransactionCount + 1, Transaction::count());

        // Check user has welcome bonus
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue($user->welcome_bonus_claimed);
        $this->assertEquals(1500.00, $user->balance);
    }

    public function test_multiple_users_can_receive_welcome_bonus()
    {
        // Register first user
        $response1 = $this->post('/register', [
            'name' => 'User One',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
        ]);
        $response1->assertRedirect('/dashboard');

        // Logout to allow second registration
        $this->post('/logout');

        // Register second user
        $response2 = $this->post('/register', [
            'name' => 'User Two',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
        ]);
        $response2->assertRedirect('/dashboard');

        // Check both users received welcome bonus
        $user1 = User::where('email', '<EMAIL>')->first();
        $user2 = User::where('email', '<EMAIL>')->first();

        $this->assertTrue($user1->welcome_bonus_claimed);
        $this->assertEquals(1500.00, $user1->balance);

        $this->assertTrue($user2->welcome_bonus_claimed);
        $this->assertEquals(1500.00, $user2->balance);

        // Check both have welcome bonus transactions
        $transaction1 = Transaction::where('user_id', $user1->id)
            ->where('type', Transaction::TYPE_WELCOME_BONUS)
            ->first();
        $transaction2 = Transaction::where('user_id', $user2->id)
            ->where('type', Transaction::TYPE_WELCOME_BONUS)
            ->first();

        $this->assertNotNull($transaction1);
        $this->assertNotNull($transaction2);
    }
}
