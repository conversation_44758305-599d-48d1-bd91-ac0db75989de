<?php

namespace Tests\Feature;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WithdrawalFeatureTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_user_can_request_withdrawal_with_sufficient_balance()
    {
        $user = User::factory()->create(['balance' => 5000]);

        $response = $this->actingAs($user)
            ->postJson('/api/withdrawals/request', [
                'amount' => 2000
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Withdrawal request submitted successfully'
            ])
            ->assertJsonStructure([
                'data' => [
                    'transaction_id',
                    'requested_amount',
                    'withdrawal_charge',
                    'net_amount',
                    'status',
                    'remaining_balance'
                ]
            ]);

        // Check that transaction was created
        $this->assertDatabaseHas('transactions', [
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 2000,
            'status' => Transaction::STATUS_PENDING
        ]);

        // Check that balance was deducted
        $this->assertEquals(3000, $user->fresh()->balance);
    }

    public function test_user_cannot_request_withdrawal_with_insufficient_balance()
    {
        $user = User::factory()->create(['balance' => 500]);

        $response = $this->actingAs($user)
            ->postJson('/api/withdrawals/request', [
                'amount' => 2000
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['amount']);

        // Check that no transaction was created
        $this->assertDatabaseMissing('transactions', [
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL
        ]);

        // Check that balance was not changed
        $this->assertEquals(500, $user->fresh()->balance);
    }

    public function test_user_cannot_request_withdrawal_below_minimum_amount()
    {
        $user = User::factory()->create(['balance' => 5000]);

        $response = $this->actingAs($user)
            ->postJson('/api/withdrawals/request', [
                'amount' => 500 // Below minimum of 1000
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['amount']);

        // Check that no transaction was created
        $this->assertDatabaseMissing('transactions', [
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL
        ]);
    }

    public function test_withdrawal_charge_calculation_is_correct()
    {
        $user = User::factory()->create(['balance' => 5000]);

        $response = $this->actingAs($user)
            ->postJson('/api/withdrawals/request', [
                'amount' => 2000
            ]);

        $response->assertStatus(200);

        $data = $response->json('data');

        // 10% charge on 2000 should be 200
        $this->assertEquals(200, $data['withdrawal_charge']);
        // Net amount should be 1800
        $this->assertEquals(1800, $data['net_amount']);
    }

    public function test_user_can_get_withdrawal_history()
    {
        $user = User::factory()->create(['balance' => 5000]);

        // Create some withdrawal transactions
        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 1000,
            'status' => Transaction::STATUS_COMPLETED
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 2000,
            'status' => Transaction::STATUS_PENDING
        ]);

        $response = $this->actingAs($user)
            ->getJson('/api/withdrawals/history');

        $response->assertStatus(200)
            ->assertJson(['success' => true])
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'amount',
                        'status',
                        'created_at'
                    ]
                ],
                'pagination'
            ]);

        $this->assertCount(2, $response->json('data'));
    }

    public function test_user_can_filter_withdrawal_history_by_status()
    {
        $user = User::factory()->create(['balance' => 5000]);

        // Create withdrawal transactions with different statuses
        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 1000,
            'status' => Transaction::STATUS_COMPLETED
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 2000,
            'status' => Transaction::STATUS_PENDING
        ]);

        $response = $this->actingAs($user)
            ->getJson('/api/withdrawals/history?status=pending');

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals('pending', $response->json('data.0.status'));
    }

    public function test_user_can_get_withdrawal_statistics()
    {
        $user = User::factory()->create(['balance' => 3000]);

        // Create withdrawal transactions with different statuses
        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 1000,
            'status' => Transaction::STATUS_COMPLETED
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 2000,
            'status' => Transaction::STATUS_PENDING
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 500,
            'status' => Transaction::STATUS_FAILED
        ]);

        $response = $this->actingAs($user)
            ->getJson('/api/withdrawals/statistics');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'total_withdrawals' => 1000,
                    'pending_withdrawals' => 2000,
                    'failed_withdrawals' => 500,
                    'withdrawal_count' => 3,
                    'current_balance' => 3000,
                    'minimum_withdrawal' => 1000,
                    'withdrawal_charge_percentage' => 10
                ]
            ]);
    }

    public function test_user_can_calculate_withdrawal_details()
    {
        $user = User::factory()->create(['balance' => 5000]);

        $response = $this->actingAs($user)
            ->postJson('/api/withdrawals/calculate', [
                'amount' => 3000
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'requested_amount' => 3000,
                    'withdrawal_charge' => 300, // 10% of 3000
                    'net_amount' => 2700, // 3000 - 300
                    'charge_percentage' => 10
                ]
            ]);
    }

    public function test_admin_can_approve_withdrawal_request()
    {
        $user = User::factory()->create(['balance' => 3000]);

        // Create a pending withdrawal transaction
        $transaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 2000,
            'status' => Transaction::STATUS_PENDING
        ]);

        $response = $this->actingAs($user) // In real app, this would be admin user
            ->putJson("/api/withdrawals/{$transaction->id}/process", [
                'action' => 'approve',
                'admin_notes' => 'Approved by admin'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Withdrawal approved successfully'
            ]);

        // Check that transaction status was updated
        $this->assertDatabaseHas('transactions', [
            'id' => $transaction->id,
            'status' => Transaction::STATUS_COMPLETED
        ]);
    }

    public function test_admin_can_reject_withdrawal_request()
    {
        $user = User::factory()->create(['balance' => 3000]);

        // Create a pending withdrawal transaction
        $transaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 2000,
            'status' => Transaction::STATUS_PENDING
        ]);

        $response = $this->actingAs($user) // In real app, this would be admin user
            ->putJson("/api/withdrawals/{$transaction->id}/process", [
                'action' => 'reject',
                'admin_notes' => 'Insufficient documentation'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Withdrawal rejected and amount refunded'
            ]);

        // Check that transaction status was updated
        $this->assertDatabaseHas('transactions', [
            'id' => $transaction->id,
            'status' => Transaction::STATUS_FAILED
        ]);

        // Check that amount was refunded to user balance
        $this->assertEquals(5000, $user->fresh()->balance); // 3000 + 2000 refund
    }

    public function test_unauthenticated_user_cannot_access_withdrawal_endpoints()
    {
        $response = $this->postJson('/api/withdrawals/request', [
            'amount' => 1000
        ]);

        $response->assertStatus(401);

        $response = $this->getJson('/api/withdrawals/history');
        $response->assertStatus(401);

        $response = $this->getJson('/api/withdrawals/statistics');
        $response->assertStatus(401);
    }
}
