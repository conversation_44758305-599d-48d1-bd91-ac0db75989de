<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Investment;
use App\Models\InvestmentPackage;
use App\Models\Transaction;
use App\Models\Referral;
use App\Models\DailyGiftCode;
use App\Models\GiftCodeClaim;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class DashboardTest extends TestCase
{
    use RefreshDatabase;

    private User $user;
    private InvestmentPackage $package;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create([
            'balance' => 5000.00,
            'total_earnings' => 2000.00,
        ]);

        $this->package = InvestmentPackage::factory()->create([
            'name' => 'Bronze',
            'price' => 1000.00,
            'daily_income' => 50.00,
            'duration_days' => 60,
            'total_return' => 3000.00,
        ]);
    }

    public function test_dashboard_page_loads_successfully()
    {
        $response = $this->actingAs($this->user)->get('/dashboard');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('dashboard')
                ->has('user')
                ->has('statistics')
                ->has('recent_transactions')
                ->has('active_investments')
                ->has('investment_packages')
                ->has('gift_code_status')
                ->has('referral_stats')
        );
    }

    public function test_dashboard_statistics_api_returns_correct_data()
    {
        // Create some test data
        $investment = Investment::factory()->create([
            'user_id' => $this->user->id,
            'package_id' => $this->package->id,
            'amount' => 1000.00,
            'daily_income' => 50.00,
            'status' => Investment::STATUS_ACTIVE,
        ]);

        Transaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 50.00,
            'status' => Transaction::STATUS_COMPLETED,
            'created_at' => Carbon::today(),
        ]);

        $response = $this->actingAs($this->user)->get('/api/dashboard/statistics');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'statistics' => [
                'current_balance',
                'total_earnings',
                'today_earnings',
                'monthly_earnings',
                'pending_withdrawals',
                'total_invested',
                'active_investments_count',
                'total_investment_value',
                'expected_daily_income',
                'referral_earnings',
                'total_referrals',
            ],
            'referral_stats' => [
                'total_referrals',
                'level_1_count',
                'level_2_count',
                'level_1_earnings',
                'level_2_earnings',
                'total_earnings',
            ],
        ]);

        $data = $response->json();
        $this->assertEquals(5000.00, $data['statistics']['current_balance']);
        $this->assertEquals(2000.00, $data['statistics']['total_earnings']);
        $this->assertEquals(50.00, $data['statistics']['today_earnings']);
        $this->assertEquals(1, $data['statistics']['active_investments_count']);
        $this->assertEquals(50.00, $data['statistics']['expected_daily_income']);
    }

    public function test_dashboard_activities_api_returns_recent_data()
    {
        // Create test investment
        $investment = Investment::factory()->create([
            'user_id' => $this->user->id,
            'package_id' => $this->package->id,
            'status' => Investment::STATUS_ACTIVE,
        ]);

        // Create test transaction
        Transaction::factory()->create([
            'user_id' => $this->user->id,
            'type' => Transaction::TYPE_INVESTMENT,
            'amount' => 1000.00,
            'reference_id' => $investment->id,
            'status' => Transaction::STATUS_COMPLETED,
        ]);

        $response = $this->actingAs($this->user)->get('/api/dashboard/activities');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'recent_transactions' => [
                '*' => [
                    'id',
                    'type',
                    'amount',
                    'description',
                    'status',
                    'created_at',
                    'formatted_date',
                    'reference',
                ]
            ],
            'active_investments' => [
                '*' => [
                    'id',
                    'package_name',
                    'amount',
                    'daily_income',
                    'start_date',
                    'end_date',
                    'status',
                    'total_earned',
                    'remaining_days',
                    'progress_percentage',
                    'expected_total_return',
                ]
            ],
        ]);
    }

    public function test_dashboard_data_api_returns_comprehensive_data()
    {
        $response = $this->actingAs($this->user)->get('/api/dashboard/data');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'user' => [
                'id',
                'name',
                'email',
                'balance',
                'total_earnings',
                'referral_code',
                'referral_link',
            ],
            'statistics',
            'recent_transactions',
            'active_investments',
            'investment_packages',
            'gift_code_status',
            'referral_stats',
        ]);

        $data = $response->json();
        $this->assertEquals($this->user->id, $data['user']['id']);
        $this->assertEquals($this->user->name, $data['user']['name']);
        $this->assertEquals($this->user->balance, $data['user']['balance']);
    }

    public function test_gift_code_status_when_no_code_available()
    {
        $response = $this->actingAs($this->user)->get('/api/dashboard/data');

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertFalse($data['gift_code_status']['available']);
        $this->assertFalse($data['gift_code_status']['claimed']);
        $this->assertEquals('No gift code available today', $data['gift_code_status']['message']);
    }

    public function test_gift_code_status_when_code_available_and_time_reached()
    {
        // Create today's gift code
        $giftCode = DailyGiftCode::factory()->create([
            'valid_date' => Carbon::today(),
            'amount' => 100.00,
            'is_active' => true,
        ]);

        // Mock current time to be after 5:30 PM
        Carbon::setTestNow(Carbon::today()->setTime(18, 0));

        $response = $this->actingAs($this->user)->get('/api/dashboard/data');

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertTrue($data['gift_code_status']['available']);
        $this->assertFalse($data['gift_code_status']['claimed']);
        $this->assertEquals($giftCode->id, $data['gift_code_status']['gift_code_id']);
        $this->assertEquals(100.00, $data['gift_code_status']['amount']);
    }

    public function test_gift_code_status_when_already_claimed()
    {
        // Create today's gift code
        $giftCode = DailyGiftCode::factory()->create([
            'valid_date' => Carbon::today(),
            'amount' => 100.00,
            'is_active' => true,
        ]);

        // Create claim record
        GiftCodeClaim::factory()->create([
            'user_id' => $this->user->id,
            'gift_code_id' => $giftCode->id,
        ]);

        // Mock current time to be after 5:30 PM
        Carbon::setTestNow(Carbon::today()->setTime(18, 0));

        $response = $this->actingAs($this->user)->get('/api/dashboard/data');

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertFalse($data['gift_code_status']['available']);
        $this->assertTrue($data['gift_code_status']['claimed']);
    }

    public function test_investment_packages_are_returned_correctly()
    {
        // Create additional packages
        InvestmentPackage::factory()->create([
            'name' => 'Silver',
            'price' => 2000.00,
            'daily_income' => 120.00,
            'is_active' => true,
        ]);

        InvestmentPackage::factory()->create([
            'name' => 'Gold',
            'price' => 5000.00,
            'daily_income' => 300.00,
            'is_active' => false, // This should not appear
        ]);

        $response = $this->actingAs($this->user)->get('/api/dashboard/data');

        $response->assertStatus(200);
        $data = $response->json();

        // Should only return active packages
        $this->assertCount(2, $data['investment_packages']);

        // Should be ordered by price ascending
        $this->assertEquals('Bronze', $data['investment_packages'][0]['name']);
        $this->assertEquals('Silver', $data['investment_packages'][1]['name']);
    }

    public function test_referral_statistics_are_calculated_correctly()
    {
        // Create referral users
        $referee1 = User::factory()->create(['referred_by' => $this->user->id]);
        $referee2 = User::factory()->create(['referred_by' => $this->user->id]);
        $referee3 = User::factory()->create(['referred_by' => $referee1->id]); // Level 2

        // Create referral relationships
        Referral::factory()->create([
            'referrer_id' => $this->user->id,
            'referee_id' => $referee1->id,
            'level' => 1,
            'total_earned' => 150.00,
        ]);

        Referral::factory()->create([
            'referrer_id' => $this->user->id,
            'referee_id' => $referee2->id,
            'level' => 1,
            'total_earned' => 200.00,
        ]);

        Referral::factory()->create([
            'referrer_id' => $this->user->id,
            'referee_id' => $referee3->id,
            'level' => 2,
            'total_earned' => 50.00,
        ]);

        $response = $this->actingAs($this->user)->get('/api/dashboard/statistics');

        $response->assertStatus(200);
        $data = $response->json();

        $this->assertEquals(3, $data['referral_stats']['total_referrals']);
        $this->assertEquals(2, $data['referral_stats']['level_1_count']);
        $this->assertEquals(1, $data['referral_stats']['level_2_count']);
        $this->assertEquals(350.00, $data['referral_stats']['level_1_earnings']);
        $this->assertEquals(50.00, $data['referral_stats']['level_2_earnings']);
        $this->assertEquals(400.00, $data['referral_stats']['total_earnings']);
    }

    public function test_dashboard_requires_authentication()
    {
        $response = $this->get('/dashboard');
        $response->assertRedirect('/login');

        $response = $this->get('/api/dashboard/data');
        $response->assertRedirect('/login');

        $response = $this->get('/api/dashboard/statistics');
        $response->assertRedirect('/login');

        $response = $this->get('/api/dashboard/activities');
        $response->assertRedirect('/login');
    }
}
