<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Referral;
use App\Models\Transaction;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ReferralFeatureTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_get_referral_stats()
    {
        $user = User::factory()->create();
        $referee = User::factory()->create();

        Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => $referee->id,
            'level' => 1,
            'bonus_percentage' => 30.00,
            'total_earned' => 300.00,
        ]);

        $response = $this->actingAs($user)->getJson('/api/referrals/stats');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'referral_code',
                    'referral_link',
                    'stats' => [
                        'total_referrals',
                        'level_1_count',
                        'level_2_count',
                        'level_1_earnings',
                        'level_2_earnings',
                        'total_earnings',
                    ]
                ]
            ]);

        $this->assertEquals(1, $response->json('data.stats.total_referrals'));
        $this->assertEquals(300.00, $response->json('data.stats.level_1_earnings'));
    }

    public function test_user_can_get_referral_tree()
    {
        $user = User::factory()->create();
        $level1Referee = User::factory()->create(['name' => 'Level 1 User']);
        $level2Referee = User::factory()->create(['name' => 'Level 2 User']);

        Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => $level1Referee->id,
            'level' => 1,
            'bonus_percentage' => 30.00,
            'total_earned' => 300.00,
        ]);

        Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => $level2Referee->id,
            'level' => 2,
            'bonus_percentage' => 5.00,
            'total_earned' => 50.00,
        ]);

        $response = $this->actingAs($user)->getJson('/api/referrals/tree');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'level_1' => [
                        '*' => [
                            'id',
                            'name',
                            'email',
                            'joined_at',
                            'level',
                            'total_earned',
                            'bonus_percentage',
                        ]
                    ],
                    'level_2' => [
                        '*' => [
                            'id',
                            'name',
                            'email',
                            'joined_at',
                            'level',
                            'total_earned',
                            'bonus_percentage',
                        ]
                    ]
                ]
            ]);

        $level1Data = $response->json('data.level_1');
        $level2Data = $response->json('data.level_2');

        $this->assertCount(1, $level1Data);
        $this->assertCount(1, $level2Data);
        $this->assertEquals('Level 1 User', $level1Data[0]['name']);
        $this->assertEquals('Level 2 User', $level2Data[0]['name']);
    }

    public function test_user_can_get_earnings_history()
    {
        $user = User::factory()->create();
        $referral = Referral::factory()->create(['referrer_id' => $user->id]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'referral_bonus',
            'amount' => 300.00,
            'reference_id' => $referral->id,
        ]);

        $response = $this->actingAs($user)->getJson('/api/referrals/earnings');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'type',
                            'amount',
                            'description',
                            'created_at',
                        ]
                    ],
                    'current_page',
                    'per_page',
                    'total',
                ]
            ]);

        $transactions = $response->json('data.data');
        $this->assertCount(1, $transactions);
        $this->assertEquals('referral_bonus', $transactions[0]['type']);
        $this->assertEquals(300.00, $transactions[0]['amount']);
    }

    public function test_user_can_get_bonus_rates()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)->getJson('/api/referrals/bonus-rates');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'level_1' => [
                        'percentage' => 30.00,
                        'description' => 'Direct referral bonus'
                    ],
                    'level_2' => [
                        'percentage' => 5.00,
                        'description' => 'Second level referral bonus'
                    ]
                ]
            ]);
    }

    public function test_user_can_generate_new_referral_code()
    {
        $user = User::factory()->create(['referral_code' => 'OLDCODE123']);

        $response = $this->actingAs($user)->postJson('/api/referrals/generate-code');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'referral_code',
                    'referral_link',
                ]
            ]);

        $newCode = $response->json('data.referral_code');
        $this->assertNotEquals('OLDCODE123', $newCode);
        $this->assertStringStartsWith('GF', $newCode);

        // Verify user's code was updated
        $this->assertEquals($newCode, $user->fresh()->referral_code);
    }

    public function test_can_validate_referral_code()
    {
        $referrer = User::factory()->create([
            'name' => 'John Doe',
            'referral_code' => 'VALIDCODE1'
        ]);

        $response = $this->postJson('/api/referrals/validate', [
            'referral_code' => 'VALIDCODE1'
        ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'referrer_name' => 'John Doe',
                    'referrer_id' => $referrer->id,
                ]
            ]);
    }

    public function test_validation_fails_for_invalid_referral_code()
    {
        $response = $this->postJson('/api/referrals/validate', [
            'referral_code' => 'INVALIDCODE'
        ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['referral_code']);
    }

    public function test_earnings_history_can_be_filtered_by_date()
    {
        $user = User::factory()->create();
        $referral = Referral::factory()->create(['referrer_id' => $user->id]);

        // Create transactions on different dates
        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'referral_bonus',
            'amount' => 300.00,
            'reference_id' => $referral->id,
            'created_at' => '2024-01-01',
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'referral_bonus',
            'amount' => 150.00,
            'reference_id' => $referral->id,
            'created_at' => '2024-01-15',
        ]);

        $response = $this->actingAs($user)->getJson('/api/referrals/earnings?date_from=2024-01-10');

        $response->assertStatus(200);
        $transactions = $response->json('data.data');
        $this->assertCount(1, $transactions);
        $this->assertEquals(150.00, $transactions[0]['amount']);
    }

    public function test_user_can_get_referral_analytics()
    {
        $user = User::factory()->create();
        $referee = User::factory()->create();

        // Create referral with some earnings
        $referral = Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => $referee->id,
            'level' => 1,
            'bonus_percentage' => 30.00,
            'total_earned' => 300.00,
        ]);

        // Create referral bonus transaction
        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'referral_bonus',
            'amount' => 300.00,
            'reference_id' => $referral->id,
        ]);

        $response = $this->actingAs($user)->getJson('/api/referrals/analytics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'monthly_stats',
                    'top_referrals',
                    'recent_activities',
                ]
            ]);

        $data = $response->json('data');
        $this->assertIsArray($data['monthly_stats']);
        $this->assertIsArray($data['top_referrals']);
        $this->assertIsArray($data['recent_activities']);
    }

    public function test_user_can_get_performance_summary()
    {
        $user = User::factory()->create();
        $referee = User::factory()->create();

        Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => $referee->id,
            'level' => 1,
            'bonus_percentage' => 30.00,
            'total_earned' => 300.00,
        ]);

        $response = $this->actingAs($user)->getJson('/api/referrals/performance');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'total_referrals',
                    'active_referrals',
                    'conversion_rate',
                    'total_earnings',
                    'this_month_earnings',
                    'last_month_earnings',
                    'growth_rate',
                    'avg_earnings_per_referral',
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals(1, $data['total_referrals']);
        $this->assertEquals(300.00, $data['total_earnings']);
        $this->assertEquals(300.00, $data['avg_earnings_per_referral']);
    }

    public function test_unauthenticated_user_cannot_access_referral_endpoints()
    {
        $response = $this->getJson('/api/referrals/stats');
        $response->assertStatus(401);

        $response = $this->getJson('/api/referrals/tree');
        $response->assertStatus(401);

        $response = $this->getJson('/api/referrals/earnings');
        $response->assertStatus(401);

        $response = $this->getJson('/api/referrals/analytics');
        $response->assertStatus(401);

        $response = $this->getJson('/api/referrals/performance');
        $response->assertStatus(401);

        $response = $this->postJson('/api/referrals/generate-code');
        $response->assertStatus(401);
    }
}
