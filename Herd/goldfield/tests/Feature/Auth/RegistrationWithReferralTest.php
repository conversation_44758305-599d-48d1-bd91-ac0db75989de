<?php

namespace Tests\Feature\Auth;

use Tests\TestCase;
use App\Models\User;
use App\Models\Referral;
use App\Models\Transaction;
use Illuminate\Foundation\Testing\RefreshDatabase;

class RegistrationWithReferralTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_register_with_referral_code()
    {
        $referrer = User::factory()->create([
            'referral_code' => '**********'
        ]);

        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
            'referral_code' => '**********',
        ]);

        $response->assertRedirect('/dashboard');

        // Check user was created with referrer
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertEquals($referrer->id, $user->referred_by);
        $this->assertEquals(1500.00, $user->balance); // Welcome bonus
        $this->assertTrue($user->welcome_bonus_claimed);
        $this->assertNotNull($user->referral_code);

        // Check referral relationships were created
        $level1Referral = Referral::where('referee_id', $user->id)
            ->where('level', 1)
            ->first();
        $this->assertNotNull($level1Referral);
        $this->assertEquals($referrer->id, $level1Referral->referrer_id);

        // Check welcome bonus transaction was created
        $welcomeTransaction = Transaction::where('user_id', $user->id)
            ->where('type', 'welcome_bonus')
            ->first();
        $this->assertNotNull($welcomeTransaction);
        $this->assertEquals(1500.00, $welcomeTransaction->amount);
        $this->assertEquals('completed', $welcomeTransaction->status);
    }

    public function test_user_can_register_without_referral_code()
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
        ]);

        $response->assertRedirect('/dashboard');

        // Check user was created without referrer
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertNull($user->referred_by);
        $this->assertEquals(1500.00, $user->balance); // Still gets welcome bonus
        $this->assertTrue($user->welcome_bonus_claimed);
        $this->assertNotNull($user->referral_code);

        // Check no referral relationships were created
        $referrals = Referral::where('referee_id', $user->id)->get();
        $this->assertCount(0, $referrals);

        // Check welcome bonus transaction was still created
        $welcomeTransaction = Transaction::where('user_id', $user->id)
            ->where('type', 'welcome_bonus')
            ->first();
        $this->assertNotNull($welcomeTransaction);
    }

    public function test_registration_with_invalid_referral_code_ignores_referral()
    {
        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
            'referral_code' => 'INVALIDCODE',
        ]);

        $response->assertRedirect('/dashboard');

        // Check user was created without referrer (invalid code ignored)
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertNotNull($user);
        $this->assertNull($user->referred_by);

        // Check no referral relationships were created
        $referrals = Referral::where('referee_id', $user->id)->get();
        $this->assertCount(0, $referrals);
    }

    public function test_registration_creates_two_level_referral_relationships()
    {
        // Create user hierarchy: grandReferrer -> referrer
        $grandReferrer = User::factory()->create();
        $referrer = User::factory()->create([
            'referral_code' => '**********',
            'referred_by' => $grandReferrer->id,
        ]);

        $response = $this->post('/register', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
            'referral_code' => '**********',
        ]);

        $response->assertRedirect('/dashboard');

        $user = User::where('email', '<EMAIL>')->first();

        // Check level 1 referral relationship
        $level1Referral = Referral::where('referee_id', $user->id)
            ->where('level', 1)
            ->first();
        $this->assertNotNull($level1Referral);
        $this->assertEquals($referrer->id, $level1Referral->referrer_id);
        $this->assertEquals(30.00, $level1Referral->bonus_percentage);

        // Check level 2 referral relationship
        $level2Referral = Referral::where('referee_id', $user->id)
            ->where('level', 2)
            ->first();
        $this->assertNotNull($level2Referral);
        $this->assertEquals($grandReferrer->id, $level2Referral->referrer_id);
        $this->assertEquals(5.00, $level2Referral->bonus_percentage);
    }

    public function test_registration_page_shows_referrer_info_with_valid_code()
    {
        $referrer = User::factory()->create([
            'name' => 'John Doe',
            'referral_code' => '**********'
        ]);

        $response = $this->get('/register?ref=**********');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('auth/register')
                ->where('referralCode', '**********')
                ->where('referrerName', 'John Doe')
        );
    }

    public function test_registration_page_handles_invalid_referral_code()
    {
        $response = $this->get('/register?ref=INVALIDCODE');

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('auth/register')
                ->where('referralCode', 'INVALIDCODE')
                ->where('referrerName', null)
        );
    }

    public function test_user_gets_unique_referral_code_on_registration()
    {
        // Create first user
        $response1 = $this->post('/register', [
            'name' => 'User One',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
        ]);
        $response1->assertRedirect('/dashboard');

        // Logout to allow second registration
        $this->post('/logout');

        // Create second user
        $response2 = $this->post('/register', [
            'name' => 'User Two',
            'email' => '<EMAIL>',
            'password' => 'password',
            'password_confirmation' => 'password',
        ]);
        $response2->assertRedirect('/dashboard');

        $user1 = User::where('email', '<EMAIL>')->first();
        $user2 = User::where('email', '<EMAIL>')->first();

        // Check both users exist and have referral codes
        $this->assertNotNull($user1);
        $this->assertNotNull($user2);
        $this->assertNotNull($user1->referral_code);
        $this->assertNotNull($user2->referral_code);

        // Check codes are different
        $this->assertNotEquals($user1->referral_code, $user2->referral_code);

        // Check codes start with 'GF'
        $this->assertStringStartsWith('GF', $user1->referral_code);
        $this->assertStringStartsWith('GF', $user2->referral_code);
    }
}
