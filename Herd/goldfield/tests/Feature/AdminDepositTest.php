<?php

namespace Tests\Feature;

use App\Models\Admin;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class AdminDepositTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Create admin user
        $this->admin = Admin::factory()->create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
    }

    public function test_admin_can_approve_deposit()
    {
        $user = User::factory()->create(['balance' => 1000]);

        $deposit = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => 5000,
            'status' => 'pending',
            'metadata' => [
                'payment_method' => 'bank_transfer',
                'bank_name' => 'First Bank',
                'transaction_reference' => 'TXN123456',
            ],
        ]);

        $response = $this->actingAs($this->admin, 'admin')
            ->post(route('admin.deposits.approve', $deposit), [
                'admin_notes' => 'Payment verified and approved',
            ]);

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Deposit approved successfully',
        ]);

        // Check transaction is completed
        $deposit->refresh();
        $this->assertEquals('completed', $deposit->status);
        $this->assertNotNull($deposit->processed_at);

        // Check user balance is updated
        $user->refresh();
        $this->assertEquals(6000, $user->balance); // 1000 + 5000
    }

    public function test_admin_can_reject_deposit()
    {
        $user = User::factory()->create(['balance' => 1000]);

        $deposit = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => 5000,
            'status' => 'pending',
        ]);

        $response = $this->actingAs($this->admin, 'admin')
            ->post(route('admin.deposits.reject', $deposit), [
                'admin_notes' => 'Invalid payment proof',
            ]);

        $response->assertStatus(200);
        $response->assertJson([
            'message' => 'Deposit rejected successfully',
        ]);

        // Check transaction is failed
        $deposit->refresh();
        $this->assertEquals('failed', $deposit->status);
        $this->assertNotNull($deposit->processed_at);

        // Check user balance is unchanged
        $user->refresh();
        $this->assertEquals(1000, $user->balance);
    }

    public function test_admin_can_view_deposit_details()
    {
        $user = User::factory()->create();

        $deposit = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => 5000,
            'status' => 'pending',
            'metadata' => [
                'payment_method' => 'bank_transfer',
                'bank_name' => 'GTBank',
                'account_number' => '**********',
                'transaction_reference' => 'GTB123456',
            ],
        ]);

        $response = $this->actingAs($this->admin, 'admin')
            ->get(route('admin.deposits.show', $deposit));

        $response->assertStatus(200);
        $response->assertJson([
            'transaction' => [
                'id' => $deposit->id,
                'amount' => '5000.00',
                'status' => 'pending',
            ],
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
            ],
        ]);
    }

    public function test_only_pending_deposits_can_be_processed()
    {
        $user = User::factory()->create();

        $completedDeposit = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'status' => 'completed',
        ]);

        $response = $this->actingAs($this->admin, 'admin')
            ->post(route('admin.deposits.approve', $completedDeposit));

        $response->assertStatus(400);
        $response->assertJson(['message' => 'Invalid deposit transaction']);
    }

    public function test_admin_notes_are_saved_in_description()
    {
        $user = User::factory()->create();

        $deposit = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'status' => 'pending',
            'description' => 'Original description',
        ]);

        $this->actingAs($this->admin, 'admin')
            ->post(route('admin.deposits.approve', $deposit), [
                'admin_notes' => 'Verified via phone call',
            ]);

        $deposit->refresh();
        $this->assertStringContainsString('Verified via phone call', $deposit->description);
    }

    public function test_reject_requires_admin_notes()
    {
        $user = User::factory()->create();

        $deposit = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'status' => 'pending',
        ]);

        $response = $this->actingAs($this->admin, 'admin')
            ->post(route('admin.deposits.reject', $deposit), [
                // No admin_notes provided
            ]);

        $response->assertStatus(302); // Laravel redirects back on validation errors
        $response->assertSessionHasErrors('admin_notes');
    }
}
