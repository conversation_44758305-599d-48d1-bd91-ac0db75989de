<?php

namespace Tests\Feature;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TransactionApiTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_get_transaction_statistics()
    {
        $user = User::factory()->create();

        // Create various types of transactions
        Transaction::factory()->completed()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
        ]);

        Transaction::factory()->completed()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_INVESTMENT,
            'amount' => 500,
        ]);

        Transaction::factory()->pending()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 200,
        ]);

        $response = $this->actingAs($user)->getJson(route('transactions.statistics'));

        $response->assertStatus(200);
        $response->assertJson([
            'total_transactions' => 3,
            'completed_transactions' => 2,
            'pending_transactions' => 1,
            'failed_transactions' => 0,
            'total_income' => 100,
            'total_investments' => 500,
            'total_withdrawals' => 0, // Pending withdrawal doesn't count
        ]);
    }

    public function test_user_can_export_transactions_to_csv()
    {
        $user = User::factory()->create();

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
            'description' => 'Daily income',
        ]);

        $response = $this->actingAs($user)->get('/api/transactions/export');

        $response->assertStatus(200);

        // Check CSV content contains expected data
        $content = $response->getContent();
        $this->assertNotEmpty($content);
        $this->assertStringContainsString('daily_income', $content);
        $this->assertStringContainsString('100', $content);
    }

    public function test_unauthenticated_user_cannot_access_transaction_statistics()
    {
        $response = $this->getJson(route('transactions.statistics'));
        $response->assertStatus(401);

        $response = $this->get(route('transactions.export'));
        $response->assertRedirect(route('login'));
    }

    public function test_transaction_statistics_filters_by_date_range()
    {
        $user = User::factory()->create();

        // Create old transaction
        Transaction::factory()->completed()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
            'created_at' => now()->subDays(10),
        ]);

        // Create recent transaction
        Transaction::factory()->completed()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_INVESTMENT,
            'amount' => 500,
            'created_at' => now()->subDays(2),
        ]);

        $response = $this->actingAs($user)->getJson(route('transactions.statistics', [
            'start_date' => now()->subDays(5)->toDateString(),
            'end_date' => now()->toDateString(),
        ]));

        $response->assertStatus(200);
        $response->assertJson([
            'total_transactions' => 1, // Only the recent transaction
            'completed_transactions' => 1,
            'total_investments' => 500,
        ]);
    }

    public function test_transaction_statistics_includes_breakdown_by_type()
    {
        $user = User::factory()->create();

        Transaction::factory()->completed()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
        ]);

        Transaction::factory()->completed()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 150,
        ]);

        Transaction::factory()->pending()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_INVESTMENT,
            'amount' => 500,
        ]);

        $response = $this->actingAs($user)->getJson(route('transactions.statistics'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'by_type' => [
                'daily_income' => ['count', 'total_amount'],
                'investment' => ['count', 'total_amount'],
                'withdrawal' => ['count', 'total_amount'],
                'referral_bonus' => ['count', 'total_amount'],
                'gift_code' => ['count', 'total_amount'],
                'welcome_bonus' => ['count', 'total_amount'],
            ]
        ]);

        $response->assertJsonPath('by_type.daily_income.count', 2);
        $response->assertJsonPath('by_type.daily_income.total_amount', 250);
        $response->assertJsonPath('by_type.investment.count', 1);
        $response->assertJsonPath('by_type.investment.total_amount', 0); // Pending doesn't count in total_amount
    }
}
