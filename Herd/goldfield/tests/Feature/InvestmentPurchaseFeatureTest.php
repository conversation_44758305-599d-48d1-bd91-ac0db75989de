<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\InvestmentPackage;
use App\Models\Investment;
use App\Models\Transaction;

class InvestmentPurchaseFeatureTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed investment packages
        $this->artisan('db:seed', ['--class' => 'InvestmentPackageSeeder']);
    }

    public function test_user_can_view_available_investment_packages(): void
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->getJson('/api/investments/packages');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'packages' => [
                    '*' => [
                        'id',
                        'name',
                        'price',
                        'daily_income',
                        'duration_days',
                        'total_return',
                        'is_active',
                    ]
                ]
            ]);

        $this->assertEquals(7, count($response->json('packages')));
    }

    public function test_user_can_purchase_investment_package_with_sufficient_balance(): void
    {
        $user = User::factory()->create(['balance' => 10000.00]);
        $package = InvestmentPackage::where('name', 'Bronze')->first();

        $response = $this->actingAs($user)
            ->postJson('/api/investments/purchase', [
                'package_id' => $package->id,
            ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'investment' => [
                    'id',
                    'user_id',
                    'package_id',
                    'amount',
                    'daily_income',
                    'start_date',
                    'end_date',
                    'status',
                    'total_earned',
                ],
                'user_balance',
            ]);

        // Check that investment was created
        $this->assertDatabaseHas('investments', [
            'user_id' => $user->id,
            'package_id' => $package->id,
            'amount' => $package->price,
            'status' => Investment::STATUS_ACTIVE,
        ]);

        // Check that user balance was deducted
        $this->assertEquals(5000.00, $user->fresh()->balance);

        // Check that transaction was recorded
        $this->assertDatabaseHas('transactions', [
            'user_id' => $user->id,
            'type' => 'investment',
            'amount' => -$package->price,
            'status' => 'completed',
        ]);
    }

    public function test_user_cannot_purchase_investment_with_insufficient_balance(): void
    {
        $user = User::factory()->create(['balance' => 1000.00]);
        $package = InvestmentPackage::where('name', 'Bronze')->first();

        $response = $this->actingAs($user)
            ->postJson('/api/investments/purchase', [
                'package_id' => $package->id,
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['balance']);

        // Check that no investment was created
        $this->assertDatabaseMissing('investments', [
            'user_id' => $user->id,
            'package_id' => $package->id,
        ]);

        // Check that user balance was not changed
        $this->assertEquals(1000.00, $user->fresh()->balance);
    }

    public function test_user_cannot_purchase_inactive_investment_package(): void
    {
        $user = User::factory()->create(['balance' => 10000.00]);
        $package = InvestmentPackage::where('name', 'Bronze')->first();
        $package->update(['is_active' => false]);

        $response = $this->actingAs($user)
            ->postJson('/api/investments/purchase', [
                'package_id' => $package->id,
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['package_id']);

        // Check that no investment was created
        $this->assertDatabaseMissing('investments', [
            'user_id' => $user->id,
            'package_id' => $package->id,
        ]);
    }

    public function test_user_cannot_purchase_nonexistent_investment_package(): void
    {
        $user = User::factory()->create(['balance' => 10000.00]);

        $response = $this->actingAs($user)
            ->postJson('/api/investments/purchase', [
                'package_id' => 999,
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['package_id']);
    }

    public function test_user_can_view_their_investments(): void
    {
        $user = User::factory()->create();
        $package = InvestmentPackage::where('name', 'Bronze')->first();

        $investment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
        ]);

        $response = $this->actingAs($user)
            ->getJson('/api/investments');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'investments' => [
                    '*' => [
                        'id',
                        'user_id',
                        'package_id',
                        'amount',
                        'daily_income',
                        'start_date',
                        'end_date',
                        'status',
                        'total_earned',
                        'package',
                    ]
                ]
            ]);
    }

    public function test_user_can_view_specific_investment_details(): void
    {
        $user = User::factory()->create();
        $package = InvestmentPackage::where('name', 'Bronze')->first();

        $investment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
        ]);

        $response = $this->actingAs($user)
            ->getJson("/api/investments/{$investment->id}");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'investment',
                'remaining_days',
                'progress_percentage',
                'expected_total_return',
            ]);
    }

    public function test_user_cannot_view_other_users_investments(): void
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        $package = InvestmentPackage::where('name', 'Bronze')->first();

        $investment = Investment::factory()->create([
            'user_id' => $user2->id,
            'package_id' => $package->id,
        ]);

        $response = $this->actingAs($user1)
            ->getJson("/api/investments/{$investment->id}");

        $response->assertStatus(403);
    }

    public function test_user_can_view_investment_statistics(): void
    {
        $user = User::factory()->create();
        $package = InvestmentPackage::where('name', 'Bronze')->first();

        Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'status' => Investment::STATUS_ACTIVE,
            'amount' => 5000.00,
            'total_earned' => 1000.00,
        ]);

        Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'status' => Investment::STATUS_COMPLETED,
            'amount' => 5000.00,
            'total_earned' => 15000.00,
        ]);

        $response = $this->actingAs($user)
            ->getJson('/api/investments/statistics');

        $response->assertStatus(200)
            ->assertJson([
                'active_investments' => 1,
                'total_invested' => 10000.00,
                'total_earned' => 16000.00,
                'completed_investments' => 1,
            ]);
    }
}
