<?php

namespace Tests\Feature;

use App\Models\Transaction;
use App\Models\User;
use App\Models\Investment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TransactionControllerTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_view_their_transactions()
    {
        $user = User::factory()->create();
        $otherUser = User::factory()->create();

        // Create transactions for the authenticated user
        $userTransaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
        ]);

        // Create transaction for another user (should not be visible)
        Transaction::factory()->create([
            'user_id' => $otherUser->id,
            'type' => Transaction::TYPE_INVESTMENT,
            'amount' => 500,
        ]);

        $response = $this->actingAs($user)->get(route('transactions.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('Transactions/Index')
                ->has('transactions.data', 1)
                ->where('transactions.data.0.id', $userTransaction->id)
        );
    }

    public function test_user_can_filter_transactions_by_type()
    {
        $user = User::factory()->create();

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_INVESTMENT,
        ]);

        $response = $this->actingAs($user)->get(route('transactions.index', [
            'type' => Transaction::TYPE_DAILY_INCOME
        ]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('Transactions/Index')
                ->has('transactions.data', 1)
                ->where('transactions.data.0.type', Transaction::TYPE_DAILY_INCOME)
        );
    }

    public function test_user_can_filter_transactions_by_status()
    {
        $user = User::factory()->create();

        Transaction::factory()->create([
            'user_id' => $user->id,
            'status' => Transaction::STATUS_COMPLETED,
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'status' => Transaction::STATUS_PENDING,
        ]);

        $response = $this->actingAs($user)->get(route('transactions.index', [
            'status' => Transaction::STATUS_COMPLETED
        ]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('Transactions/Index')
                ->has('transactions.data', 1)
                ->where('transactions.data.0.status', Transaction::STATUS_COMPLETED)
        );
    }

    public function test_user_can_filter_transactions_by_date_range()
    {
        $user = User::factory()->create();

        $oldTransaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'created_at' => now()->subDays(10),
        ]);

        $recentTransaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'created_at' => now()->subDays(2),
        ]);

        $response = $this->actingAs($user)->get(route('transactions.index', [
            'start_date' => now()->subDays(5)->toDateString(),
            'end_date' => now()->toDateString(),
        ]));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('Transactions/Index')
                ->has('transactions.data', 1)
                ->where('transactions.data.0.id', $recentTransaction->id)
        );
    }

    public function test_user_can_view_specific_transaction()
    {
        $user = User::factory()->create();
        $transaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
        ]);

        $response = $this->actingAs($user)->get(route('transactions.show', $transaction));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('Transactions/Show')
                ->where('transaction.id', $transaction->id)
                ->where('transaction.amount', '100.00')
        );
    }

    public function test_user_cannot_view_other_users_transaction()
    {
        $user = User::factory()->create();
        $otherUser = User::factory()->create();

        $transaction = Transaction::factory()->create([
            'user_id' => $otherUser->id,
        ]);

        $response = $this->actingAs($user)->get(route('transactions.show', $transaction));

        $response->assertStatus(403);
    }

    public function test_user_can_get_transaction_statistics()
    {
        $user = User::factory()->create();

        // Create various types of transactions
        Transaction::factory()->completed()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
        ]);

        Transaction::factory()->completed()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_INVESTMENT,
            'amount' => 500,
        ]);

        Transaction::factory()->pending()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 200,
        ]);

        $response = $this->actingAs($user)->get(route('transactions.statistics'));

        $response->assertStatus(200);
        $response->assertJson([
            'total_transactions' => 3,
            'completed_transactions' => 2,
            'pending_transactions' => 1,
            'failed_transactions' => 0,
            'total_income' => 100,
            'total_investments' => 500,
            'total_withdrawals' => 0, // Pending withdrawal doesn't count
        ]);
    }

    public function test_user_can_export_transactions_to_csv()
    {
        $user = User::factory()->create();

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
            'description' => 'Daily income',
        ]);

        $response = $this->actingAs($user)->get(route('transactions.export'));

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
        $response->assertHeader('content-disposition', function ($value) {
            return str_contains($value, 'attachment; filename="transactions_');
        });

        // Check CSV content contains headers and data
        $content = $response->getContent();
        $this->assertStringContainsString('ID,Date,Type,Amount,Description,Status,Reference ID', $content);
        $this->assertStringContainsString('daily_income', $content);
        $this->assertStringContainsString('100', $content);
        $this->assertStringContainsString('Daily income', $content);
    }

    public function test_unauthenticated_user_cannot_access_transactions()
    {
        $response = $this->get(route('transactions.index'));
        $response->assertRedirect(route('login'));

        $response = $this->get(route('transactions.statistics'));
        $response->assertRedirect(route('login'));

        $response = $this->get(route('transactions.export'));
        $response->assertRedirect(route('login'));
    }

    public function test_transactions_are_paginated()
    {
        $user = User::factory()->create();

        // Create 25 transactions (more than the default 20 per page)
        Transaction::factory()->count(25)->create(['user_id' => $user->id]);

        $response = $this->actingAs($user)->get(route('transactions.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('Transactions/Index')
                ->has('transactions.data', 20) // Should only show 20 per page
                ->has('transactions.links') // Should have pagination links
        );
    }

    public function test_transactions_are_ordered_by_most_recent_first()
    {
        $user = User::factory()->create();

        $oldTransaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'created_at' => now()->subDays(5),
        ]);

        $newTransaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'created_at' => now()->subDays(1),
        ]);

        $response = $this->actingAs($user)->get(route('transactions.index'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('Transactions/Index')
                ->where('transactions.data.0.id', $newTransaction->id)
                ->where('transactions.data.1.id', $oldTransaction->id)
        );
    }
}
