<?php

namespace Tests\Feature;

use App\Models\Investment;
use App\Models\InvestmentPackage;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class ProcessDailyEarningsTest extends TestCase
{
    use RefreshDatabase;

    public function test_command_processes_active_investments()
    {
        // Create user and investment package
        $user = User::factory()->create(['balance' => 1000, 'total_earnings' => 0]);
        $package = InvestmentPackage::factory()->create([
            'daily_income' => 100,
            'duration_days' => 60,
        ]);

        // Create active investment
        $investment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'daily_income' => 100,
            'status' => Investment::STATUS_ACTIVE,
            'start_date' => now()->subDays(5),
            'end_date' => now()->addDays(55),
            'total_earned' => 0,
        ]);

        // Run the command
        $this->artisan('goldfield:process-daily-earnings')
            ->expectsOutput('Starting daily earnings processing...')
            ->expectsOutput('Found 1 active investments to process.')
            ->assertExitCode(0);

        // Check that transaction was created
        $transaction = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_DAILY_INCOME)
            ->where('reference_id', $investment->id)
            ->first();

        $this->assertNotNull($transaction);
        $this->assertEquals(100, $transaction->amount);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);

        // Check that user balance and earnings were updated
        $user->refresh();
        $this->assertEquals(1100, $user->balance);
        $this->assertEquals(100, $user->total_earnings);

        // Check that investment total earned was updated
        $investment->refresh();
        $this->assertEquals(100, $investment->total_earned);
    }

    public function test_command_skips_already_processed_investments()
    {
        $user = User::factory()->create(['balance' => 1000, 'total_earnings' => 0]);
        $package = InvestmentPackage::factory()->create(['daily_income' => 100]);

        $investment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'daily_income' => 100,
            'status' => Investment::STATUS_ACTIVE,
            'start_date' => now()->subDays(5),
            'end_date' => now()->addDays(55),
        ]);

        // Create existing transaction for today
        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'reference_id' => $investment->id,
            'created_at' => now(),
        ]);

        $this->artisan('goldfield:process-daily-earnings')
            ->expectsOutput('Starting daily earnings processing...')
            ->assertExitCode(0);

        // Should only have the one transaction we created
        $transactionCount = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_DAILY_INCOME)
            ->where('reference_id', $investment->id)
            ->count();

        $this->assertEquals(1, $transactionCount);

        // User balance should not have changed
        $user->refresh();
        $this->assertEquals(1000, $user->balance);
        $this->assertEquals(0, $user->total_earnings);
    }

    public function test_command_marks_expired_investments_as_completed()
    {
        $user = User::factory()->create();
        $package = InvestmentPackage::factory()->create(['daily_income' => 100]);

        $investment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'daily_income' => 100,
            'status' => Investment::STATUS_ACTIVE,
            'start_date' => now()->subDays(65),
            'end_date' => now()->subDays(5), // Expired 5 days ago
        ]);

        $this->artisan('goldfield:process-daily-earnings')
            ->assertExitCode(0);

        // Investment should be marked as completed
        $investment->refresh();
        $this->assertEquals(Investment::STATUS_COMPLETED, $investment->status);

        // No transaction should be created for expired investment
        $transactionCount = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_DAILY_INCOME)
            ->where('reference_id', $investment->id)
            ->whereDate('created_at', now()->toDateString())
            ->count();

        $this->assertEquals(0, $transactionCount);
    }

    public function test_command_skips_inactive_investments()
    {
        $user = User::factory()->create(['balance' => 1000]);
        $package = InvestmentPackage::factory()->create(['daily_income' => 100]);

        $investment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'daily_income' => 100,
            'status' => Investment::STATUS_COMPLETED, // Not active
            'start_date' => now()->subDays(5),
            'end_date' => now()->addDays(55),
        ]);

        $this->artisan('goldfield:process-daily-earnings')
            ->expectsOutput('No active investments found.')
            ->assertExitCode(0);

        // No transaction should be created
        $transactionCount = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_DAILY_INCOME)
            ->count();

        $this->assertEquals(0, $transactionCount);

        // User balance should not change
        $user->refresh();
        $this->assertEquals(1000, $user->balance);
    }

    public function test_command_handles_multiple_investments()
    {
        $user1 = User::factory()->create(['balance' => 1000, 'total_earnings' => 0]);
        $user2 = User::factory()->create(['balance' => 2000, 'total_earnings' => 0]);

        $package1 = InvestmentPackage::factory()->create(['daily_income' => 100]);
        $package2 = InvestmentPackage::factory()->create(['daily_income' => 200]);

        $investment1 = Investment::factory()->create([
            'user_id' => $user1->id,
            'package_id' => $package1->id,
            'daily_income' => 100,
            'status' => Investment::STATUS_ACTIVE,
            'start_date' => now()->subDays(5),
            'end_date' => now()->addDays(55),
            'total_earned' => 0,
        ]);

        $investment2 = Investment::factory()->create([
            'user_id' => $user2->id,
            'package_id' => $package2->id,
            'daily_income' => 200,
            'status' => Investment::STATUS_ACTIVE,
            'start_date' => now()->subDays(3),
            'end_date' => now()->addDays(57),
            'total_earned' => 0,
        ]);

        $this->artisan('goldfield:process-daily-earnings')
            ->expectsOutput('Found 2 active investments to process.')
            ->assertExitCode(0);

        // Check both users received their earnings
        $user1->refresh();
        $user2->refresh();

        $this->assertEquals(1100, $user1->balance);
        $this->assertEquals(100, $user1->total_earnings);

        $this->assertEquals(2200, $user2->balance);
        $this->assertEquals(200, $user2->total_earnings);

        // Check both investments were updated
        $investment1->refresh();
        $investment2->refresh();

        $this->assertEquals(100, $investment1->total_earned);
        $this->assertEquals(200, $investment2->total_earned);
    }

    public function test_dry_run_mode_does_not_make_changes()
    {
        $user = User::factory()->create(['balance' => 1000, 'total_earnings' => 0]);
        $package = InvestmentPackage::factory()->create(['daily_income' => 100]);

        $investment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'daily_income' => 100,
            'status' => Investment::STATUS_ACTIVE,
            'start_date' => now()->subDays(5),
            'end_date' => now()->addDays(55),
            'total_earned' => 0,
        ]);

        $this->artisan('goldfield:process-daily-earnings', ['--dry-run' => true])
            ->expectsOutput('Running in dry-run mode. No changes will be made.')
            ->assertExitCode(0);

        // No transaction should be created
        $transactionCount = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_DAILY_INCOME)
            ->count();

        $this->assertEquals(0, $transactionCount);

        // User balance should not change
        $user->refresh();
        $this->assertEquals(1000, $user->balance);
        $this->assertEquals(0, $user->total_earnings);

        // Investment total earned should not change
        $investment->refresh();
        $this->assertEquals(0, $investment->total_earned);
    }

    public function test_command_creates_proper_transaction_description()
    {
        $user = User::factory()->create();
        $package = InvestmentPackage::factory()->create([
            'name' => 'Gold Package',
            'daily_income' => 150,
        ]);

        $investment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'daily_income' => 150,
            'status' => Investment::STATUS_ACTIVE,
            'start_date' => now()->subDays(5),
            'end_date' => now()->addDays(55),
        ]);

        $this->artisan('goldfield:process-daily-earnings')
            ->assertExitCode(0);

        $transaction = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_DAILY_INCOME)
            ->first();

        $this->assertNotNull($transaction);
        $this->assertEquals('Daily income from Gold Package investment', $transaction->description);
        $this->assertEquals($investment->id, $transaction->reference_id);
    }
}
