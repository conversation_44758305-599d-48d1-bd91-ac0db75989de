<?php

namespace Tests\Feature;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class DepositSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    /** @test */
    public function user_can_view_deposit_form()
    {
        $user = User::factory()->create();

        $response = $this->actingAs($user)
            ->get(route('deposits.create'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) => $page->component('Actions/Deposit'));
    }

    /** @test */
    public function user_can_submit_deposit_request()
    {
        $user = User::factory()->create();

        $depositData = [
            'amount' => 5000,
            'payment_method' => 'bank_transfer',
            'bank_name' => 'First Bank of Nigeria',
            'account_number' => '**********',
            'account_name' => '<PERSON>',
            'transaction_reference' => 'TXN123456789',
        ];

        $response = $this->actingAs($user)
            ->post(route('deposits.store'), $depositData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('transactions', [
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => 5000,
            'status' => 'pending',
        ]);

        $transaction = Transaction::where('user_id', $user->id)->first();
        $this->assertStringStartsWith('DEP', $transaction->reference);
        $this->assertEquals('bank_transfer', $transaction->metadata['payment_method']);
        $this->assertEquals('First Bank of Nigeria', $transaction->metadata['bank_name']);
    }

    /** @test */
    public function user_can_submit_deposit_with_proof_of_payment()
    {
        $user = User::factory()->create();
        $file = UploadedFile::fake()->image('receipt.jpg');

        $depositData = [
            'amount' => 10000,
            'payment_method' => 'bank_transfer',
            'bank_name' => 'GTBank',
            'account_number' => '**********',
            'account_name' => 'Jane Doe',
            'transaction_reference' => 'GTB987654321',
            'proof_of_payment' => $file,
        ];

        $response = $this->actingAs($user)
            ->post(route('deposits.store'), $depositData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $transaction = Transaction::where('user_id', $user->id)->first();
        $this->assertNotNull($transaction->metadata['proof_of_payment']);
        Storage::disk('public')->assertExists($transaction->metadata['proof_of_payment']);
    }

    /** @test */
    public function deposit_validation_works()
    {
        $user = User::factory()->create();

        // Test minimum amount validation
        $response = $this->actingAs($user)
            ->post(route('deposits.store'), [
                'amount' => 500, // Below minimum
                'payment_method' => 'bank_transfer',
                'transaction_reference' => 'TXN123',
            ]);

        $response->assertSessionHasErrors('amount');

        // Test required fields for bank transfer
        $response = $this->actingAs($user)
            ->post(route('deposits.store'), [
                'amount' => 5000,
                'payment_method' => 'bank_transfer',
                'transaction_reference' => 'TXN123',
                // Missing bank_name, account_number, account_name
            ]);

        $response->assertSessionHasErrors(['bank_name', 'account_number', 'account_name']);
    }

    /** @test */
    public function user_can_view_deposit_history()
    {
        $user = User::factory()->create();

        // Create some deposit transactions
        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => 5000,
            'status' => 'completed',
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => 10000,
            'status' => 'pending',
        ]);

        $response = $this->actingAs($user)
            ->get(route('deposits.history'));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('Actions/DepositHistory')
                ->has('deposits.data', 2)
        );
    }

    /** @test */
    public function user_can_filter_deposit_history()
    {
        $user = User::factory()->create();

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'status' => 'completed',
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'status' => 'pending',
        ]);

        $response = $this->actingAs($user)
            ->get(route('deposits.history', ['status' => 'completed']));

        $response->assertStatus(200);
        $response->assertInertia(fn ($page) =>
            $page->component('Actions/DepositHistory')
                ->has('deposits.data', 1)
        );
    }

    /** @test */
    public function user_can_export_deposit_history()
    {
        $user = User::factory()->create();

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => 'deposit',
            'amount' => 5000,
        ]);

        $response = $this->actingAs($user)
            ->get(route('deposits.history', ['export' => true]));

        $response->assertStatus(200);
        $response->assertHeader('content-type', 'text/csv; charset=UTF-8');
        $this->assertStringContainsString('deposits_', $response->headers->get('content-disposition'));
    }
}
