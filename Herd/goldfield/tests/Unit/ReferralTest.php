<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\Referral;
use App\Models\Transaction;
use App\Models\Investment;
use Illuminate\Foundation\Testing\RefreshDatabase;

class ReferralTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_can_generate_referral_code()
    {
        $user = User::factory()->create();

        $code = $user->generateReferralCode();

        $this->assertNotNull($code);
        $this->assertStringStartsWith('GF', $code);
        $this->assertEquals(10, strlen($code)); // GF + 8 characters
        $this->assertEquals($code, $user->fresh()->referral_code);
    }

    public function test_user_can_get_existing_referral_code()
    {
        $user = User::factory()->create(['referral_code' => 'GF12345678']);

        $code = $user->getReferralCode();

        $this->assertEquals('GF12345678', $code);
    }

    public function test_user_can_be_found_by_referral_code()
    {
        $user = User::factory()->create(['referral_code' => 'GF12345678']);

        $foundUser = User::findByReferralCode('GF12345678');

        $this->assertEquals($user->id, $foundUser->id);
    }

    public function test_referral_code_is_unique()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();

        $code1 = $user1->generateReferralCode();
        $code2 = $user2->generateReferralCode();

        $this->assertNotEquals($code1, $code2);
    }

    public function test_bonus_percentage_calculation()
    {
        $this->assertEquals(30.00, Referral::getBonusPercentage(1));
        $this->assertEquals(5.00, Referral::getBonusPercentage(2));
        $this->assertEquals(0.00, Referral::getBonusPercentage(3));
    }

    public function test_bonus_amount_calculation()
    {
        $investmentAmount = 1000.00;

        $level1Bonus = Referral::calculateBonus($investmentAmount, 1);
        $level2Bonus = Referral::calculateBonus($investmentAmount, 2);

        $this->assertEquals(300.00, $level1Bonus);
        $this->assertEquals(50.00, $level2Bonus);
    }

    public function test_referral_relationships_creation()
    {
        $grandReferrer = User::factory()->create();
        $referrer = User::factory()->create(['referred_by' => $grandReferrer->id]);
        $referee = User::factory()->create();

        Referral::createReferralRelationships($referee, $referrer);

        // Check level 1 referral
        $level1Referral = Referral::where('referee_id', $referee->id)
            ->where('level', 1)
            ->first();

        $this->assertNotNull($level1Referral);
        $this->assertEquals($referrer->id, $level1Referral->referrer_id);
        $this->assertEquals(30.00, $level1Referral->bonus_percentage);

        // Check level 2 referral
        $level2Referral = Referral::where('referee_id', $referee->id)
            ->where('level', 2)
            ->first();

        $this->assertNotNull($level2Referral);
        $this->assertEquals($grandReferrer->id, $level2Referral->referrer_id);
        $this->assertEquals(5.00, $level2Referral->bonus_percentage);
    }

    public function test_referral_relationships_creation_without_grand_referrer()
    {
        $referrer = User::factory()->create();
        $referee = User::factory()->create();

        Referral::createReferralRelationships($referee, $referrer);

        // Check only level 1 referral exists
        $referrals = Referral::where('referee_id', $referee->id)->get();

        $this->assertCount(1, $referrals);
        $this->assertEquals(1, $referrals->first()->level);
    }

    public function test_referral_bonus_distribution()
    {
        $grandReferrer = User::factory()->create(['balance' => 0, 'total_earnings' => 0]);
        $referrer = User::factory()->create(['balance' => 0, 'total_earnings' => 0, 'referred_by' => $grandReferrer->id]);
        $investor = User::factory()->create(['referred_by' => $referrer->id]);

        // Create referral relationships
        Referral::createReferralRelationships($investor, $referrer);

        $investmentAmount = 1000.00;

        Referral::distributeReferralBonuses($investor, $investmentAmount);

        // Check referrer received level 1 bonus
        $referrer->refresh();
        $this->assertEquals(300.00, $referrer->balance);
        $this->assertEquals(300.00, $referrer->total_earnings);

        // Check grand referrer received level 2 bonus
        $grandReferrer->refresh();
        $this->assertEquals(50.00, $grandReferrer->balance);
        $this->assertEquals(50.00, $grandReferrer->total_earnings);

        // Check referral records updated
        $level1Referral = Referral::where('referee_id', $investor->id)
            ->where('level', 1)
            ->first();
        $this->assertEquals(300.00, $level1Referral->total_earned);

        $level2Referral = Referral::where('referee_id', $investor->id)
            ->where('level', 2)
            ->first();
        $this->assertEquals(50.00, $level2Referral->total_earned);

        // Check transactions created
        $referrerTransaction = Transaction::where('user_id', $referrer->id)
            ->where('type', 'referral_bonus')
            ->first();
        $this->assertNotNull($referrerTransaction);
        $this->assertEquals(300.00, $referrerTransaction->amount);

        $grandReferrerTransaction = Transaction::where('user_id', $grandReferrer->id)
            ->where('type', 'referral_bonus')
            ->first();
        $this->assertNotNull($grandReferrerTransaction);
        $this->assertEquals(50.00, $grandReferrerTransaction->amount);
    }

    public function test_user_referral_statistics()
    {
        $user = User::factory()->create();
        $referee1 = User::factory()->create();
        $referee2 = User::factory()->create();
        $grandReferee = User::factory()->create();

        // Create referral relationships
        Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => $referee1->id,
            'level' => 1,
            'bonus_percentage' => 30.00,
            'total_earned' => 300.00,
        ]);

        Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => $referee2->id,
            'level' => 1,
            'bonus_percentage' => 30.00,
            'total_earned' => 150.00,
        ]);

        Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => $grandReferee->id,
            'level' => 2,
            'bonus_percentage' => 5.00,
            'total_earned' => 50.00,
        ]);

        $stats = $user->getReferralStats();

        $this->assertEquals(3, $stats['total_referrals']);
        $this->assertEquals(2, $stats['level_1_count']);
        $this->assertEquals(1, $stats['level_2_count']);
        $this->assertEquals(450.00, $stats['level_1_earnings']);
        $this->assertEquals(50.00, $stats['level_2_earnings']);
        $this->assertEquals(500.00, $stats['total_earnings']);
    }

    public function test_user_total_referral_earnings()
    {
        $user = User::factory()->create();

        Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => User::factory()->create()->id,
            'level' => 1,
            'bonus_percentage' => 30.00,
            'total_earned' => 300.00,
        ]);

        Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => User::factory()->create()->id,
            'level' => 2,
            'bonus_percentage' => 5.00,
            'total_earned' => 50.00,
        ]);

        $totalEarnings = $user->getTotalReferralEarnings();

        $this->assertEquals(350.00, $totalEarnings);
    }

    public function test_user_referral_link_generation()
    {
        $user = User::factory()->create(['referral_code' => 'GF12345678']);

        $link = $user->getReferralLink();

        $this->assertEquals(url('/register?ref=GF12345678'), $link);
    }

    public function test_user_referral_conversion_rate()
    {
        $user = User::factory()->create();

        // Create referrals - one with investment, one without
        $activeReferee = User::factory()->create();
        $inactiveReferee = User::factory()->create();

        Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => $activeReferee->id,
            'level' => 1,
            'bonus_percentage' => 30.00,
        ]);

        Referral::create([
            'referrer_id' => $user->id,
            'referee_id' => $inactiveReferee->id,
            'level' => 1,
            'bonus_percentage' => 30.00,
        ]);

        // Create investment for active referee
        Investment::factory()->create([
            'user_id' => $activeReferee->id,
            'status' => 'active',
        ]);

        $conversionRate = $user->getReferralConversionRate();

        $this->assertEquals(50.0, $conversionRate); // 1 out of 2 = 50%
    }

    public function test_user_referral_conversion_rate_with_no_referrals()
    {
        $user = User::factory()->create();

        $conversionRate = $user->getReferralConversionRate();

        $this->assertEquals(0.0, $conversionRate);
    }
}
