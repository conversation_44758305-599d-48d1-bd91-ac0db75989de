<?php

namespace Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\DailyGiftCode;
use App\Models\GiftCodeClaim;
use App\Models\User;
use Carbon\Carbon;

class DailyGiftCodeTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Carbon::setTestNow('2025-07-25 18:00:00');
    }

    protected function tearDown(): void
    {
        Carbon::setTestNow();
        parent::tearDown();
    }

    public function test_gift_code_time_constant()
    {
        $this->assertEquals('17:30:00', DailyGiftCode::GIFT_CODE_TIME);
    }

    public function test_is_claimable_returns_false_when_inactive()
    {
        $giftCode = DailyGiftCode::factory()->create([
            'valid_date' => Carbon::today(),
            'is_active' => false,
        ]);

        $this->assertFalse($giftCode->isClaimable());
    }

    public function test_is_claimable_returns_true_when_conditions_met()
    {
        $giftCode = DailyGiftCode::factory()->create([
            'valid_date' => Carbon::today(),
            'is_active' => true,
        ]);

        Carbon::setTestNow('2025-07-25 17:30:00'); // Exactly 5:30 PM
        $this->assertTrue($giftCode->isClaimable());

        Carbon::setTestNow('2025-07-25 20:00:00'); // Later in the day
        $this->assertTrue($giftCode->isClaimable());
    }

    public function test_is_claimable_returns_false_before_gift_time()
    {
        $giftCode = DailyGiftCode::factory()->create([
            'valid_date' => Carbon::today(),
            'is_active' => true,
        ]);

        Carbon::setTestNow('2025-07-25 17:29:59'); // Just before 5:30 PM
        $this->assertFalse($giftCode->isClaimable());
    }

    public function test_is_claimable_returns_false_on_different_day()
    {
        $giftCode = DailyGiftCode::factory()->create([
            'valid_date' => Carbon::yesterday(),
            'is_active' => true,
        ]);

        $this->assertFalse($giftCode->isClaimable());
    }

    public function test_has_been_claimed_by_user_returns_correct_value()
    {
        $user = User::factory()->create();
        $giftCode = DailyGiftCode::factory()->create();

        $this->assertFalse($giftCode->hasBeenClaimedByUser($user->id));

        GiftCodeClaim::factory()->create([
            'user_id' => $user->id,
            'gift_code_id' => $giftCode->id,
        ]);

        $this->assertTrue($giftCode->hasBeenClaimedByUser($user->id));
    }

    public function test_has_reached_limit_returns_correct_value()
    {
        $giftCode = DailyGiftCode::factory()->create([
            'usage_limit' => 10,
            'used_count' => 9,
        ]);

        $this->assertFalse($giftCode->hasReachedLimit());

        $giftCode->update(['used_count' => 10]);
        $this->assertTrue($giftCode->hasReachedLimit());

        $giftCode->update(['used_count' => 11]);
        $this->assertTrue($giftCode->hasReachedLimit());
    }

    public function test_generate_code_creates_unique_codes()
    {
        $codes = [];
        for ($i = 0; $i < 5; $i++) {
            $code = DailyGiftCode::generateCode();
            $this->assertNotContains($code, $codes);
            $this->assertTrue(str_starts_with($code, 'GOLD'));
            $this->assertEquals(12, strlen($code));
            $codes[] = $code;
        }
    }

    public function test_get_todays_code_returns_correct_code()
    {
        $this->assertNull(DailyGiftCode::getTodaysCode());

        $giftCode = DailyGiftCode::factory()->create([
            'valid_date' => Carbon::today(),
            'is_active' => true,
        ]);

        $this->assertEquals($giftCode->id, DailyGiftCode::getTodaysCode()->id);

        // Create inactive code for today - should not be returned
        DailyGiftCode::factory()->create([
            'valid_date' => Carbon::today(),
            'is_active' => false,
        ]);

        $this->assertEquals($giftCode->id, DailyGiftCode::getTodaysCode()->id);
    }

    public function test_create_todays_code_creates_new_code()
    {
        $giftCode = DailyGiftCode::createTodaysCode(150.00);

        $this->assertEquals(150.00, $giftCode->amount);
        $this->assertEquals(Carbon::today()->format('Y-m-d'), $giftCode->valid_date->format('Y-m-d'));
        $this->assertTrue($giftCode->is_active);
        $this->assertEquals(1000, $giftCode->usage_limit);
        $this->assertEquals(0, $giftCode->used_count);
        $this->assertTrue(str_starts_with($giftCode->code, 'GOLD'));
    }

    public function test_create_todays_code_returns_existing_code()
    {
        $existingCode = DailyGiftCode::factory()->create([
            'valid_date' => Carbon::today(),
            'amount' => 100.00,
        ]);

        $giftCode = DailyGiftCode::createTodaysCode(200.00);

        $this->assertEquals($existingCode->id, $giftCode->id);
        $this->assertEquals(100.00, $giftCode->amount); // Should keep original amount
    }

    public function test_claims_relationship()
    {
        $giftCode = DailyGiftCode::factory()->create();
        $user = User::factory()->create();

        $this->assertEquals(0, $giftCode->claims()->count());

        $claim = GiftCodeClaim::factory()->create([
            'gift_code_id' => $giftCode->id,
            'user_id' => $user->id,
        ]);

        $this->assertEquals(1, $giftCode->claims()->count());
        $this->assertEquals($claim->id, $giftCode->claims()->first()->id);
    }

    public function test_fillable_attributes()
    {
        $attributes = [
            'code' => 'GOLDTEST123',
            'amount' => 150.00,
            'valid_date' => Carbon::today(),
            'is_active' => true,
            'usage_limit' => 500,
            'used_count' => 10,
        ];

        $giftCode = DailyGiftCode::create($attributes);

        $this->assertEquals('GOLDTEST123', $giftCode->code);
        $this->assertEquals(150.00, $giftCode->amount);
        $this->assertEquals(Carbon::today()->format('Y-m-d'), $giftCode->valid_date->format('Y-m-d'));
        $this->assertTrue($giftCode->is_active);
        $this->assertEquals(500, $giftCode->usage_limit);
        $this->assertEquals(10, $giftCode->used_count);
    }

    public function test_casts_work_correctly()
    {
        $giftCode = DailyGiftCode::factory()->create([
            'amount' => '150.50',
            'valid_date' => '2025-07-25',
            'is_active' => '1',
        ]);

        $this->assertIsString($giftCode->amount); // Decimal cast returns string
        $this->assertEquals('150.50', $giftCode->amount);
        $this->assertInstanceOf(Carbon::class, $giftCode->valid_date);
        $this->assertIsBool($giftCode->is_active);
        $this->assertTrue($giftCode->is_active);
    }
}
