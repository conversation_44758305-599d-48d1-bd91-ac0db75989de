<?php

namespace Tests\Unit;

use App\Models\Transaction;
use App\Models\User;
use App\Models\Investment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class TransactionTest extends TestCase
{
    use RefreshDatabase;

    public function test_transaction_belongs_to_user()
    {
        $user = User::factory()->create();
        $transaction = Transaction::factory()->create(['user_id' => $user->id]);

        $this->assertInstanceOf(User::class, $transaction->user);
        $this->assertEquals($user->id, $transaction->user->id);
    }

    public function test_transaction_can_belong_to_investment()
    {
        $user = User::factory()->create();
        $investment = Investment::factory()->create(['user_id' => $user->id]);
        $transaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_INVESTMENT,
            'reference_id' => $investment->id,
        ]);

        $this->assertInstanceOf(Investment::class, $transaction->investment);
        $this->assertEquals($investment->id, $transaction->investment->id);
    }

    public function test_create_with_balance_update_for_income_transaction()
    {
        $user = User::factory()->create(['balance' => 1000, 'total_earnings' => 500]);

        $transaction = Transaction::createWithBalanceUpdate([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
            'description' => 'Daily income',
            'status' => Transaction::STATUS_COMPLETED,
        ]);

        $user->refresh();

        $this->assertEquals(1100, $user->balance);
        $this->assertEquals(600, $user->total_earnings);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
    }

    public function test_create_with_balance_update_for_investment_transaction()
    {
        $user = User::factory()->create(['balance' => 1000, 'total_earnings' => 500]);

        $transaction = Transaction::createWithBalanceUpdate([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_INVESTMENT,
            'amount' => 200,
            'description' => 'Investment purchase',
            'status' => Transaction::STATUS_COMPLETED,
        ]);

        $user->refresh();

        $this->assertEquals(800, $user->balance);
        $this->assertEquals(500, $user->total_earnings); // Should not change for investments
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
    }

    public function test_create_with_balance_update_for_withdrawal_transaction()
    {
        $user = User::factory()->create(['balance' => 1000, 'total_earnings' => 500]);

        $transaction = Transaction::createWithBalanceUpdate([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 300,
            'description' => 'Withdrawal request',
            'status' => Transaction::STATUS_COMPLETED,
        ]);

        $user->refresh();

        $this->assertEquals(700, $user->balance);
        $this->assertEquals(500, $user->total_earnings); // Should not change for withdrawals
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
    }

    public function test_pending_transaction_does_not_update_balance()
    {
        $user = User::factory()->create(['balance' => 1000, 'total_earnings' => 500]);

        $transaction = Transaction::createWithBalanceUpdate([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
            'description' => 'Daily income',
            'status' => Transaction::STATUS_PENDING,
        ]);

        $user->refresh();

        // Balance should not change for pending transactions
        $this->assertEquals(1000, $user->balance);
        $this->assertEquals(500, $user->total_earnings);
        $this->assertEquals(Transaction::STATUS_PENDING, $transaction->status);
    }

    public function test_complete_pending_transaction_updates_balance()
    {
        $user = User::factory()->create(['balance' => 1000, 'total_earnings' => 500]);

        $transaction = Transaction::create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
            'description' => 'Daily income',
            'status' => Transaction::STATUS_PENDING,
        ]);

        $transaction->complete();
        $user->refresh();

        $this->assertEquals(1100, $user->balance);
        $this->assertEquals(600, $user->total_earnings);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
    }

    public function test_fail_transaction_does_not_update_balance()
    {
        $user = User::factory()->create(['balance' => 1000, 'total_earnings' => 500]);

        $transaction = Transaction::create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
            'amount' => 100,
            'description' => 'Daily income',
            'status' => Transaction::STATUS_PENDING,
        ]);

        $transaction->fail();
        $user->refresh();

        $this->assertEquals(1000, $user->balance);
        $this->assertEquals(500, $user->total_earnings);
        $this->assertEquals(Transaction::STATUS_FAILED, $transaction->status);
    }

    public function test_of_type_scope()
    {
        $user = User::factory()->create();

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_INVESTMENT,
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_DAILY_INCOME,
        ]);

        $investmentTransactions = Transaction::ofType(Transaction::TYPE_INVESTMENT)->get();
        $incomeTransactions = Transaction::ofType(Transaction::TYPE_DAILY_INCOME)->get();

        $this->assertCount(1, $investmentTransactions);
        $this->assertCount(1, $incomeTransactions);
        $this->assertEquals(Transaction::TYPE_INVESTMENT, $investmentTransactions->first()->type);
        $this->assertEquals(Transaction::TYPE_DAILY_INCOME, $incomeTransactions->first()->type);
    }

    public function test_with_status_scope()
    {
        $user = User::factory()->create();

        Transaction::factory()->create([
            'user_id' => $user->id,
            'status' => Transaction::STATUS_COMPLETED,
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'status' => Transaction::STATUS_PENDING,
        ]);

        $completedTransactions = Transaction::withStatus(Transaction::STATUS_COMPLETED)->get();
        $pendingTransactions = Transaction::withStatus(Transaction::STATUS_PENDING)->get();

        $this->assertCount(1, $completedTransactions);
        $this->assertCount(1, $pendingTransactions);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $completedTransactions->first()->status);
        $this->assertEquals(Transaction::STATUS_PENDING, $pendingTransactions->first()->status);
    }

    public function test_date_range_scope()
    {
        $user = User::factory()->create();

        $oldTransaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'created_at' => now()->subDays(10),
        ]);

        $recentTransaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'created_at' => now()->subDays(2),
        ]);

        $startDate = now()->subDays(5);
        $endDate = now();

        $recentTransactions = Transaction::dateRange($startDate, $endDate)->get();

        $this->assertCount(1, $recentTransactions);
        $this->assertEquals($recentTransaction->id, $recentTransactions->first()->id);
    }

    public function test_get_types_returns_all_transaction_types()
    {
        $types = Transaction::getTypes();

        $expectedTypes = [
            Transaction::TYPE_INVESTMENT,
            Transaction::TYPE_WITHDRAWAL,
            Transaction::TYPE_DAILY_INCOME,
            Transaction::TYPE_REFERRAL_BONUS,
            Transaction::TYPE_GIFT_CODE,
            Transaction::TYPE_WELCOME_BONUS,
        ];

        $this->assertEquals($expectedTypes, $types);
    }

    public function test_get_statuses_returns_all_transaction_statuses()
    {
        $statuses = Transaction::getStatuses();

        $expectedStatuses = [
            Transaction::STATUS_PENDING,
            Transaction::STATUS_COMPLETED,
            Transaction::STATUS_FAILED,
        ];

        $this->assertEquals($expectedStatuses, $statuses);
    }
}
