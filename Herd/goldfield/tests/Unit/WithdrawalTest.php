<?php

namespace Tests\Unit;

use App\Http\Controllers\WithdrawalController;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class WithdrawalTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->seed();
    }

    public function test_minimum_withdrawal_constant_is_correct()
    {
        $this->assertEquals(1000, WithdrawalController::MINIMUM_WITHDRAWAL);
    }

    public function test_withdrawal_charge_percentage_is_correct()
    {
        $this->assertEquals(10, WithdrawalController::WITHDRAWAL_CHARGE_PERCENTAGE);
    }

    public function test_withdrawal_charge_calculation()
    {
        $amount = 2000;
        $expectedCharge = $amount * (WithdrawalController::WITHDRAWAL_CHARGE_PERCENTAGE / 100);
        $expectedNet = $amount - $expectedCharge;

        $this->assertEquals(200, $expectedCharge);
        $this->assertEquals(1800, $expectedNet);
    }

    public function test_withdrawal_transaction_creation_updates_balance()
    {
        $user = User::factory()->create(['balance' => 5000]);
        $initialBalance = $user->balance;
        $withdrawalAmount = 2000;

        // Simulate the withdrawal process
        $user->decrement('balance', $withdrawalAmount);

        $transaction = Transaction::create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => $withdrawalAmount,
            'description' => 'Test withdrawal',
            'status' => Transaction::STATUS_PENDING,
        ]);

        $this->assertDatabaseHas('transactions', [
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => $withdrawalAmount,
            'status' => Transaction::STATUS_PENDING,
        ]);

        $this->assertEquals($initialBalance - $withdrawalAmount, $user->fresh()->balance);
    }

    public function test_withdrawal_approval_marks_transaction_as_completed()
    {
        $user = User::factory()->create(['balance' => 3000]);

        $transaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 2000,
            'status' => Transaction::STATUS_PENDING
        ]);

        // Simulate approval
        $transaction->update(['status' => Transaction::STATUS_COMPLETED]);

        $this->assertDatabaseHas('transactions', [
            'id' => $transaction->id,
            'status' => Transaction::STATUS_COMPLETED
        ]);
    }

    public function test_withdrawal_rejection_marks_transaction_as_failed_and_refunds_balance()
    {
        $user = User::factory()->create(['balance' => 3000]);
        $initialBalance = $user->balance;

        $transaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 2000,
            'status' => Transaction::STATUS_PENDING
        ]);

        // Simulate rejection with refund
        $transaction->update(['status' => Transaction::STATUS_FAILED]);
        $user->increment('balance', $transaction->amount);

        $this->assertDatabaseHas('transactions', [
            'id' => $transaction->id,
            'status' => Transaction::STATUS_FAILED
        ]);

        $this->assertEquals($initialBalance + $transaction->amount, $user->fresh()->balance);
    }

    public function test_withdrawal_history_filtering_by_status()
    {
        $user = User::factory()->create();

        // Create transactions with different statuses
        $completedTransaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'status' => Transaction::STATUS_COMPLETED
        ]);

        $pendingTransaction = Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'status' => Transaction::STATUS_PENDING
        ]);

        // Test filtering by completed status
        $completedWithdrawals = $user->transactions()
            ->ofType(Transaction::TYPE_WITHDRAWAL)
            ->withStatus(Transaction::STATUS_COMPLETED)
            ->get();

        $this->assertCount(1, $completedWithdrawals);
        $this->assertEquals($completedTransaction->id, $completedWithdrawals->first()->id);

        // Test filtering by pending status
        $pendingWithdrawals = $user->transactions()
            ->ofType(Transaction::TYPE_WITHDRAWAL)
            ->withStatus(Transaction::STATUS_PENDING)
            ->get();

        $this->assertCount(1, $pendingWithdrawals);
        $this->assertEquals($pendingTransaction->id, $pendingWithdrawals->first()->id);
    }

    public function test_withdrawal_statistics_calculation()
    {
        $user = User::factory()->create(['balance' => 5000]);

        // Create various withdrawal transactions
        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 1000,
            'status' => Transaction::STATUS_COMPLETED
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 1500,
            'status' => Transaction::STATUS_COMPLETED
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 2000,
            'status' => Transaction::STATUS_PENDING
        ]);

        Transaction::factory()->create([
            'user_id' => $user->id,
            'type' => Transaction::TYPE_WITHDRAWAL,
            'amount' => 500,
            'status' => Transaction::STATUS_FAILED
        ]);

        // Calculate statistics
        $totalWithdrawals = $user->transactions()
            ->ofType(Transaction::TYPE_WITHDRAWAL)
            ->withStatus(Transaction::STATUS_COMPLETED)
            ->sum('amount');

        $pendingWithdrawals = $user->transactions()
            ->ofType(Transaction::TYPE_WITHDRAWAL)
            ->withStatus(Transaction::STATUS_PENDING)
            ->sum('amount');

        $failedWithdrawals = $user->transactions()
            ->ofType(Transaction::TYPE_WITHDRAWAL)
            ->withStatus(Transaction::STATUS_FAILED)
            ->sum('amount');

        $withdrawalCount = $user->transactions()
            ->ofType(Transaction::TYPE_WITHDRAWAL)
            ->count();

        $this->assertEquals(2500, $totalWithdrawals); // 1000 + 1500
        $this->assertEquals(2000, $pendingWithdrawals);
        $this->assertEquals(500, $failedWithdrawals);
        $this->assertEquals(4, $withdrawalCount);
    }

    public function test_withdrawal_amount_validation_rules()
    {
        // Test minimum withdrawal amount
        $this->assertTrue(1000 >= WithdrawalController::MINIMUM_WITHDRAWAL);
        $this->assertFalse(999 >= WithdrawalController::MINIMUM_WITHDRAWAL);

        // Test that amount must be numeric
        $this->assertTrue(is_numeric(1000));
        $this->assertTrue(is_numeric(1000.50));
        $this->assertFalse(is_numeric('abc'));
    }
}
