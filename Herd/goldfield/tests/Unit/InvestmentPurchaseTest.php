<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\Investment;
use App\Models\InvestmentPackage;
use App\Models\Transaction;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class InvestmentPurchaseTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed investment packages
        $this->artisan('db:seed', ['--class' => 'InvestmentPackageSeeder']);
    }

    public function test_investment_model_has_correct_status_constants(): void
    {
        $this->assertEquals('active', Investment::STATUS_ACTIVE);
        $this->assertEquals('completed', Investment::STATUS_COMPLETED);
        $this->assertEquals('cancelled', Investment::STATUS_CANCELLED);

        $expectedStatuses = ['active', 'completed', 'cancelled'];
        $this->assertEquals($expectedStatuses, Investment::STATUSES);
    }

    public function test_investment_can_check_if_active(): void
    {
        $investment = Investment::factory()->make(['status' => Investment::STATUS_ACTIVE]);
        $this->assertTrue($investment->isActive());

        $investment = Investment::factory()->make(['status' => Investment::STATUS_COMPLETED]);
        $this->assertFalse($investment->isActive());
    }

    public function test_investment_can_check_if_completed(): void
    {
        $investment = Investment::factory()->make(['status' => Investment::STATUS_COMPLETED]);
        $this->assertTrue($investment->isCompleted());

        $investment = Investment::factory()->make(['status' => Investment::STATUS_ACTIVE]);
        $this->assertFalse($investment->isCompleted());
    }

    public function test_investment_can_check_if_cancelled(): void
    {
        $investment = Investment::factory()->make(['status' => Investment::STATUS_CANCELLED]);
        $this->assertTrue($investment->isCancelled());

        $investment = Investment::factory()->make(['status' => Investment::STATUS_ACTIVE]);
        $this->assertFalse($investment->isCancelled());
    }

    public function test_investment_calculates_remaining_days_correctly(): void
    {
        $startDate = Carbon::today();
        $endDate = $startDate->copy()->addDays(30);

        $investment = Investment::factory()->make([
            'status' => Investment::STATUS_ACTIVE,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);

        $this->assertEquals(30, $investment->getRemainingDays());
    }

    public function test_investment_calculates_days_elapsed_correctly(): void
    {
        $startDate = Carbon::today()->subDays(10);

        $investment = Investment::factory()->make([
            'start_date' => $startDate,
        ]);

        $this->assertEquals(10, $investment->getDaysElapsed());
    }

    public function test_investment_calculates_progress_percentage(): void
    {
        $package = InvestmentPackage::factory()->make(['duration_days' => 60]);
        $startDate = Carbon::today()->subDays(30);

        $investment = Investment::factory()->make([
            'start_date' => $startDate,
        ]);
        $investment->setRelation('package', $package);

        $this->assertEquals(50.0, $investment->getProgressPercentage());
    }

    public function test_investment_calculates_expected_total_return(): void
    {
        $package = InvestmentPackage::factory()->make(['duration_days' => 60]);
        $investment = Investment::factory()->make(['daily_income' => 100.00]);
        $investment->setRelation('package', $package);

        $this->assertEquals(6000.00, $investment->getExpectedTotalReturn());
    }

    public function test_investment_can_check_if_expired(): void
    {
        $expiredInvestment = Investment::factory()->make([
            'end_date' => Carbon::yesterday(),
        ]);
        $this->assertTrue($expiredInvestment->hasExpired());

        $activeInvestment = Investment::factory()->make([
            'end_date' => Carbon::tomorrow(),
        ]);
        $this->assertFalse($activeInvestment->hasExpired());
    }

    public function test_investment_can_be_marked_as_completed(): void
    {
        $user = User::factory()->create();
        $package = InvestmentPackage::where('name', 'Bronze')->first();

        $investment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'status' => Investment::STATUS_ACTIVE,
        ]);

        $investment->markAsCompleted();

        $this->assertEquals(Investment::STATUS_COMPLETED, $investment->fresh()->status);
    }

    public function test_investment_can_be_marked_as_cancelled(): void
    {
        $user = User::factory()->create();
        $package = InvestmentPackage::where('name', 'Bronze')->first();

        $investment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'status' => Investment::STATUS_ACTIVE,
        ]);

        $investment->markAsCancelled();

        $this->assertEquals(Investment::STATUS_CANCELLED, $investment->fresh()->status);
    }

    public function test_investment_scopes_work_correctly(): void
    {
        $user = User::factory()->create();
        $package = InvestmentPackage::where('name', 'Bronze')->first();

        $activeInvestment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'status' => Investment::STATUS_ACTIVE,
        ]);

        $completedInvestment = Investment::factory()->create([
            'user_id' => $user->id,
            'package_id' => $package->id,
            'status' => Investment::STATUS_COMPLETED,
        ]);

        $this->assertEquals(1, Investment::active()->count());
        $this->assertEquals(1, Investment::completed()->count());
        $this->assertEquals(2, Investment::forUser($user->id)->count());
    }
}
