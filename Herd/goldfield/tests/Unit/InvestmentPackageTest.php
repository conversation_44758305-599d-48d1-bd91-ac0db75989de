<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\InvestmentPackage;
use Illuminate\Foundation\Testing\RefreshDatabase;

class InvestmentPackageTest extends TestCase
{
    use RefreshDatabase;

    public function test_investment_package_has_correct_fillable_attributes(): void
    {
        $package = new InvestmentPackage();
        $expected = [
            'name',
            'price',
            'daily_income',
            'duration_days',
            'total_return',
            'is_active',
        ];

        $this->assertEquals($expected, $package->getFillable());
    }

    public function test_investment_package_calculates_total_return_correctly(): void
    {
        $package = InvestmentPackage::factory()->make([
            'daily_income' => 100.00,
            'duration_days' => 30,
        ]);

        $this->assertEquals(3000.00, $package->calculateTotalReturn());
    }

    public function test_investment_package_calculates_daily_return_percentage(): void
    {
        $package = InvestmentPackage::factory()->make([
            'price' => 1000.00,
            'daily_income' => 50.00,
        ]);

        $this->assertEquals(5.0, $package->getDailyReturnPercentage());
    }

    public function test_investment_package_calculates_total_return_percentage(): void
    {
        $package = InvestmentPackage::factory()->make([
            'price' => 1000.00,
            'total_return' => 3000.00,
        ]);

        $this->assertEquals(300.0, $package->getTotalReturnPercentage());
    }

    public function test_investment_package_is_available_when_active(): void
    {
        $package = InvestmentPackage::factory()->make(['is_active' => true]);
        $this->assertTrue($package->isAvailable());

        $package = InvestmentPackage::factory()->make(['is_active' => false]);
        $this->assertFalse($package->isAvailable());
    }

    public function test_validation_rules_are_correct(): void
    {
        $rules = InvestmentPackage::validationRules();

        $this->assertArrayHasKey('name', $rules);
        $this->assertArrayHasKey('price', $rules);
        $this->assertArrayHasKey('daily_income', $rules);
        $this->assertArrayHasKey('duration_days', $rules);
        $this->assertArrayHasKey('total_return', $rules);
        $this->assertArrayHasKey('is_active', $rules);
    }

    public function test_package_names_constant_contains_all_seven_packages(): void
    {
        $expectedPackages = [
            'Bronze',
            'Silver',
            'Gold',
            'Sapphire',
            'Ruby',
            'Emerald',
            'Diamond'
        ];

        $this->assertEquals($expectedPackages, InvestmentPackage::PACKAGE_NAMES);
        $this->assertCount(7, InvestmentPackage::PACKAGE_NAMES);
    }
}
