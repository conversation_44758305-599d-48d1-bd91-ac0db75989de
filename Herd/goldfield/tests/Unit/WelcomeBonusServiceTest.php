<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\Transaction;
use App\Services\WelcomeBonusService;
use Illuminate\Foundation\Testing\RefreshDatabase;

class WelcomeBonusServiceTest extends TestCase
{
    use RefreshDatabase;

    private WelcomeBonusService $welcomeBonusService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->welcomeBonusService = new WelcomeBonusService();
    }

    public function test_can_credit_welcome_bonus_to_new_user()
    {
        $user = User::factory()->create([
            'balance' => 0,
            'total_earnings' => 0,
            'welcome_bonus_claimed' => false,
        ]);

        $result = $this->welcomeBonusService->creditWelcomeBonus($user);

        $this->assertTrue($result);

        // Refresh user from database
        $user->refresh();

        // Check user balance and earnings were updated
        $this->assertEquals(1500.00, $user->balance);
        $this->assertEquals(1500.00, $user->total_earnings);
        $this->assertTrue($user->welcome_bonus_claimed);

        // Check transaction was created
        $transaction = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_WELCOME_BONUS)
            ->first();

        $this->assertNotNull($transaction);
        $this->assertEquals(1500.00, $transaction->amount);
        $this->assertEquals(Transaction::STATUS_COMPLETED, $transaction->status);
        $this->assertEquals('Welcome bonus for new user registration', $transaction->description);
    }

    public function test_cannot_credit_welcome_bonus_if_already_claimed()
    {
        $user = User::factory()->create([
            'balance' => 1500.00,
            'total_earnings' => 1500.00,
            'welcome_bonus_claimed' => true,
        ]);

        $result = $this->welcomeBonusService->creditWelcomeBonus($user);

        $this->assertFalse($result);

        // Check user balance didn't change
        $user->refresh();
        $this->assertEquals(1500.00, $user->balance);
        $this->assertEquals(1500.00, $user->total_earnings);
    }

    public function test_cannot_credit_welcome_bonus_if_transaction_already_exists()
    {
        $user = User::factory()->create([
            'balance' => 0,
            'total_earnings' => 0,
            'welcome_bonus_claimed' => false,
        ]);

        // Create existing welcome bonus transaction
        Transaction::factory()->welcomeBonus()->create([
            'user_id' => $user->id,
            'amount' => 1500.00,
        ]);

        $result = $this->welcomeBonusService->creditWelcomeBonus($user);

        $this->assertFalse($result);

        // Check user balance didn't change
        $user->refresh();
        $this->assertEquals(0, $user->balance);
        $this->assertEquals(0, $user->total_earnings);
        $this->assertFalse($user->welcome_bonus_claimed);
    }

    public function test_is_eligible_for_welcome_bonus_returns_true_for_new_user()
    {
        $user = User::factory()->create([
            'welcome_bonus_claimed' => false,
        ]);

        $result = $this->welcomeBonusService->isEligibleForWelcomeBonus($user);

        $this->assertTrue($result);
    }

    public function test_is_eligible_for_welcome_bonus_returns_false_if_already_claimed()
    {
        $user = User::factory()->create([
            'welcome_bonus_claimed' => true,
        ]);

        $result = $this->welcomeBonusService->isEligibleForWelcomeBonus($user);

        $this->assertFalse($result);
    }

    public function test_is_eligible_for_welcome_bonus_returns_false_if_transaction_exists()
    {
        $user = User::factory()->create([
            'welcome_bonus_claimed' => false,
        ]);

        // Create existing welcome bonus transaction
        Transaction::factory()->welcomeBonus()->create([
            'user_id' => $user->id,
        ]);

        $result = $this->welcomeBonusService->isEligibleForWelcomeBonus($user);

        $this->assertFalse($result);
    }

    public function test_get_welcome_bonus_amount_returns_correct_amount()
    {
        $amount = $this->welcomeBonusService->getWelcomeBonusAmount();

        $this->assertEquals(1500.00, $amount);
    }

    public function test_get_welcome_bonus_transaction_returns_transaction()
    {
        $user = User::factory()->create();
        $transaction = Transaction::factory()->welcomeBonus()->create([
            'user_id' => $user->id,
        ]);

        $result = $this->welcomeBonusService->getWelcomeBonusTransaction($user);

        $this->assertNotNull($result);
        $this->assertEquals($transaction->id, $result->id);
        $this->assertEquals(Transaction::TYPE_WELCOME_BONUS, $result->type);
    }

    public function test_get_welcome_bonus_transaction_returns_null_if_no_transaction()
    {
        $user = User::factory()->create();

        $result = $this->welcomeBonusService->getWelcomeBonusTransaction($user);

        $this->assertNull($result);
    }

    public function test_credit_welcome_bonus_is_atomic()
    {
        $user = User::factory()->create([
            'balance' => 0,
            'total_earnings' => 0,
            'welcome_bonus_claimed' => false,
        ]);

        // Mock a database failure scenario by creating a user with invalid data
        // This test ensures that if any part of the transaction fails, nothing is committed
        $originalBalance = $user->balance;
        $originalEarnings = $user->total_earnings;
        $originalClaimed = $user->welcome_bonus_claimed;

        // Simulate successful operation
        $result = $this->welcomeBonusService->creditWelcomeBonus($user);
        $this->assertTrue($result);

        // Verify all changes were made atomically
        $user->refresh();
        $this->assertNotEquals($originalBalance, $user->balance);
        $this->assertNotEquals($originalEarnings, $user->total_earnings);
        $this->assertNotEquals($originalClaimed, $user->welcome_bonus_claimed);

        // Verify transaction exists
        $transaction = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_WELCOME_BONUS)
            ->first();
        $this->assertNotNull($transaction);
    }

    public function test_credit_welcome_bonus_updates_existing_user_with_balance()
    {
        $user = User::factory()->create([
            'balance' => 500.00,
            'total_earnings' => 500.00,
            'welcome_bonus_claimed' => false,
        ]);

        $result = $this->welcomeBonusService->creditWelcomeBonus($user);

        $this->assertTrue($result);

        // Refresh user from database
        $user->refresh();

        // Check welcome bonus was added to existing balance
        $this->assertEquals(2000.00, $user->balance); // 500 + 1500
        $this->assertEquals(2000.00, $user->total_earnings); // 500 + 1500
        $this->assertTrue($user->welcome_bonus_claimed);
    }
}
