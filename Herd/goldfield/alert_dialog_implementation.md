# ALERT DIALOG IMPLEMENTATION (Similar to Payment Gateways)

## 🔧 MANUAL IMPLEMENTATION STEPS

Since the auto-formatting is not working correctly, here's the exact code you need to add manually to make the reject dialog look similar to the payment gateways alert dialog:

### **1. AlertDialog Imports (Already Added)**
The imports are already added:
```typescript
import {
    AlertDialog,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
```

### **2. Add State Variables**
Add these lines after line 73 (after the statusFilter state):

```typescript
const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
const [depositToReject, setDepositToReject] = useState<Deposit | null>(null);
const [rejectionReason, setRejectionReason] = useState('');
const [isRejecting, setIsRejecting] = useState(false);
```

### **3. Replace the handleReject Function**
Find the current `handleReject` function (around line 230) and replace it with:

```typescript
const handleReject = (deposit: Deposit) => {
    setDepositToReject(deposit);
    setRejectionReason('');
    setIsRejectDialogOpen(true);
};

const handleRejectConfirm = async () => {
    if (!depositToReject || !rejectionReason.trim()) {
        toast({
            title: "Rejection Reason Required",
            description: "Please provide a reason for rejecting this deposit.",
            variant: "destructive",
        });
        return;
    }

    try {
        setIsRejecting(true);
        await updateDepositStatus(depositToReject, 'reject', rejectionReason.trim());
        setIsRejectDialogOpen(false);
        setDepositToReject(null);
        setRejectionReason('');
    } catch (error) {
        // Error is already handled in updateDepositStatus
    } finally {
        setIsRejecting(false);
    }
};

const handleRejectCancel = () => {
    setIsRejectDialogOpen(false);
    setDepositToReject(null);
    setRejectionReason('');
};
```

### **4. Add the AlertDialog Component**
Add this before the closing `</AdminLayout>` tag (around line 510):

```typescript
{/* Reject Confirmation Dialog */}
<AlertDialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
    <AlertDialogContent>
        <AlertDialogHeader>
            <AlertDialogTitle>Reject Deposit</AlertDialogTitle>
            <AlertDialogDescription>
                Are you sure you want to reject this deposit? This action cannot be undone.
            </AlertDialogDescription>
        </AlertDialogHeader>

        {depositToReject && (
            <div className="space-y-4">
                {/* Deposit Details */}
                <div className="rounded-lg border bg-gray-50 p-4">
                    <h4 className="font-medium text-gray-900 mb-2">Deposit Details</h4>
                    <div className="space-y-1 text-sm text-gray-600">
                        <div className="flex justify-between">
                            <span>User:</span>
                            <span className="font-medium">{depositToReject.user.name}</span>
                        </div>
                        <div className="flex justify-between">
                            <span>Email:</span>
                            <span className="font-medium">{depositToReject.user.email}</span>
                        </div>
                        <div className="flex justify-between">
                            <span>Amount:</span>
                            <span className="font-medium text-green-600">
                                {formatCurrency(depositToReject.amount)}
                            </span>
                        </div>
                        <div className="flex justify-between">
                            <span>Reference:</span>
                            <span className="font-medium">{depositToReject.reference}</span>
                        </div>
                        <div className="flex justify-between">
                            <span>Method:</span>
                            <span className="font-medium capitalize">{depositToReject.method}</span>
                        </div>
                        <div className="flex justify-between">
                            <span>Date:</span>
                            <span className="font-medium">
                                {new Date(depositToReject.created_at).toLocaleDateString()}
                            </span>
                        </div>
                    </div>
                </div>

                {/* Rejection Reason Input */}
                <div className="space-y-2">
                    <label htmlFor="rejection-reason" className="block text-sm font-medium text-gray-700">
                        Rejection Reason <span className="text-red-500">*</span>
                    </label>
                    <textarea
                        id="rejection-reason"
                        value={rejectionReason}
                        onChange={(e) => setRejectionReason(e.target.value)}
                        placeholder="Please provide a detailed reason for rejecting this deposit..."
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 resize-none"
                        rows={4}
                        disabled={isRejecting}
                    />
                    <p className="text-xs text-gray-500">
                        This reason will be saved for audit purposes and may be visible to the user.
                    </p>
                </div>
            </div>
        )}

        <AlertDialogFooter>
            <AlertDialogCancel 
                onClick={handleRejectCancel}
                disabled={isRejecting}
            >
                Cancel
            </AlertDialogCancel>
            <Button
                onClick={handleRejectConfirm}
                disabled={!rejectionReason.trim() || isRejecting}
                variant="destructive"
                className="min-w-[100px]"
            >
                {isRejecting ? (
                    <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Rejecting...
                    </>
                ) : (
                    'Reject Deposit'
                )}
            </Button>
        </AlertDialogFooter>
    </AlertDialogContent>
</AlertDialog>
```

## 🎯 FEATURES OF THIS IMPLEMENTATION

### **✅ Similar to Payment Gateways:**
- 🎨 **Same Styling** → Uses identical AlertDialog components and styling
- 📋 **Detailed Information** → Shows all deposit details before rejection
- 🔒 **Confirmation Required** → Requires explicit confirmation
- ⚡ **Loading States** → Shows loading spinner during rejection
- 🚫 **Disabled States** → Disables buttons during processing

### **✅ Enhanced for Deposits:**
- 💰 **Deposit-Specific Details** → Shows amount, reference, method, date
- 📝 **Required Reason** → Textarea for detailed rejection reason
- 🔍 **Audit Trail** → Saves reason for compliance
- ✅ **Validation** → Ensures reason is provided before allowing rejection
- 📱 **Responsive** → Works on all screen sizes

### **✅ Professional UX:**
- 🎯 **Clear Actions** → Cancel and Reject buttons clearly labeled
- 🔄 **Smooth Workflow** → No page refresh required
- 🚀 **Instant Feedback** → Deposit disappears immediately after rejection
- 🎨 **Consistent Design** → Matches admin interface perfectly

## 🌐 TESTING

1. Visit: http://goldfield.test/admin/deposits
2. Click red reject button (X icon)
3. Should see professional dialog similar to payment gateways
4. View deposit details in the dialog
5. Enter rejection reason in textarea
6. Click "Reject Deposit"
7. Should see loading state, then success toast
8. Deposit should disappear from list instantly

## 🎉 EXPECTED RESULT

- ✅ **Professional Dialog** → Identical styling to payment gateways
- ✅ **Comprehensive Details** → All deposit information visible
- ✅ **Required Validation** → Must provide rejection reason
- ✅ **Loading States** → Professional loading indicators
- ✅ **Instant Updates** → Immediate UI updates
- ✅ **Consistent Experience** → Matches existing admin interface
