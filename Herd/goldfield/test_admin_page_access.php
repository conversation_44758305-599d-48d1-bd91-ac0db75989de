<?php

// Test admin page access and data
echo "🔍 TESTING ADMIN PAGE ACCESS\n";
echo "============================\n\n";

require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "🧪 STEP 1: CHECK PENDING DEPOSITS DATA\n";
    echo "======================================\n";
    
    // Get pending deposits directly from database
    $pendingDeposits = App\Models\Payment::where('status', 'pending')
        ->where('metadata->type', 'deposit')
        ->with(['user'])
        ->orderBy('created_at', 'desc')
        ->get();
    
    echo "✅ Found {$pendingDeposits->count()} pending deposits\n\n";
    
    if ($pendingDeposits->count() > 0) {
        echo "📋 PENDING DEPOSITS:\n";
        foreach ($pendingDeposits->take(5) as $payment) {
            $pendingDuration = \Carbon\Carbon::parse($payment->created_at)->diffInSeconds(now());
            $eligible = $pendingDuration > 30 ? 'YES' : 'NO';
            
            echo "   ID: {$payment->id}\n";
            echo "   User: {$payment->user->name} ({$payment->user->email})\n";
            echo "   Amount: ₦" . number_format($payment->amount / 100, 2) . "\n";
            echo "   Reference: {$payment->reference}\n";
            echo "   Pending: {$pendingDuration}s (Eligible: {$eligible})\n";
            echo "   Created: {$payment->created_at}\n\n";
        }
    } else {
        echo "ℹ️  No pending deposits found\n\n";
    }
    
    echo "🧪 STEP 2: TEST ADMIN CONTROLLER DIRECTLY\n";
    echo "=========================================\n";
    
    // Test the admin controller
    $user = App\Models\User::first();
    auth()->login($user);
    
    $request = new Illuminate\Http\Request();
    $request->setUserResolver(function () use ($user) {
        return $user;
    });
    
    $depositController = app(App\Http\Controllers\Admin\DepositController::class);
    
    try {
        $response = $depositController->index($request);
        echo "✅ Admin controller working - response generated\n";
        
        // Check if it's an Inertia response
        if ($response instanceof \Inertia\Response) {
            $props = $response->toResponse($request)->getData()['page']['props'];
            $pendingDepositsData = $props['pendingDeposits'] ?? [];
            
            echo "✅ Inertia response with " . count($pendingDepositsData) . " deposits\n";
            
            if (count($pendingDepositsData) > 0) {
                echo "📋 SAMPLE DATA:\n";
                $sample = $pendingDepositsData[0];
                echo "   User: {$sample['user']['name']}\n";
                echo "   Amount: ₦" . number_format($sample['amount'] / 100, 2) . "\n";
                echo "   Duration: {$sample['pending_duration']}s\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Admin controller error: " . $e->getMessage() . "\n";
    }
    
    echo "\n🧪 STEP 3: CHECK ADMIN AUTHENTICATION\n";
    echo "=====================================\n";
    
    // Check if admin middleware exists
    $middlewareGroups = config('auth.guards');
    echo "✅ Auth guards configured:\n";
    foreach ($middlewareGroups as $guard => $config) {
        echo "   - {$guard}: {$config['driver']}\n";
    }
    
    echo "\n🧪 STEP 4: CREATE TEST DATA IF NEEDED\n";
    echo "====================================\n";
    
    if ($pendingDeposits->count() === 0) {
        echo "Creating test pending deposit...\n";
        
        $testRequest = new Illuminate\Http\Request([
            'amount' => 5000000, // ₦50,000
            'email' => $user->email,
            'type' => 'deposit',
            'callback_url' => 'http://goldfield.test/payment/callback',
            'metadata' => [
                'user_id' => $user->id,
                'description' => 'Test data for admin page',
                'source' => 'admin_test_data'
            ]
        ]);
        
        $testRequest->setUserResolver(function () use ($user) {
            return $user;
        });
        
        $paymentController = app(App\Http\Controllers\PaymentController::class);
        $response = $paymentController->initialize($testRequest);
        $responseData = json_decode($response->getContent(), true);
        
        if ($response->getStatusCode() === 200 && $responseData['status'] === 'success') {
            echo "✅ Test deposit created: Payment ID {$responseData['data']['payment_id']}\n";
        } else {
            echo "❌ Failed to create test deposit\n";
        }
    }
    
    echo "\n🌐 ADMIN URLS TO TRY:\n";
    echo "====================\n";
    echo "Main deposits page: http://goldfield.test/admin/deposits\n";
    echo "Pending deposits: http://goldfield.test/admin/deposits/pending\n";
    echo "Statistics API: http://goldfield.test/admin/deposits/statistics\n\n";
    
    echo "🔧 TROUBLESHOOTING TIPS:\n";
    echo "========================\n";
    echo "1. Check browser console for JavaScript errors\n";
    echo "2. Verify you're logged in as admin\n";
    echo "3. Check if admin middleware is working\n";
    echo "4. Try accessing the statistics API directly\n";
    echo "5. Check Laravel logs for any errors\n\n";
    
    echo "📋 NEXT STEPS:\n";
    echo "==============\n";
    echo "1. Visit: http://goldfield.test/admin/deposits\n";
    echo "2. If blank, check browser DevTools Console tab\n";
    echo "3. If auth error, try logging in as admin first\n";
    echo "4. If still issues, check Laravel logs\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
