<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\InvestmentController;

Route::middleware('auth')->get('/user', function (Request $request) {
    return $request->user();
});

// API routes that require authentication and session
Route::middleware(['web'])->group(function () {
    // Apply auth middleware to specific routes that need it
    Route::middleware('auth')->group(function () {
    // Dashboard routes
    Route::prefix('dashboard')->group(function () {
        Route::get('/data', [App\Http\Controllers\DashboardController::class, 'data'])->name('api.dashboard.data');
        Route::get('/statistics', [App\Http\Controllers\DashboardController::class, 'statistics'])->name('api.dashboard.statistics');
        Route::get('/activities', [App\Http\Controllers\DashboardController::class, 'activities'])->name('api.dashboard.activities');
        Route::post('/refresh', [App\Http\Controllers\DashboardController::class, 'refresh'])->name('api.dashboard.refresh');
    });

    // Bank account verification
    Route::post('/bank/verify-account', [App\Http\Controllers\BankController::class, 'verifyAccount'])->name('api.bank.verify');

    // Investment routes
    Route::prefix('investments')->group(function () {
        Route::get('/', [InvestmentController::class, 'index']);
        Route::get('/packages', [InvestmentController::class, 'packages']);
        Route::post('/purchase', [InvestmentController::class, 'purchase']);
        Route::get('/statistics', [InvestmentController::class, 'statistics']);
        Route::get('/{investment}', [InvestmentController::class, 'show']);
    });

    // Transaction routes
    Route::prefix('transactions')->group(function () {
        Route::get('/', [App\Http\Controllers\TransactionController::class, 'index'])->name('api.transactions.index');
        Route::get('/statistics', [App\Http\Controllers\TransactionController::class, 'statistics'])->name('transactions.statistics');
        Route::get('/export', [App\Http\Controllers\TransactionController::class, 'export'])->name('transactions.export');
        Route::get('/{transaction}', [App\Http\Controllers\TransactionController::class, 'show'])->name('api.transactions.show');
    });

    // Withdrawal routes
    Route::prefix('withdrawals')->group(function () {
        Route::post('/request', [App\Http\Controllers\WithdrawalController::class, 'requestWithdrawal'])->name('api.withdrawals.request');
        Route::get('/history', [App\Http\Controllers\WithdrawalController::class, 'getWithdrawalHistory'])->name('api.withdrawals.history');
        Route::get('/statistics', [App\Http\Controllers\WithdrawalController::class, 'getWithdrawalStats'])->name('api.withdrawals.statistics');
        Route::post('/calculate', [App\Http\Controllers\WithdrawalController::class, 'calculateWithdrawal'])->name('api.withdrawals.calculate');
        Route::put('/{transaction}/process', [App\Http\Controllers\WithdrawalController::class, 'processWithdrawal'])->name('api.withdrawals.process');
    });

    // Referral routes
    Route::prefix('referrals')->group(function () {
        Route::get('/stats', [App\Http\Controllers\ReferralController::class, 'getStats'])->name('api.referrals.stats');
        Route::get('/tree', [App\Http\Controllers\ReferralController::class, 'getReferralTree'])->name('api.referrals.tree');
        Route::get('/earnings', [App\Http\Controllers\ReferralController::class, 'getEarningsHistory'])->name('api.referrals.earnings');
        Route::get('/bonus-rates', [App\Http\Controllers\ReferralController::class, 'getBonusRates'])->name('api.referrals.bonus-rates');
        Route::get('/analytics', [App\Http\Controllers\ReferralController::class, 'getAnalytics'])->name('api.referrals.analytics');
        Route::get('/performance', [App\Http\Controllers\ReferralController::class, 'getPerformanceSummary'])->name('api.referrals.performance');
        Route::post('/generate-code', [App\Http\Controllers\ReferralController::class, 'generateNewCode'])->name('api.referrals.generate-code');
    });

    // Gift Code routes
    Route::prefix('gift-codes')->group(function () {
        Route::get('/today', [App\Http\Controllers\GiftCodeController::class, 'getTodaysCode'])->name('api.gift-codes.today');
        Route::post('/claim', [App\Http\Controllers\GiftCodeController::class, 'claimGiftCode'])->name('api.gift-codes.claim');
        Route::get('/history', [App\Http\Controllers\GiftCodeController::class, 'getClaimHistory'])->name('api.gift-codes.history');
    });
    }); // Close auth middleware group
}); // Close web middleware group

// Public referral routes (for registration)
Route::post('/referrals/validate', [App\Http\Controllers\ReferralController::class, 'validateReferralCode'])->name('api.referrals.validate');
