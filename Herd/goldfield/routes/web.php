<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

// Route::get('/', function () {
//     return Inertia::render('welcome');
// })->name('home');
Route::get('/', [App\Http\Controllers\Auth\AuthenticatedSessionController::class, 'create'])
    ->name('home');

Route::get('/test-page', function () {
    return Inertia::render('test/TestPage');
})->name('test.page');

// Test profile route
Route::get('/test-profile', function () {
    return Inertia::render('TestProfile');
})->name('test.profile');

// Test virtual account creation (temporary route for testing)
Route::get('/test/virtual-account', function () {
    $otpay = app(\App\Services\OtpayService::class);
    $result = $otpay->createVirtualAccount([
        'name' => 'Test User',
        'email' => '<EMAIL>',
        'phone' => '***********',
        'bank_code' => [100033], // Access Bank
    ]);
    
    return response()->json($result);
})->name('test.virtual-account');

// Webhook routes (CSRF exempt)
Route::prefix('webhooks')->name('webhooks.')->group(function () {
    // OTPay webhook
    Route::post('/otpay', [App\Http\Controllers\WebhookController::class, 'handleWebhook'])
        ->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class])
        ->name('otpay');
        
    // Legacy payment webhook (keep for backward compatibility)
    Route::post('/payment', [App\Http\Controllers\PaymentController::class, 'webhook'])
        ->withoutMiddleware([\App\Http\Middleware\VerifyCsrfToken::class])
        ->name('payment');
});

Route::middleware(['auth', 'verified'])->group(function () {
    // Virtual Account Routes
    Route::prefix('virtual-accounts')->name('virtual-accounts.')->group(function () {
        Route::get('/', [App\Http\Controllers\VirtualAccountController::class, 'index'])->name('index');
        Route::get('/create', [App\Http\Controllers\VirtualAccountController::class, 'create'])->name('create');
        Route::post('/', [App\Http\Controllers\VirtualAccountController::class, 'store'])->name('store');
        Route::get('/{id}', [App\Http\Controllers\VirtualAccountController::class, 'show'])->name('show');
    });

    // Payment routes
    Route::post('/payment/initialize', [App\Http\Controllers\PaymentController::class, 'initialize'])->name('payment.initialize');
    Route::get('/payment/callback', [App\Http\Controllers\PaymentController::class, 'callback'])->name('payment.callback');

    Route::get('user/dashboard', [App\Http\Controllers\DashboardController::class, 'index'])->name('dashboard');

    // Transaction routes (web interface)
    Route::get('transactions', [App\Http\Controllers\TransactionController::class, 'index'])->name('transactions.index');
    Route::get('transactions/{transaction}', [App\Http\Controllers\TransactionController::class, 'show'])->name('transactions.show');

    // Withdrawal routes (web interface)
    Route::get('withdrawals', function () {
        return Inertia::render('withdrawals');
    })->name('withdrawals.index');

    // User deposit routes
    Route::get('/user/deposit', [App\Http\Controllers\DepositController::class, 'create'])->name('deposits.create');
    Route::post('/deposits', [App\Http\Controllers\DepositController::class, 'store'])->name('deposits.store');
    Route::get('/deposits/history', [App\Http\Controllers\DepositController::class, 'history'])->name('deposits.history');

    Route::get('user/withdraw', fn () => Inertia::render('Actions/Withdraw'))->name('user.withdraw');
    Route::get('bank/account/add', fn () => Inertia::render('Bank/AddAccount'))->name('bank.account.add');

    // Team routes
    Route::get('team', function () {
        return Inertia::render('Team/Index');
    })->name('team');

    // User Profile and Settings
    Route::middleware('auth')->group(function () {
        // Invite routes
        Route::get('user/invite', function () {
            $user = Auth::user();
            return Inertia::render('Invite/Index', [
                'referral_link' => route('register', ['ref' => $user->referral_code]),
                'referral_code' => $user->referral_code,
            ]);
        })->name('user.invite');

        // Profile routes
        Route::get('user/profile', function () {
            $user = Auth::user();
            return Inertia::render('user/profile', [
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'profile_photo_url' => $user->profile_photo_url ?? null,
                    'created_at' => $user->created_at,
                    'email_verified_at' => $user->email_verified_at,
                    'last_login_at' => $user->last_login_at ?? now(),
                    'account_status' => [
                        'email_verified' => (bool) $user->email_verified_at,
                        'kyc_verified' => false,
                    ]
                ]
            ]);
        })->name('user.profile');

        // Edit Profile
        Route::get('user/profile/edit', [\App\Http\Controllers\ProfileController::class, 'edit'])
            ->name('user.profile.edit');

        // Update Profile - Handle both PUT and POST methods
        Route::match(['put', 'post'], 'user/profile', [\App\Http\Controllers\ProfileController::class, 'update'])
            ->name('user.profile.update');

        // Gift Code Redemption
        Route::get('/gift-codes/redeem', [\App\Http\Controllers\GiftCodeRedemptionController::class, 'showRedeemForm'])
            ->name('gift-codes.redeem');
        Route::post('/gift-codes/redeem', [\App\Http\Controllers\GiftCodeRedemptionController::class, 'redeem'])
            ->name('gift-codes.redeem.submit');
        Route::get('/api/gift-codes/history', [\App\Http\Controllers\GiftCodeRedemptionController::class, 'history'])
            ->name('api.gift-codes.history');
    });
});

// Admin routes
Route::prefix('admin')->name('admin.')->group(function () {
    // Admin authentication routes
    Route::middleware('guest:admin')->group(function () {
        Route::get('login', [App\Http\Controllers\Admin\AdminAuthController::class, 'showLoginForm'])->name('login');
        Route::post('login', [App\Http\Controllers\Admin\AdminAuthController::class, 'login']);
    });

    // Protected admin routes
    Route::middleware(['auth:admin', 'admin'])->group(function () {
        Route::get('dashboard', [App\Http\Controllers\Admin\AdminDashboardController::class, 'index'])->name('dashboard');

        // Admin users
        Route::get('users', fn () => Inertia::render('Admin/Users'))->name('users');

        // Plans routes - Using fully qualified route names
        Route::prefix('plans')->name('plans.')->group(function () {
            Route::get('/', [\App\Http\Controllers\Admin\PlanController::class, 'index'])
                ->name('index');
            Route::get('/create', [\App\Http\Controllers\Admin\PlanController::class, 'create'])
                ->name('create');
            Route::post('/', [\App\Http\Controllers\Admin\PlanController::class, 'store'])
                ->name('store');
            Route::get('/{plan}/edit', [\App\Http\Controllers\Admin\PlanController::class, 'edit'])
                ->name('edit');
            Route::put('/{plan}', [\App\Http\Controllers\Admin\PlanController::class, 'update'])
                ->name('update');
            Route::delete('/{plan}', [\App\Http\Controllers\Admin\PlanController::class, 'destroy'])
                ->name('destroy');
        });

        Route::get('investments', fn () => Inertia::render('Admin/Investments'))->name('investments');
        Route::get('transactions', fn () => Inertia::render('Admin/Transactions'))->name('transactions');
        Route::get('deposits', fn () => Inertia::render('Admin/Deposits'))->name('deposits');

        // Admin deposit management
        Route::post('deposits/{transaction}/approve', [App\Http\Controllers\Admin\AdminDepositController::class, 'approve'])->name('deposits.approve');
        Route::post('deposits/{transaction}/reject', [App\Http\Controllers\Admin\AdminDepositController::class, 'reject'])->name('deposits.reject');
        Route::get('deposits/{transaction}', [App\Http\Controllers\Admin\AdminDepositController::class, 'show'])->name('deposits.show');
        Route::get('withdrawals', fn () => Inertia::render('Admin/Withdrawals'))->name('withdrawals');
        Route::get('gift-codes', fn () => Inertia::render('Admin/GiftCodes'))->name('gift-codes');
        Route::get('referrals', fn () => Inertia::render('Admin/Transactions'))->name('referrals');
        Route::get('reports', fn () => Inertia::render('Admin/Transactions'))->name('reports');
        Route::get('activity', fn () => Inertia::render('Admin/Transactions'))->name('activity');
        Route::get('settings', fn () => Inertia::render('Admin/Settings'))->name('settings');

        // Payment Gateways
        Route::get('payment-gateways', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'index'])->name('payment-gateways.index');
        Route::get('payment-gateways/create', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'create'])->name('payment-gateways.create');
        Route::post('payment-gateways', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'store'])->name('payment-gateways.store');
        Route::get('payment-gateways/{paymentGateway}/edit', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'edit'])->name('payment-gateways.edit');
        Route::put('payment-gateways/{paymentGateway}', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'update'])->name('payment-gateways.update');
        Route::delete('payment-gateways/{paymentGateway}', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'destroy'])->name('payment-gateways.destroy');
        Route::post('payment-gateways/{paymentGateway}/toggle-status', [App\Http\Controllers\Admin\PaymentGatewayController::class, 'toggleStatus'])->name('payment-gateways.toggle-status');

        Route::post('logout', [App\Http\Controllers\Admin\AdminAuthController::class, 'logout'])->name('logout');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
