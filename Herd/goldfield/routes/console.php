<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule daily earnings processing
Schedule::command('goldfield:process-daily-earnings')
    ->daily()
    ->at('00:01') // Run at 12:01 AM daily
    ->withoutOverlapping()
    ->runInBackground();

// Schedule daily gift code generation
Schedule::command('gift-code:generate')
    ->daily()
    ->at('17:30') // Run at 5:30 PM daily
    ->withoutOverlapping()
    ->runInBackground();
