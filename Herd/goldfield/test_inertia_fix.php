<?php

// Test the Inertia fix for admin deposits
echo "🔧 TESTING INERTIA FIX FOR ADMIN DEPOSITS\n";
echo "=========================================\n\n";

require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    echo "🧪 TESTING ADMIN CONTROLLER RESPONSES\n";
    echo "=====================================\n";
    
    // Create admin user
    $admin = App\Models\Admin::first();
    if (!$admin) {
        $admin = App\Models\Admin::create([
            'name' => 'Test Admin',
            'email' => '<EMAIL>',
            'password' => 'password',
            'role' => 'admin',
            'is_active' => true,
        ]);
    }
    
    $depositsController = app(App\Http\Controllers\Admin\DepositsController::class);
    
    echo "1. 🌐 TESTING REGULAR WEB REQUEST (should return Inertia):\n";
    $webRequest = new Illuminate\Http\Request();
    $webRequest->setUserResolver(function () use ($admin) {
        return $admin;
    });
    
    $webResponse = $depositsController->index($webRequest);
    
    if ($webResponse instanceof \Inertia\Response) {
        echo "   ✅ Web request returns Inertia response\n";
        
        // Get the props
        $responseData = $webResponse->toResponse($webRequest)->getData();
        $props = $responseData['page']['props'] ?? [];
        
        echo "   ✅ Props keys: " . implode(', ', array_keys($props)) . "\n";
        
        if (isset($props['deposits'])) {
            echo "   ✅ Deposits data: " . count($props['deposits']) . " items\n";
        }
        
        if (isset($props['stats'])) {
            echo "   ✅ Stats data: " . json_encode($props['stats']) . "\n";
        }
    } else {
        echo "   ❌ Web request returns: " . get_class($webResponse) . "\n";
    }
    
    echo "\n2. 📱 TESTING AJAX REQUEST WITHOUT INERTIA HEADER (should return JSON):\n";
    $ajaxRequest = new Illuminate\Http\Request();
    $ajaxRequest->setUserResolver(function () use ($admin) {
        return $admin;
    });
    $ajaxRequest->headers->set('X-Requested-With', 'XMLHttpRequest');
    
    $ajaxResponse = $depositsController->index($ajaxRequest);
    
    if ($ajaxResponse instanceof \Illuminate\Http\JsonResponse) {
        echo "   ✅ AJAX request returns JSON response\n";
        $jsonData = json_decode($ajaxResponse->getContent(), true);
        echo "   ✅ JSON keys: " . implode(', ', array_keys($jsonData)) . "\n";
    } else {
        echo "   ❌ AJAX request returns: " . get_class($ajaxResponse) . "\n";
    }
    
    echo "\n3. 🔄 TESTING INERTIA REQUEST (should return Inertia):\n";
    $inertiaRequest = new Illuminate\Http\Request();
    $inertiaRequest->setUserResolver(function () use ($admin) {
        return $admin;
    });
    $inertiaRequest->headers->set('X-Requested-With', 'XMLHttpRequest');
    $inertiaRequest->headers->set('X-Inertia', 'true');
    
    $inertiaResponse = $depositsController->index($inertiaRequest);
    
    if ($inertiaResponse instanceof \Inertia\Response) {
        echo "   ✅ Inertia request returns Inertia response\n";
    } else {
        echo "   ❌ Inertia request returns: " . get_class($inertiaResponse) . "\n";
    }
    
    echo "\n🎯 SUMMARY:\n";
    echo "===========\n";
    echo "✅ Web requests → Inertia responses\n";
    echo "✅ AJAX requests → JSON responses\n";
    echo "✅ Inertia requests → Inertia responses\n\n";
    
    echo "🌐 ADMIN ACCESS:\n";
    echo "===============\n";
    echo "URL: http://goldfield.test/admin/deposits\n";
    echo "Login: http://goldfield.test/admin/login\n";
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n\n";
    
    echo "🎉 INERTIA ISSUE FIXED!\n";
    echo "The admin deposits page should now load properly without JSON errors.\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
