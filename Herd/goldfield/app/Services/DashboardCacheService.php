<?php

namespace App\Services;

use App\Models\User;
use App\Models\Investment;
use App\Models\Transaction;
use App\Models\InvestmentPackage;
use App\Models\Plan;
use App\Models\DailyGiftCode;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardCacheService
{
    /**
     * Cache duration constants (in seconds)
     */
    const CACHE_DURATION_SHORT = 300; // 5 minutes
    const CACHE_DURATION_MEDIUM = 900; // 15 minutes
    const CACHE_DURATION_LONG = 3600; // 1 hour

    /**
     * Get cached dashboard statistics for a user.
     */
    public function getCachedStatistics(User $user): array
    {
        $cacheKey = "dashboard_stats_{$user->id}";

        return Cache::remember($cacheKey, self::CACHE_DURATION_SHORT, function () use ($user) {
            return $this->calculateStatistics($user);
        });
    }

    /**
     * Get cached recent transactions for a user.
     */
    public function getCachedRecentTransactions(User $user, int $limit = 10): array
    {
        $cacheKey = "dashboard_transactions_{$user->id}_{$limit}";

        return Cache::remember($cacheKey, self::CACHE_DURATION_SHORT, function () use ($user, $limit) {
            return $this->getRecentTransactions($user, $limit);
        });
    }

    /**
     * Get cached active investments for a user.
     */
    public function getCachedActiveInvestments(User $user): array
    {
        $cacheKey = "dashboard_investments_{$user->id}";

        return Cache::remember($cacheKey, self::CACHE_DURATION_MEDIUM, function () use ($user) {
            return $this->getActiveInvestments($user);
        });
    }

    /**
     * Get cached investment packages.
     */
    public function getCachedInvestmentPackages(): array
    {
        $cacheKey = "investment_packages_active";

        return Cache::remember($cacheKey, self::CACHE_DURATION_LONG, function () {
            return $this->getInvestmentPackages();
        });
    }
    
    /**
     * Clear the investment packages cache.
     */
    public function clearInvestmentPackagesCache(): void
    {
        $cacheKey = "investment_packages_active";
        Cache::forget($cacheKey);
    }

    /**
     * Get cached investment plans from admin.
     */
    public function getCachedInvestmentPlans(): array
    {
        $cacheKey = "investment_plans_active";

        return Cache::remember($cacheKey, self::CACHE_DURATION_LONG, function () {
            return $this->getInvestmentPlans();
        });
    }

    /**
     * Get cached gift code status for a user.
     */
    public function getCachedGiftCodeStatus(User $user): array
    {
        $cacheKey = "gift_code_status_{$user->id}_" . Carbon::today()->format('Y-m-d');

        return Cache::remember($cacheKey, self::CACHE_DURATION_SHORT, function () use ($user) {
            return $this->getGiftCodeStatus($user);
        });
    }

    /**
     * Get cached referral statistics for a user.
     */
    public function getCachedReferralStats(User $user): array
    {
        $cacheKey = "referral_stats_{$user->id}";

        return Cache::remember($cacheKey, self::CACHE_DURATION_MEDIUM, function () use ($user) {
            return $user->getReferralStats();
        });
    }

    /**
     * Invalidate user-specific cache entries.
     */
    public function invalidateUserCache(User $user): void
    {
        $keys = [
            "dashboard_stats_{$user->id}",
            "dashboard_transactions_{$user->id}_10",
            "dashboard_investments_{$user->id}",
            "gift_code_status_{$user->id}_" . Carbon::today()->format('Y-m-d'),
            "referral_stats_{$user->id}",
        ];

        foreach ($keys as $key) {
            Cache::forget($key);
        }
    }

    /**
     * Invalidate global cache entries.
     */
    public function invalidateGlobalCache(): void
    {
        Cache::forget('investment_packages_active');
    }

    /**
     * Calculate statistics with optimized queries.
     */
    private function calculateStatistics(User $user): array
    {
        // Calculate today's earnings
        $todayEarnings = DB::table('transactions')
            ->where('user_id', $user->id)
            ->where('status', Transaction::STATUS_COMPLETED)
            ->whereIn('type', [Transaction::TYPE_DAILY_INCOME, Transaction::TYPE_REFERRAL_BONUS, Transaction::TYPE_GIFT_CODE])
            ->whereDate('created_at', Carbon::today())
            ->sum('amount');

        // Calculate monthly earnings
        $monthlyEarnings = DB::table('transactions')
            ->where('user_id', $user->id)
            ->where('status', Transaction::STATUS_COMPLETED)
            ->whereIn('type', [Transaction::TYPE_DAILY_INCOME, Transaction::TYPE_REFERRAL_BONUS, Transaction::TYPE_GIFT_CODE])
            ->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year)
            ->sum('amount');

        // Calculate pending withdrawals
        $pendingWithdrawals = DB::table('transactions')
            ->where('user_id', $user->id)
            ->where('type', Transaction::TYPE_WITHDRAWAL)
            ->where('status', Transaction::STATUS_PENDING)
            ->sum('amount');

        // Get investment statistics
        $investmentStats = DB::table('investments')
            ->where('user_id', $user->id)
            ->select([
                DB::raw('SUM(CASE WHEN status IN ("active", "completed") THEN amount ELSE 0 END) as total_invested'),
                DB::raw('COUNT(CASE WHEN status = "active" THEN 1 END) as active_investments_count'),
                DB::raw('SUM(CASE WHEN status = "active" THEN amount ELSE 0 END) as total_investment_value'),
                DB::raw('SUM(CASE WHEN status = "active" THEN daily_income ELSE 0 END) as expected_daily_income'),
            ])
            ->first();

        // Get referral earnings
        $referralEarnings = DB::table('referrals')
            ->where('referrer_id', $user->id)
            ->sum('total_earned');

        // Get total referrals count
        $totalReferrals = DB::table('referrals')
            ->where('referrer_id', $user->id)
            ->count();

        return [
            'current_balance' => $user->balance,
            'total_earnings' => $user->total_earnings,
            'today_earnings' => $todayEarnings ?? 0,
            'monthly_earnings' => $monthlyEarnings ?? 0,
            'pending_withdrawals' => $pendingWithdrawals ?? 0,
            'total_invested' => $investmentStats->total_invested ?? 0,
            'active_investments_count' => $investmentStats->active_investments_count ?? 0,
            'total_investment_value' => $investmentStats->total_investment_value ?? 0,
            'expected_daily_income' => $investmentStats->expected_daily_income ?? 0,
            'referral_earnings' => $referralEarnings ?? 0,
            'total_referrals' => $totalReferrals ?? 0,
        ];
    }

    /**
     * Get recent transactions with optimized query.
     */
    private function getRecentTransactions(User $user, int $limit): array
    {
        $transactions = DB::table('transactions as t')
            ->leftJoin('investments as i', function ($join) {
                $join->on('t.reference_id', '=', 'i.id')
                     ->where('t.type', '=', Transaction::TYPE_INVESTMENT);
            })
            ->leftJoin('investment_packages as ip', 'i.package_id', '=', 'ip.id')
            ->leftJoin('referrals as r', function ($join) {
                $join->on('t.reference_id', '=', 'r.id')
                     ->where('t.type', '=', Transaction::TYPE_REFERRAL_BONUS);
            })
            ->leftJoin('users as ru', 'r.referee_id', '=', 'ru.id')
            ->leftJoin('gift_code_claims as gcc', function ($join) {
                $join->on('t.reference_id', '=', 'gcc.id')
                     ->where('t.type', '=', Transaction::TYPE_GIFT_CODE);
            })
            ->leftJoin('daily_gift_codes as dgc', 'gcc.gift_code_id', '=', 'dgc.id')
            ->where('t.user_id', $user->id)
            ->select([
                't.id',
                't.type',
                't.amount',
                't.description',
                't.status',
                't.created_at',
                'ip.name as package_name',
                'r.level as referral_level',
                'ru.name as referee_name',
                'dgc.code as gift_code',
            ])
            ->orderBy('t.created_at', 'desc')
            ->limit($limit)
            ->get();

        return $transactions->map(function ($transaction) {
            $reference = null;

            switch ($transaction->type) {
                case Transaction::TYPE_INVESTMENT:
                    if ($transaction->package_name) {
                        $reference = [
                            'type' => 'investment',
                            'package_name' => $transaction->package_name,
                        ];
                    }
                    break;
                case Transaction::TYPE_REFERRAL_BONUS:
                    if ($transaction->referee_name) {
                        $reference = [
                            'type' => 'referral',
                            'level' => $transaction->referral_level,
                            'referee_name' => $transaction->referee_name,
                        ];
                    }
                    break;
                case Transaction::TYPE_GIFT_CODE:
                    if ($transaction->gift_code) {
                        $reference = [
                            'type' => 'gift_code',
                            'code' => $transaction->gift_code,
                        ];
                    }
                    break;
            }

            return [
                'id' => $transaction->id,
                'type' => $transaction->type,
                'amount' => $transaction->amount,
                'description' => $transaction->description,
                'status' => $transaction->status,
                'created_at' => $transaction->created_at,
                'formatted_date' => Carbon::parse($transaction->created_at)->format('M d, Y H:i'),
                'reference' => $reference,
            ];
        })->toArray();
    }

    /**
     * Get active investments with optimized query.
     */
    private function getActiveInvestments(User $user): array
    {
        $investments = DB::table('investments as i')
            ->join('investment_packages as ip', 'i.package_id', '=', 'ip.id')
            ->where('i.user_id', $user->id)
            ->where('i.status', Investment::STATUS_ACTIVE)
            ->select([
                'i.id',
                'i.amount',
                'i.daily_income',
                'i.start_date',
                'i.end_date',
                'i.status',
                'i.total_earned',
                'ip.name as package_name',
                'ip.duration_days',
            ])
            ->orderBy('i.created_at', 'desc')
            ->get();

        return $investments->map(function ($investment) {
            $startDate = Carbon::parse($investment->start_date);
            $endDate = Carbon::parse($investment->end_date);
            $today = Carbon::today();

            $remainingDays = max(0, $today->diffInDays($endDate, false));
            $daysElapsed = max(0, $startDate->diffInDays($today, false));
            $progressPercentage = min(100, ($daysElapsed / $investment->duration_days) * 100);
            $expectedTotalReturn = $investment->daily_income * $investment->duration_days;

            return [
                'id' => $investment->id,
                'package_name' => $investment->package_name,
                'amount' => $investment->amount,
                'daily_income' => $investment->daily_income,
                'start_date' => $investment->start_date,
                'end_date' => $investment->end_date,
                'status' => $investment->status,
                'total_earned' => $investment->total_earned,
                'remaining_days' => $remainingDays,
                'progress_percentage' => $progressPercentage,
                'expected_total_return' => $expectedTotalReturn,
            ];
        })->toArray();
    }

    /**
     * Get investment packages with optimized query.
     */
    public function getInvestmentPackages(): array
    {
        $plans = DB::table('plans')
            ->where('is_active', true)
            ->select([
                'id',
                'name',
                'price',
                'daily_income',
                'total_return',
                'duration as duration_days',
                'icon',
                'color',
            ])
            ->orderBy('price', 'asc')
            ->get();

        return $plans->map(function ($plan) {
            $roiPercentage = $plan->price > 0 ? (($plan->total_return - $plan->price) / $plan->price) * 100 : 0;

            return [
                'id' => $plan->id,
                'name' => $plan->name,
                'price' => (float) $plan->price,
                'daily_income' => (float) $plan->daily_income,
                'total_return' => (float) $plan->total_return,
                'duration_days' => (int) $plan->duration_days,
                'roi_percentage' => $roiPercentage,
                'icon' => $plan->icon,
                'color' => $plan->color,
            ];
        })->toArray();
    }

    /**
     * Get gift code status with optimized query.
     */
    private function getGiftCodeStatus(User $user): array
    {
        $today = Carbon::today();
        $giftTime = Carbon::today()->setTime(17, 30); // 5:30 PM

        // Get today's gift code with claim status in single query
        $giftCodeData = DB::table('daily_gift_codes as dgc')
            ->leftJoin('gift_code_claims as gcc', function ($join) use ($user) {
                $join->on('dgc.id', '=', 'gcc.gift_code_id')
                     ->where('gcc.user_id', '=', $user->id);
            })
            ->where('dgc.valid_date', $today)
            ->where('dgc.is_active', true)
            ->select([
                'dgc.id',
                'dgc.amount',
                'gcc.id as claim_id',
            ])
            ->first();

        if (!$giftCodeData) {
            return [
                'available' => false,
                'claimed' => false,
                'next_gift_time' => $giftTime->addDay()->toISOString(),
                'message' => 'No gift code available today',
            ];
        }

        $hasClaimed = !is_null($giftCodeData->claim_id);
        $now = Carbon::now();
        $isGiftTimeReached = $now->gte($giftTime);

        return [
            'available' => $isGiftTimeReached && !$hasClaimed,
            'claimed' => $hasClaimed,
            'gift_code_id' => $giftCodeData->id,
            'amount' => $giftCodeData->amount,
            'gift_time' => $giftTime->toISOString(),
            'next_gift_time' => $giftTime->addDay()->toISOString(),
            'time_until_gift' => $isGiftTimeReached ? 0 : $giftTime->diffInSeconds($now),
        ];
    }
}
