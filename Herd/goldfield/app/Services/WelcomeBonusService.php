<?php

namespace App\Services;

use App\Models\User;
use App\Models\Transaction;
use Illuminate\Support\Facades\DB;

/**
 * Service for managing welcome bonus functionality.
 *
 * This service handles the crediting of welcome bonuses to new users,
 * prevents duplicate bonuses, and provides methods to check eligibility
 * and retrieve welcome bonus information.
 *
 * Usage:
 * $service = new WelcomeBonusService();
 * $service->creditWelcomeBonus($user);
 */
class WelcomeBonusService
{
    const WELCOME_BONUS_AMOUNT = 1500.00;

    /**
     * Credit welcome bonus to a new user.
     */
    public function creditWelcomeBonus(User $user): bool
    {
        // Check if user has already claimed welcome bonus
        if ($user->welcome_bonus_claimed) {
            return false;
        }

        // Check if welcome bonus transaction already exists
        $existingTransaction = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_WELCOME_BONUS)
            ->exists();

        if ($existingTransaction) {
            return false;
        }

        return DB::transaction(function () use ($user) {
            // Update user balance and mark welcome bonus as claimed
            $user->increment('balance', self::WELCOME_BONUS_AMOUNT);
            $user->increment('total_earnings', self::WELCOME_BONUS_AMOUNT);
            $user->update(['welcome_bonus_claimed' => true]);

            // Create welcome bonus transaction
            Transaction::create([
                'user_id' => $user->id,
                'type' => Transaction::TYPE_WELCOME_BONUS,
                'amount' => self::WELCOME_BONUS_AMOUNT,
                'description' => 'Welcome bonus for new user registration',
                'status' => Transaction::STATUS_COMPLETED,
            ]);

            return true;
        });
    }

    /**
     * Check if user is eligible for welcome bonus.
     */
    public function isEligibleForWelcomeBonus(User $user): bool
    {
        // User must not have claimed welcome bonus yet
        if ($user->welcome_bonus_claimed) {
            return false;
        }

        // Check if welcome bonus transaction already exists
        $existingTransaction = Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_WELCOME_BONUS)
            ->exists();

        return !$existingTransaction;
    }

    /**
     * Get welcome bonus amount.
     */
    public function getWelcomeBonusAmount(): float
    {
        return self::WELCOME_BONUS_AMOUNT;
    }

    /**
     * Get welcome bonus transaction for a user.
     */
    public function getWelcomeBonusTransaction(User $user): ?Transaction
    {
        return Transaction::where('user_id', $user->id)
            ->where('type', Transaction::TYPE_WELCOME_BONUS)
            ->first();
    }
}
