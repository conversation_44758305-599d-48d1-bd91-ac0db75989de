<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class OtpayService
{
    protected $baseUrl;
    protected $apiKey;
    protected $secretKey;
    protected $businessCode;
    protected $timeout;

    public function __construct()
    {
        $this->baseUrl = rtrim(config('otpay.base_url'), '/');
        $this->apiKey = config('otpay.api_key');
        $this->secretKey = config('otpay.secret_key');
        $this->businessCode = config('otpay.business_code');
        $this->timeout = config('otpay.timeout', 30);

        if (empty($this->apiKey) || empty($this->secretKey) || empty($this->businessCode)) {
            throw new \RuntimeException('OTPay API credentials are not properly configured.');
        }
    }

    /**
     * Generate headers for API requests
     */
    protected function getHeaders(): array
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'api-key' => $this->apiKey,
            'secret-key' => $this->secretKey,
            'X-Request-Id' => (string) Str::uuid(),
        ];
    }

    /**
     * Initialize a payment
     */
    public function initializePayment(array $data): array
    {
        $endpoint = "{$this->baseUrl}/payments/initialize";

        $payload = array_merge([
            'business_code' => $this->businessCode,
            'amount' => $data['amount'],
            'email' => $data['email'],
            'reference' => $data['reference'],
            'callback_url' => $data['callback_url'],
            'currency' => $data['currency'] ?? config('otpay.default_currency'),
            'metadata' => $data['metadata'] ?? [],
        ], $data);

        try {
            $response = Http::withHeaders($this->getHeaders())
                ->timeout($this->timeout)
                ->post($endpoint, $payload);

            return [
                'success' => $response->successful(),
                'data' => $response->json(),
                'status' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('OTPay Payment Initialization Error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Payment initialization failed. Please try again.',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify a payment
     */
    public function verifyPayment(string $reference): array
    {
        $endpoint = "{$this->baseUrl}/transactions/verify/{$reference}";

        try {
            $response = Http::withHeaders($this->getHeaders())
                ->timeout($this->timeout)
                ->get($endpoint);

            return [
                'success' => $response->successful(),
                'data' => $response->json(),
                'status' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('OTPay Payment Verification Error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Payment verification failed. Please try again.',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Create a virtual account
     */
    public function createVirtualAccount(array $data): array
    {
        $endpoint = "{$this->baseUrl}/create_virtual_account";

        $payload = array_merge([
            'business_code' => $this->businessCode,
            'name' => $data['name'] ?? 'Customer',
            'email' => $data['email'],
            'phone' => $data['phone'],
            'bank_code' => $data['bank_code'] ?? [100033], // Default to Access Bank
        ], $data);

        try {
            $response = Http::withHeaders($this->getHeaders())
                ->timeout($this->timeout)
                ->post($endpoint, $payload);

            return [
                'success' => $response->successful(),
                'data' => $response->json(),
                'status' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('OTPay Virtual Account Creation Error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Virtual account creation failed. Please try again.',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify webhook signature
     *
     * @param string $payload Raw request payload
     * @param string $signature Signature from X-OTPay-Signature header
     * @return bool
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        if (empty($this->webhookSecret)) {
            Log::error('OTPay Webhook: Webhook secret is not configured');
            return false;
        }

        $computedSignature = hash_hmac('sha256', $payload, $this->webhookSecret);

        // Log verification attempt for debugging
        Log::debug('OTPay Webhook Signature Verification', [
            'received_signature' => $signature,
            'computed_signature' => $computedSignature,
            'is_valid' => hash_equals($computedSignature, $signature)
        ]);

        return hash_equals($computedSignature, $signature);
    }

    /**
     * Get list of supported banks
     *
     * @return array
     */
    public function getBanks(): array
    {
        $endpoint = "{$this->baseUrl}/get_banks";

        try {
            $response = Http::withHeaders($this->getHeaders())
                ->timeout($this->timeout)
                ->get($endpoint);

            if ($response->successful()) {
                return $response->json('data', []);
            }

            Log::error('OTPay: Failed to fetch banks', [
                'status' => $response->status(),
                'response' => $response->json()
            ]);

            return [];

        } catch (\Exception $e) {
            Log::error('OTPay: Exception while fetching banks - ' . $e->getMessage());
            return [];
        }
    }
}
