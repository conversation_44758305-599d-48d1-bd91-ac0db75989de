<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class OtpayService
{
    protected $baseUrl;
    protected $apiKey;
    protected $secretKey;
    protected $businessCode;
    protected $timeout;

    public function __construct()
    {
        $this->baseUrl = rtrim(config('otpay.base_url'), '/');
        $this->apiKey = config('otpay.api_key');
        $this->secretKey = config('otpay.secret_key');
        $this->businessCode = config('otpay.business_code');
        $this->timeout = config('otpay.timeout', 30);

        // Log configuration values for debugging
        Log::info('OtpayService constructor called', [
            'base_url' => $this->baseUrl,
            'api_key_set' => !empty($this->apiKey),
            'secret_key_set' => !empty($this->secretKey),
            'business_code_set' => !empty($this->businessCode),
        ]);

        if (empty($this->apiKey) || empty($this->secretKey) || empty($this->businessCode)) {
            Log::error('OTPay API credentials missing', [
                'api_key' => $this->apiKey ? 'SET' : 'EMPTY',
                'secret_key' => $this->secretKey ? 'SET' : 'EMPTY',
                'business_code' => $this->businessCode ? 'SET' : 'EMPTY',
            ]);
            throw new \RuntimeException('OTPay API credentials are not properly configured.');
        }
    }

    /**
     * Generate headers for API requests
     */
    protected function getHeaders(): array
    {
        return [
            'Accept' => 'application/json',
            'Content-Type' => 'application/json',
            'Authorization' => 'Bearer ' . $this->apiKey,
            'X-Request-Id' => (string) Str::uuid(),
        ];
    }

    /**
     * Initialize a payment
     */
    public function initializePayment(array $data): array
    {
        $endpoint = "{$this->baseUrl}/payments/initialize";

        $payload = array_merge([
            'business_code' => $this->businessCode,
            'amount' => $data['amount'],
            'email' => $data['email'],
            'reference' => $data['reference'],
            'callback_url' => $data['callback_url'],
            'currency' => $data['currency'] ?? config('otpay.default_currency'),
            'metadata' => $data['metadata'] ?? [],
        ], $data);

        try {
            $response = Http::withHeaders($this->getHeaders())
                ->timeout($this->timeout)
                ->post($endpoint, $payload);

            return [
                'success' => $response->successful(),
                'data' => $response->json(),
                'status' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('OTPay Payment Initialization Error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Payment initialization failed. Please try again.',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify a payment
     */
    public function verifyPayment(string $reference): array
    {
        $endpoint = "{$this->baseUrl}/transactions/verify/{$reference}";

        try {
            $response = Http::withHeaders($this->getHeaders())
                ->timeout($this->timeout)
                ->get($endpoint);

            return [
                'success' => $response->successful(),
                'data' => $response->json(),
                'status' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('OTPay Payment Verification Error: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Payment verification failed. Please try again.',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Verify webhook signature
     */
    public function verifyWebhookSignature(string $payload, string $signature): bool
    {
        $webhookSecret = config('otpay.webhook_secret');

        // If no webhook secret is set, skip verification
        if (empty($webhookSecret)) {
            return true;
        }

        $computedSignature = hash_hmac('sha512', $payload, $webhookSecret);
        return hash_equals($computedSignature, $signature);
    }
}
