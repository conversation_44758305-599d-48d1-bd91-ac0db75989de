<?php

namespace App\Listeners;

use App\Services\DashboardCacheService;
use App\Models\User;
use App\Models\Investment;
use App\Models\Transaction;
use App\Models\Referral;
use App\Models\GiftCodeClaim;
use Illuminate\Database\Eloquent\Model;

class InvalidateDashboardCache
{
    protected DashboardCacheService $cacheService;

    public function __construct(DashboardCacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Handle model events that should invalidate dashboard cache.
     */
    public function handle($event, $models)
    {
        foreach ($models as $model) {
            $this->invalidateCacheForModel($model);
        }
    }

    /**
     * Invalidate cache based on the model type and changes.
     */
    private function invalidateCacheForModel(Model $model)
    {
        switch (get_class($model)) {
            case Transaction::class:
                $this->handleTransactionChange($model);
                break;

            case Investment::class:
                $this->handleInvestmentChange($model);
                break;

            case Referral::class:
                $this->handleReferralChange($model);
                break;

            case GiftCodeClaim::class:
                $this->handleGiftCodeClaimChange($model);
                break;

            case User::class:
                $this->handleUserChange($model);
                break;
        }
    }

    /**
     * Handle transaction model changes.
     */
    private function handleTransactionChange(Transaction $transaction)
    {
        if ($transaction->user_id) {
            $user = User::find($transaction->user_id);
            if ($user) {
                $this->cacheService->invalidateUserCache($user);
            }
        }
    }

    /**
     * Handle investment model changes.
     */
    private function handleInvestmentChange(Investment $investment)
    {
        if ($investment->user_id) {
            $user = User::find($investment->user_id);
            if ($user) {
                $this->cacheService->invalidateUserCache($user);
            }
        }
    }

    /**
     * Handle referral model changes.
     */
    private function handleReferralChange(Referral $referral)
    {
        // Invalidate cache for both referrer and referee
        if ($referral->referrer_id) {
            $referrer = User::find($referral->referrer_id);
            if ($referrer) {
                $this->cacheService->invalidateUserCache($referrer);
            }
        }

        if ($referral->referee_id) {
            $referee = User::find($referral->referee_id);
            if ($referee) {
                $this->cacheService->invalidateUserCache($referee);
            }
        }
    }

    /**
     * Handle gift code claim changes.
     */
    private function handleGiftCodeClaimChange(GiftCodeClaim $claim)
    {
        if ($claim->user_id) {
            $user = User::find($claim->user_id);
            if ($user) {
                $this->cacheService->invalidateUserCache($user);
            }
        }
    }

    /**
     * Handle user model changes.
     */
    private function handleUserChange(User $user)
    {
        // Only invalidate if balance or earnings related fields changed
        $relevantFields = ['balance', 'total_earnings'];

        if ($user->wasChanged($relevantFields)) {
            $this->cacheService->invalidateUserCache($user);
        }
    }
}
