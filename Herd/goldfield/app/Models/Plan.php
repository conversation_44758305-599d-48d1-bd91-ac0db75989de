<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Plan extends Model
{
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'min_deposit',
        'max_deposit',
        'type',
        'return',
        'return_type',
        'return_periods',
        'duration',
        'features',
        'is_active',
        'referral_bonus',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'min_deposit' => 'decimal:2',
        'max_deposit' => 'decimal:2',
        'return' => 'decimal:2',
        'referral_bonus' => 'decimal:2',
        'is_active' => 'boolean',
        'features' => 'array',
        'return_periods' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Get the features attribute.
     *
     * @param  mixed  $value
     * @return array
     */
    public function getFeaturesAttribute($value)
    {
        if (is_string($value)) {
            return json_decode($value, true) ?: [];
        }
        
        return $value ?? [];
    }
    
    /**
     * Get the return_periods attribute.
     *
     * @param  mixed  $value
     * @return array
     */
    public function getReturnPeriodsAttribute($value)
    {
        if (is_string($value)) {
            return json_decode($value, true) ?: [];
        }
        
        return $value ?? [];
    }

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'type' => 'fixed',
        'return_type' => 'percent',
        'is_active' => true,
        'return_periods' => '{}',
        'features' => '[]',
    ];
}
