<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class DailyGiftCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'amount',
        'valid_date',
        'is_active',
        'usage_limit',
        'used_count',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'valid_date' => 'date',
        'is_active' => 'boolean',
    ];

    /**
     * The time when gift codes become available (5:30 PM)
     */
    const GIFT_CODE_TIME = '17:30:00';

    /**
     * Get the claims for this gift code.
     */
    public function claims()
    {
        return $this->hasMany(GiftCodeClaim::class, 'gift_code_id');
    }

    /**
     * Check if the gift code is currently claimable
     */
    public function isClaimable(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = Carbon::now();
        $giftDateTime = Carbon::parse($this->valid_date->format('Y-m-d') . ' ' . self::GIFT_CODE_TIME);

        // Gift code is claimable from 5:30 PM on the valid date until end of day
        return $now->gte($giftDateTime) && $now->isSameDay($this->valid_date);
    }

    /**
     * Check if user has already claimed this gift code
     */
    public function hasBeenClaimedByUser(int $userId): bool
    {
        return $this->claims()->where('user_id', $userId)->exists();
    }

    /**
     * Check if gift code has reached usage limit
     */
    public function hasReachedLimit(): bool
    {
        return $this->used_count >= $this->usage_limit;
    }

    /**
     * Generate a unique gift code
     */
    public static function generateCode(): string
    {
        do {
            $code = 'GOLD' . strtoupper(substr(md5(uniqid()), 0, 8));
        } while (self::where('code', $code)->exists());

        return $code;
    }

    /**
     * Get today's active gift code
     */
    public static function getTodaysCode(): ?self
    {
        return self::where('valid_date', Carbon::today())
            ->where('is_active', true)
            ->first();
    }

    /**
     * Create today's gift code if it doesn't exist
     */
    public static function createTodaysCode(float $amount = 100.00): self
    {
        return self::firstOrCreate(
            ['valid_date' => Carbon::today()],
            [
                'code' => self::generateCode(),
                'amount' => $amount,
                'is_active' => true,
                'usage_limit' => 1000,
                'used_count' => 0,
            ]
        );
    }
}
