<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class Admin extends Authenticatable
{
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'is_active',
        'last_login_at',
        'permissions',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'is_active' => 'boolean',
        'last_login_at' => 'datetime',
        'permissions' => 'array',
    ];

    /**
     * Check if admin is super admin.
     */
    public function isSuperAdmin(): bool
    {
        return $this->role === 'super_admin';
    }

    /**
     * Check if admin has a specific role
     */
    public function hasRole($role): bool
    {
        return $this->role === $role;
    }

    /**
     * Check if admin has any of the given roles
     */
    public function hasAnyRole(array $roles): bool
    {
        return in_array($this->role, $roles, true);
    }

    /**
     * Check if admin has a specific permission
     */
    public function hasPermission($permission): bool
    {
        $permissions = $this->getAllPermissions();
        return in_array($permission, $permissions, true);
    }

    /**
     * Get all permissions for the admin
     */
    public function getAllPermissions(): array
    {
        $cacheKey = 'admin_permissions_' . $this->id;
        
        return Cache::remember($cacheKey, now()->addDay(), function () {
            // Default permissions based on role
            $rolePermissions = $this->getRolePermissions();
            
            // Merge with custom permissions if any
            $customPermissions = $this->permissions ?? [];
            
            return array_unique(array_merge($rolePermissions, $customPermissions));
        });
    }

    /**
     * Get permissions based on role
     */
    protected function getRolePermissions(): array
    {
        $permissions = [];
        
        // Base permissions for all admins
        $basePermissions = [
            'view_dashboard',
        ];
        
        // Role-specific permissions
        switch ($this->role) {
            case 'super_admin':
                $permissions = array_merge($basePermissions, [
                    'manage_users',
                    'manage_roles',
                    'manage_permissions',
                    'manage_settings',
                    'view_reports',
                    'manage_transactions',
                    'approve_deposits',
                    'reject_deposits',
                    'view_all_deposits',
                ]);
                break;
                
            case 'admin':
                $permissions = array_merge($basePermissions, [
                    'manage_users',
                    'view_reports',
                    'manage_transactions',
                    'approve_deposits',
                    'reject_deposits',
                    'view_all_deposits',
                ]);
                break;
                
            case 'support':
                $permissions = array_merge($basePermissions, [
                    'view_users',
                    'view_transactions',
                    'view_deposits',
                ]);
                break;
                
            default:
                $permissions = $basePermissions;
        }
        
        return $permissions;
    }
    
    /**
     * Clear permissions cache
     */
    public function clearPermissionsCache(): void
    {
        $cacheKey = 'admin_permissions_' . $this->id;
        Cache::forget($cacheKey);
    }
    
    /**
     * The "booting" method of the model.
     */
    protected static function booted()
    {
        static::saved(function ($admin) {
            $admin->clearPermissionsCache();
        });
        
        static::deleted(function ($admin) {
            $admin->clearPermissionsCache();
        });
    }

    /**
     * Check if admin is active.
     */
    public function isActive(): bool
    {
        return (bool) $this->is_active;
    }

    /**
     * Update last login timestamp.
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }
}
