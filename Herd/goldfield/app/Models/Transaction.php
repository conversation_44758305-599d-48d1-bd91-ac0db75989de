<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\DB;

class Transaction extends Model
{
    use HasFactory;

    // Transaction type constants
    const TYPE_DEPOSIT = 'deposit';
    const TYPE_INVESTMENT = 'investment';
    const TYPE_WITHDRAWAL = 'withdrawal';
    const TYPE_DAILY_INCOME = 'daily_income';
    const TYPE_REFERRAL_BONUS = 'referral_bonus';
    const TYPE_GIFT_CODE = 'gift_code';
    const TYPE_WELCOME_BONUS = 'welcome_bonus';

    // Transaction status constants
    const STATUS_PENDING = 'pending';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';

    protected $fillable = [
        'user_id',
        'type',
        'amount',
        'description',
        'reference_id',
        'reference',
        'status',
        'metadata',
        'processed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'metadata' => 'array',
        'processed_at' => 'datetime',
    ];

    /**
     * Get the user that owns the transaction.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the related investment if this is an investment transaction.
     */
    public function investment()
    {
        return $this->belongsTo(Investment::class, 'reference_id');
    }

    /**
     * Get the related referral if this is a referral bonus transaction.
     */
    public function referral()
    {
        return $this->belongsTo(Referral::class, 'reference_id');
    }

    /**
     * Get the related gift code claim if this is a gift code transaction.
     */
    public function giftCodeClaim()
    {
        return $this->belongsTo(GiftCodeClaim::class, 'reference_id');
    }

    /**
     * Create a transaction and update user balance atomically.
     */
    public static function createWithBalanceUpdate(array $data)
    {
        return DB::transaction(function () use ($data) {
            $transaction = self::create($data);

            if ($transaction->status === self::STATUS_COMPLETED) {
                $transaction->updateUserBalance();
            }

            return $transaction;
        });
    }

    /**
     * Update user balance based on transaction type and amount.
     */
    public function updateUserBalance()
    {
        $user = $this->user;

        switch ($this->type) {
            case self::TYPE_DEPOSIT:
                // Add to balance for deposits
                $user->increment('balance', $this->amount);
                break;

            case self::TYPE_INVESTMENT:
                // Deduct from balance for investments
                $user->decrement('balance', $this->amount);
                break;

            case self::TYPE_DAILY_INCOME:
            case self::TYPE_REFERRAL_BONUS:
            case self::TYPE_GIFT_CODE:
            case self::TYPE_WELCOME_BONUS:
                // Add to balance and total earnings for income types
                $user->increment('balance', $this->amount);
                $user->increment('total_earnings', $this->amount);
                break;

            case self::TYPE_WITHDRAWAL:
                // Deduct from balance for withdrawals
                $user->decrement('balance', $this->amount);
                break;
        }
    }

    /**
     * Complete a pending transaction and update balance.
     */
    public function complete()
    {
        return DB::transaction(function () {
            $this->update(['status' => self::STATUS_COMPLETED]);
            $this->updateUserBalance();
            return $this;
        });
    }

    /**
     * Fail a pending transaction.
     */
    public function fail()
    {
        $this->update(['status' => self::STATUS_FAILED]);
        return $this;
    }

    /**
     * Scope to filter by transaction type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by transaction status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get all available transaction types.
     */
    public static function getTypes()
    {
        return [
            self::TYPE_DEPOSIT,
            self::TYPE_INVESTMENT,
            self::TYPE_WITHDRAWAL,
            self::TYPE_DAILY_INCOME,
            self::TYPE_REFERRAL_BONUS,
            self::TYPE_GIFT_CODE,
            self::TYPE_WELCOME_BONUS,
        ];
    }

    /**
     * Get all available transaction statuses.
     */
    public static function getStatuses()
    {
        return [
            self::STATUS_PENDING,
            self::STATUS_COMPLETED,
            self::STATUS_FAILED,
        ];
    }
}
