<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Storage;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'balance',
        'total_earnings',
        'referral_code',
        'referred_by',
        'welcome_bonus_claimed',
        'profile_photo_path',
    ];

    protected $appends = [
        'profile_photo_url',
    ];

    public function getProfilePhotoUrlAttribute()
    {
        if ($this->profile_photo_path) {
            return Storage::disk('public')->url($this->profile_photo_path);
        }

        $name = trim(collect(explode(' ', $this->name))->map(function ($segment) {
            return mb_substr($segment, 0, 1);
        })->join(' '));

        return 'https://ui-avatars.com/api/?name='.urlencode($name).'&color=7F9CF5&background=EBF4FF';
    }

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'balance' => 'decimal:2',
            'total_earnings' => 'decimal:2',
            'welcome_bonus_claimed' => 'boolean',
        ];
    }

    /**
     * Get the user's investments.
     */
    public function investments()
    {
        return $this->hasMany(Investment::class);
    }

    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the user's transactions.
     */
    public function transactions()
    {
        return $this->hasMany(Transaction::class);
    }

    /**
     * Get the user who referred this user.
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referred_by');
    }

    /**
     * Get the users referred by this user.
     */
    /**
     * Get the gift codes used by this user.
     */
    public function giftCodes()
    {
        return $this->belongsToMany(GiftCode::class)
            ->withPivot('amount', 'created_at')
            ->orderBy('gift_code_user.created_at', 'desc');
    }

    /**
     * Apply a gift code to the user's account.
     */
    public function applyGiftCode(string $code): array
    {
        $giftCode = GiftCode::where('code', $code)->first();

        if (!$giftCode) {
            return [
                'success' => false,
                'message' => 'Invalid gift code.'
            ];
        }

        if ($giftCode->isUsedByUser($this)) {
            return [
                'success' => false,
                'message' => 'You have already used this gift code.'
            ];
        }

        if (!$giftCode->isValid()) {
            return [
                'success' => false,
                'message' => 'This gift code is no longer valid.'
            ];
        }

        // Start a database transaction
        \DB::beginTransaction();

        try {
            // Mark the code as used
            $giftCode->markAsUsed($this);

            // Credit the user's account
            $this->increment('balance', $giftCode->amount);
            $this->increment('total_earnings', $giftCode->amount);

            // Create a transaction record
            $this->transactions()->create([
                'amount' => $giftCode->amount,
                'type' => 'gift_code',
                'status' => 'completed',
                'description' => 'Gift code redemption: ' . $giftCode->code,
            ]);

            \DB::commit();

            return [
                'success' => true,
                'message' => 'Gift code applied successfully!',
                'amount' => $giftCode->amount
            ];
        } catch (\Exception $e) {
            \DB::rollBack();
            \Log::error('Failed to apply gift code: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'An error occurred while processing your gift code.'
            ];
        }
    }

    /**
     * Get the users referred by this user.
     */
    public function referrals()
    {
        return $this->hasMany(User::class, 'referred_by');
    }

    /**
     * Get the referral relationships where this user is the referrer.
     */
    public function referralRelationships()
    {
        return $this->hasMany(Referral::class, 'referrer_id');
    }

    /**
     * Get the gift code claims for this user.
     */
    public function giftCodeClaims()
    {
        return $this->hasMany(GiftCodeClaim::class);
    }

    /**
     * Generate a unique referral code for the user.
     */
    public function generateReferralCode(): string
    {
        do {
            $code = 'GF' . strtoupper(substr(md5(uniqid()), 0, 8));
        } while (self::where('referral_code', $code)->exists());

        $this->update(['referral_code' => $code]);
        return $code;
    }

    /**
     * Get or generate referral code.
     */
    public function getReferralCode(): string
    {
        if (!$this->referral_code) {
            return $this->generateReferralCode();
        }
        return $this->referral_code;
    }

    /**
     * Find user by referral code.
     */
    public static function findByReferralCode(string $code): ?self
    {
        return self::where('referral_code', $code)->first();
    }

    /**
     * Get total referral earnings for this user.
     */
    public function getTotalReferralEarnings(): float
    {
        return $this->referralRelationships()->sum('total_earned');
    }

    /**
     * Get referral statistics.
     */
    public function getReferralStats(): array
    {
        $level1Count = $this->referralRelationships()->where('level', 1)->count();
        $level2Count = $this->referralRelationships()->where('level', 2)->count();
        $level1Earnings = $this->referralRelationships()->where('level', 1)->sum('total_earned');
        $level2Earnings = $this->referralRelationships()->where('level', 2)->sum('total_earned');

        return [
            'total_referrals' => $level1Count + $level2Count,
            'level_1_count' => $level1Count,
            'level_2_count' => $level2Count,
            'level_1_earnings' => $level1Earnings,
            'level_2_earnings' => $level2Earnings,
            'total_earnings' => $level1Earnings + $level2Earnings,
        ];
    }

    /**
     * Get referral link for sharing.
     */
    public function getReferralLink(): string
    {
        return url('/register?ref=' . $this->getReferralCode());
    }

    /**
     * Get active referrals (referrals who have made investments).
     */
    public function getActiveReferrals()
    {
        return $this->referralRelationships()
            ->whereHas('referee.investments', function ($query) {
                $query->where('status', 'active');
            });
    }

    /**
     * Get referral conversion rate.
     */
    public function getReferralConversionRate(): float
    {
        $totalReferrals = $this->referralRelationships()->count();
        if ($totalReferrals === 0) {
            return 0;
        }

        $activeReferrals = $this->getActiveReferrals()->count();
        return ($activeReferrals / $totalReferrals) * 100;
    }


}
