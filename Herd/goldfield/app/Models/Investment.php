<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class Investment extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'package_id',
        'amount',
        'daily_income',
        'start_date',
        'end_date',
        'status',
        'total_earned',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'daily_income' => 'decimal:2',
        'total_earned' => 'decimal:2',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    /**
     * Investment status constants
     */
    public const STATUS_ACTIVE = 'active';
    public const STATUS_COMPLETED = 'completed';
    public const STATUS_CANCELLED = 'cancelled';

    public const STATUSES = [
        self::STATUS_ACTIVE,
        self::STATUS_COMPLETED,
        self::STATUS_CANCELLED,
    ];

    /**
     * Get the user that owns the investment.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the investment package.
     */
    public function package()
    {
        return $this->belongsTo(InvestmentPackage::class);
    }

    /**
     * Get the transactions related to this investment.
     */
    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'reference_id')->where('type', 'investment');
    }

    /**
     * Get daily income transactions for this investment.
     */
    public function dailyIncomeTransactions()
    {
        return $this->hasMany(Transaction::class, 'reference_id')->where('type', 'daily_income');
    }

    /**
     * Check if investment is active
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if investment is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if investment is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === self::STATUS_CANCELLED;
    }

    /**
     * Get remaining days for the investment
     */
    public function getRemainingDays(): int
    {
        if (!$this->isActive()) {
            return 0;
        }

        $today = Carbon::today();
        $endDate = Carbon::parse($this->end_date);

        return max(0, $today->diffInDays($endDate, false));
    }

    /**
     * Get days elapsed since investment started
     */
    public function getDaysElapsed(): int
    {
        $today = Carbon::today();
        $startDate = Carbon::parse($this->start_date);

        return max(0, $startDate->diffInDays($today, false));
    }

    /**
     * Get progress percentage
     */
    public function getProgressPercentage(): float
    {
        $totalDays = $this->package->duration_days;
        $daysElapsed = $this->getDaysElapsed();

        return min(100, ($daysElapsed / $totalDays) * 100);
    }

    /**
     * Calculate expected total return
     */
    public function getExpectedTotalReturn(): float
    {
        return $this->daily_income * $this->package->duration_days;
    }

    /**
     * Check if investment has expired
     */
    public function hasExpired(): bool
    {
        return Carbon::today()->isAfter($this->end_date);
    }

    /**
     * Mark investment as completed
     */
    public function markAsCompleted(): void
    {
        $this->update(['status' => self::STATUS_COMPLETED]);
    }

    /**
     * Mark investment as cancelled
     */
    public function markAsCancelled(): void
    {
        $this->update(['status' => self::STATUS_CANCELLED]);
    }

    /**
     * Scope to get active investments
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get completed investments
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * Scope to get investments for a specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get expired investments that are still active
     */
    public function scopeExpiredActive($query)
    {
        return $query->active()->where('end_date', '<', Carbon::today());
    }
}
