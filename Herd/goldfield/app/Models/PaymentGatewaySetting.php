<?php

namespace App\Models;

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentGatewaySetting extends Model
{
    use SoftDeletes;
    
    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'gateway_name',
        'gateway_type',
        'is_active',
        'credentials',
        'additional_data',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'credentials' => 'array',
        'additional_data' => 'array',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Only apply soft deletes if the column exists
        static::addGlobalScope('withTrashed', function ($builder) {
            if (Schema::hasColumn((new static)->getTable(), 'deleted_at')) {
                $builder->withTrashed();
            }
        });
    }
}
