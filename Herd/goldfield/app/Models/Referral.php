<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Referral extends Model
{
    use HasFactory;

    protected $fillable = [
        'referrer_id',
        'referee_id',
        'level',
        'bonus_percentage',
        'total_earned',
    ];

    protected $casts = [
        'bonus_percentage' => 'decimal:2',
        'total_earned' => 'decimal:2',
    ];

    /**
     * Get the user who made the referral.
     */
    public function referrer()
    {
        return $this->belongsTo(User::class, 'referrer_id');
    }

    /**
     * Get the user who was referred.
     */
    public function referee()
    {
        return $this->belongsTo(User::class, 'referee_id');
    }

    /**
     * Get the transactions related to this referral.
     */
    public function transactions()
    {
        return $this->hasMany(Transaction::class, 'reference_id')->where('type', 'referral_bonus');
    }

    /**
     * Get bonus percentage for a given level.
     */
    public static function getBonusPercentage(int $level): float
    {
        return match ($level) {
            1 => 30.00,
            2 => 5.00,
            default => 0.00,
        };
    }

    /**
     * Calculate bonus amount for a given investment amount and level.
     */
    public static function calculateBonus(float $investmentAmount, int $level): float
    {
        $percentage = self::getBonusPercentage($level);
        return ($investmentAmount * $percentage) / 100;
    }

    /**
     * Create referral relationships for a new user.
     */
    public static function createReferralRelationships(User $referee, User $referrer): void
    {
        // Create level 1 referral relationship
        self::create([
            'referrer_id' => $referrer->id,
            'referee_id' => $referee->id,
            'level' => 1,
            'bonus_percentage' => self::getBonusPercentage(1),
        ]);

        // Create level 2 referral relationship if referrer has a referrer
        if ($referrer->referrer) {
            self::create([
                'referrer_id' => $referrer->referrer->id,
                'referee_id' => $referee->id,
                'level' => 2,
                'bonus_percentage' => self::getBonusPercentage(2),
            ]);
        }
    }

    /**
     * Distribute referral bonuses for an investment.
     */
    public static function distributeReferralBonuses(User $investor, float $investmentAmount): void
    {
        // Get all referral relationships for this investor
        $referralRelationships = self::where('referee_id', $investor->id)->get();

        foreach ($referralRelationships as $referral) {
            $bonusAmount = self::calculateBonus($investmentAmount, $referral->level);

            if ($bonusAmount > 0) {
                // Update referrer's balance
                $referral->referrer->increment('balance', $bonusAmount);
                $referral->referrer->increment('total_earnings', $bonusAmount);

                // Update referral total earned
                $referral->increment('total_earned', $bonusAmount);

                // Create transaction record
                Transaction::create([
                    'user_id' => $referral->referrer_id,
                    'type' => 'referral_bonus',
                    'amount' => $bonusAmount,
                    'description' => "Level {$referral->level} referral bonus from {$investor->name}'s investment",
                    'reference_id' => $referral->id,
                    'status' => 'completed',
                ]);
            }
        }
    }
}
