<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class GiftCodeClaim extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'gift_code_id',
        'amount',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
    ];

    /**
     * Get the user who claimed the gift code.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the gift code that was claimed.
     */
    public function giftCode()
    {
        return $this->belongsTo(DailyGiftCode::class, 'gift_code_id');
    }

    /**
     * Get the transaction related to this claim.
     */
    public function transaction()
    {
        return $this->hasOne(Transaction::class, 'reference_id')->where('type', 'gift_code');
    }
}
