<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Validation\Rule;

class InvestmentPackage extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'price',
        'daily_income',
        'duration_days',
        'total_return',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'daily_income' => 'decimal:2',
        'total_return' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Available package names
     */
    public const PACKAGE_NAMES = [
        'Bronze',
        'Silver',
        'Gold',
        'Sapphire',
        'Ruby',
        'Emerald',
        'Diamond'
    ];

    /**
     * Get the investments for this package.
     */
    public function investments()
    {
        return $this->hasMany(Investment::class, 'package_id');
    }

    /**
     * Get active investments for this package.
     */
    public function activeInvestments()
    {
        return $this->investments()->where('status', 'active');
    }

    /**
     * Get validation rules for investment package
     */
    public static function validationRules()
    {
        return [
            'name' => ['required', 'string', 'max:50', Rule::in(self::PACKAGE_NAMES)],
            'price' => ['required', 'numeric', 'min:0', 'max:999999999999.99'],
            'daily_income' => ['required', 'numeric', 'min:0', 'max:999999999999.99'],
            'duration_days' => ['required', 'integer', 'min:1', 'max:365'],
            'total_return' => ['required', 'numeric', 'min:0', 'max:999999999999.99'],
            'is_active' => ['boolean'],
        ];
    }

    /**
     * Calculate total return based on daily income and duration
     */
    public function calculateTotalReturn(): float
    {
        return $this->daily_income * $this->duration_days;
    }

    /**
     * Get daily return percentage
     */
    public function getDailyReturnPercentage(): float
    {
        if ($this->price <= 0) {
            return 0;
        }
        return ($this->daily_income / $this->price) * 100;
    }

    /**
     * Get total return percentage
     */
    public function getTotalReturnPercentage(): float
    {
        if ($this->price <= 0) {
            return 0;
        }
        return ($this->total_return / $this->price) * 100;
    }

    /**
     * Get ROI percentage (alias for total return percentage)
     */
    public function getRoiPercentage(): float
    {
        return $this->getTotalReturnPercentage();
    }

    /**
     * Check if package is available for purchase
     */
    public function isAvailable(): bool
    {
        return $this->is_active;
    }

    /**
     * Scope to get only active packages
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by price ascending
     */
    public function scopeOrderByPrice($query, $direction = 'asc')
    {
        return $query->orderBy('price', $direction);
    }
}
