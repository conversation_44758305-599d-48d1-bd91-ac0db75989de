<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use App\Models\User;

class GiftCode extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'amount',
        'max_uses',
        'used_count',
        'expires_at',
        'is_active'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'max_uses' => 'integer',
        'used_count' => 'integer',
        'is_active' => 'boolean',
        'expires_at' => 'datetime',
    ];

    public function users()
    {
        return $this->belongsToMany(User::class)
            ->withPivot('amount')
            ->withTimestamps();
    }

    public function isValid(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return false;
        }

        if ($this->used_count >= $this->max_uses) {
            return false;
        }

        return true;
    }

    public function isUsedByUser(User $user): bool
    {
        return $this->users()->where('user_id', $user->id)->exists();
    }

    public function markAsUsed(User $user): void
    {
        if ($this->isUsedByUser($user)) {
            return;
        }

        $this->users()->attach($user->id, ['amount' => $this->amount]);
        $this->increment('used_count');
    }
}
