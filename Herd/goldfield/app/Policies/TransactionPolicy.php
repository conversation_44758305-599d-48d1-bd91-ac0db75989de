<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Transaction;
use Illuminate\Auth\Access\HandlesAuthorization;

class TransactionPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the admin can view any models.
     */
    public function viewAny(Admin $admin): bool
    {
        return $admin->can('view transactions');
    }

    /**
     * Determine whether the admin can view the model.
     */
    public function view(Admin $admin, Transaction $transaction): bool
    {
        return $admin->can('view transactions');
    }

    /**
     * Determine whether the admin can update the model.
     */
    public function update(Admin $admin, Transaction $transaction): bool
    {
        return $admin->can('update transactions') && $transaction->status === 'pending';
    }
}
