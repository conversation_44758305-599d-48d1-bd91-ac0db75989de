<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\DailyGiftCode;
use Carbon\Carbon;

class GenerateDailyGiftCode extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gift-code:generate {--amount=100 : The gift code amount}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate daily gift code for miners';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $amount = (float) $this->option('amount');

        try {
            $giftCode = DailyGiftCode::createTodaysCode($amount);

            if ($giftCode->wasRecentlyCreated) {
                $this->info("Daily gift code generated successfully!");
                $this->info("Code: {$giftCode->code}");
                $this->info("Amount: ₦{$giftCode->amount}");
                $this->info("Valid Date: {$giftCode->valid_date->format('Y-m-d')}");
                $this->info("Available from: 5:30 PM");
            } else {
                $this->info("Gift code for today already exists:");
                $this->info("Code: {$giftCode->code}");
                $this->info("Amount: ₦{$giftCode->amount}");
                $this->info("Claims: {$giftCode->used_count}/{$giftCode->usage_limit}");
            }

            return Command::SUCCESS;
        } catch (\Exception $e) {
            $this->error("Failed to generate gift code: " . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
