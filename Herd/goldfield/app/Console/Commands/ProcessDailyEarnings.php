<?php

namespace App\Console\Commands;

use App\Models\Investment;
use App\Models\Transaction;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ProcessDailyEarnings extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'goldfield:process-daily-earnings {--dry-run : Show what would be processed without making changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process daily earnings for all active investments';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');

        if ($isDryRun) {
            $this->info('Running in dry-run mode. No changes will be made.');
        }

        $this->info('Starting daily earnings processing...');

        // Get all active investments (including expired ones to mark as completed)
        $activeInvestments = Investment::where('status', Investment::STATUS_ACTIVE)
            ->with('user')
            ->get();

        if ($activeInvestments->isEmpty()) {
            $this->info('No active investments found.');
            return 0;
        }

        $this->info("Found {$activeInvestments->count()} active investments to process.");

        $totalEarnings = 0;
        $processedCount = 0;
        $errors = [];

        foreach ($activeInvestments as $investment) {
            try {
                $result = $this->processInvestmentEarnings($investment, $isDryRun);

                if ($result['processed']) {
                    $totalEarnings += $result['amount'];
                    $processedCount++;

                    $this->line("✓ Processed investment #{$investment->id} for user {$investment->user->name}: ₦{$result['amount']}");
                } else {
                    $this->line("- Skipped investment #{$investment->id}: {$result['reason']}");
                }
            } catch (\Exception $e) {
                $errors[] = "Investment #{$investment->id}: " . $e->getMessage();
                $this->error("✗ Error processing investment #{$investment->id}: " . $e->getMessage());
            }
        }

        // Summary
        $this->newLine();
        $this->info("Daily earnings processing completed!");
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Investments Processed', $processedCount],
                ['Total Earnings Distributed', "₦" . number_format($totalEarnings, 2)],
                ['Errors', count($errors)],
            ]
        );

        if (!empty($errors)) {
            $this->newLine();
            $this->error('Errors encountered:');
            foreach ($errors as $error) {
                $this->error("- {$error}");
            }
        }

        return count($errors) > 0 ? 1 : 0;
    }

    /**
     * Process daily earnings for a single investment.
     */
    private function processInvestmentEarnings(Investment $investment, bool $isDryRun = false): array
    {
        // Check if earnings have already been processed today
        $today = now()->toDateString();
        $existingTransaction = Transaction::where('user_id', $investment->user_id)
            ->where('type', Transaction::TYPE_DAILY_INCOME)
            ->where('reference_id', $investment->id)
            ->whereDate('created_at', $today)
            ->first();

        if ($existingTransaction) {
            return [
                'processed' => false,
                'reason' => 'Already processed today',
                'amount' => 0,
            ];
        }

        // Check if investment is still active and within duration
        if ($investment->status !== Investment::STATUS_ACTIVE) {
            return [
                'processed' => false,
                'reason' => 'Investment not active',
                'amount' => 0,
            ];
        }

        if (now()->toDateString() > $investment->end_date->toDateString()) {
            // Mark investment as completed if it has expired
            if (!$isDryRun) {
                $investment->update(['status' => Investment::STATUS_COMPLETED]);
            }

            return [
                'processed' => false,
                'reason' => 'Investment expired',
                'amount' => 0,
            ];
        }

        $dailyIncome = $investment->daily_income;

        if (!$isDryRun) {
            DB::transaction(function () use ($investment, $dailyIncome) {
                // Create transaction record
                $transaction = Transaction::create([
                    'user_id' => $investment->user_id,
                    'type' => Transaction::TYPE_DAILY_INCOME,
                    'amount' => $dailyIncome,
                    'description' => "Daily income from {$investment->package->name} investment",
                    'reference_id' => $investment->id,
                    'status' => Transaction::STATUS_COMPLETED,
                ]);

                // Update user balance and total earnings
                $investment->user->increment('balance', $dailyIncome);
                $investment->user->increment('total_earnings', $dailyIncome);

                // Update investment total earned
                $investment->increment('total_earned', $dailyIncome);
            });
        }

        return [
            'processed' => true,
            'reason' => 'Successfully processed',
            'amount' => $dailyIncome,
        ];
    }
}
