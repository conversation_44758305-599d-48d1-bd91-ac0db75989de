<?php

namespace App\Providers;

use App\Models\Transaction;
use App\Policies\TransactionPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Transaction::class => TransactionPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        $this->registerPolicies();

        // Define admin-specific abilities
        Gate::define('view transactions', function ($admin) {
            return $admin->hasRole('admin') || $admin->hasRole('super_admin');
        });

        Gate::define('update transactions', function ($admin) {
            return $admin->hasRole('admin') || $admin->hasRole('super_admin');
        });
    }
}
