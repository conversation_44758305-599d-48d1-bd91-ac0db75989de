<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Investment;
use App\Models\InvestmentPackage;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Referral;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Illuminate\Validation\ValidationException;

class InvestmentController extends Controller
{
    /**
     * Display a listing of the user's investments.
     */
    public function index()
    {
        $user = Auth::user();
        $investments = $user->investments()
            ->with('package')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'investments' => $investments,
        ]);
    }

    /**
     * Display available investment packages.
     */
    public function packages()
    {
        $packages = InvestmentPackage::active()
            ->orderByPrice()
            ->get();

        return response()->json([
            'packages' => $packages,
        ]);
    }

    /**
     * Purchase an investment package.
     */
    public function purchase(Request $request)
    {
        $request->validate([
            'package_id' => ['required', 'exists:investment_packages,id'],
        ]);

        $user = Auth::user();
        $package = InvestmentPackage::findOrFail($request->package_id);

        // Check if package is available
        if (!$package->isAvailable()) {
            throw ValidationException::withMessages([
                'package_id' => ['This investment package is not currently available.'],
            ]);
        }

        // Check if user has sufficient balance
        if ($user->balance < $package->price) {
            throw ValidationException::withMessages([
                'balance' => ['Insufficient balance. You need ₦' . number_format($package->price - $user->balance) . ' more.'],
            ]);
        }

        try {
            DB::beginTransaction();

            // Deduct balance from user
            $user->decrement('balance', $package->price);

            // Calculate investment dates
            $startDate = Carbon::today();
            $endDate = $startDate->copy()->addDays($package->duration_days);

            // Create investment record
            $investment = Investment::create([
                'user_id' => $user->id,
                'package_id' => $package->id,
                'amount' => $package->price,
                'daily_income' => $package->daily_income,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'status' => Investment::STATUS_ACTIVE,
                'total_earned' => 0,
            ]);

            // Create transaction record
            Transaction::create([
                'user_id' => $user->id,
                'type' => 'investment',
                'amount' => -$package->price, // Negative because it's a deduction
                'description' => "Investment in {$package->name} package",
                'reference_id' => $investment->id,
                'status' => 'completed',
            ]);

            // Distribute referral bonuses
            Referral::distributeReferralBonuses($user, $package->price);

            DB::commit();

            // Load the investment with package relationship
            $investment->load('package');

            return response()->json([
                'message' => 'Investment purchased successfully!',
                'investment' => $investment,
                'user_balance' => $user->fresh()->balance,
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'message' => 'Failed to process investment purchase.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified investment.
     */
    public function show(Investment $investment)
    {
        // Ensure user can only view their own investments
        if ($investment->user_id !== Auth::id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $investment->load(['package', 'transactions']);

        return response()->json([
            'investment' => $investment,
            'remaining_days' => $investment->getRemainingDays(),
            'progress_percentage' => $investment->getProgressPercentage(),
            'expected_total_return' => $investment->getExpectedTotalReturn(),
        ]);
    }

    /**
     * Get user's investment statistics.
     */
    public function statistics()
    {
        $user = Auth::user();

        $activeInvestments = $user->investments()->active()->count();
        $totalInvested = $user->investments()->sum('amount');
        $totalEarned = $user->investments()->sum('total_earned');
        $completedInvestments = $user->investments()->completed()->count();

        return response()->json([
            'active_investments' => $activeInvestments,
            'total_invested' => $totalInvested,
            'total_earned' => $totalEarned,
            'completed_investments' => $completedInvestments,
        ]);
    }
}
