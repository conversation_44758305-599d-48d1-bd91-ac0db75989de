<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Referral;
use App\Models\Transaction;
use App\Services\WelcomeBonusService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rules;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    /**
     * Show the registration page.
     */
    public function create(Request $request): Response
    {
        $referralCode = $request->query('ref');
        $referrer = null;

        if ($referralCode) {
            $referrer = User::findByReferralCode($referralCode);
        }

        return Inertia::render('Auth/register', [
            'referralCode' => $referralCode,
            'referrerName' => $referrer?->name,
        ]);
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|lowercase|email|max:255|unique:'.User::class,
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'referral_code' => 'nullable|string|max:20',
        ]);

        $welcomeBonusService = new WelcomeBonusService();

        $user = DB::transaction(function () use ($request, $welcomeBonusService) {
            // Find referrer if referral code is provided
            $referrer = null;
            if ($request->referral_code) {
                $referrer = User::findByReferralCode($request->referral_code);
            }

            // Create the user without welcome bonus initially
            $user = User::create([
                'name' => $request->name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'referred_by' => $referrer?->id,
            ]);

            // Generate referral code for the new user
            $user->generateReferralCode();

            // Credit welcome bonus using the service
            $welcomeBonusService->creditWelcomeBonus($user);

            // Create referral relationships if there's a referrer
            if ($referrer) {
                Referral::createReferralRelationships($user, $referrer);
            }

            return $user;
        });

        event(new Registered($user));
        Auth::login($user);

        return redirect()->intended(route('dashboard', absolute: false));
    }
}
