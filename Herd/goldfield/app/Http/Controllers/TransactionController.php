<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Inertia\Inertia;

class TransactionController extends Controller
{
    /**
     * Display a listing of the user's transactions.
     */
    public function index(Request $request)
    {
        $query = auth()->user()->transactions()->with(['investment', 'referral', 'giftCodeClaim']);

        // Filter by type if provided
        if ($request->filled('type') && in_array($request->type, Transaction::getTypes())) {
            $query->ofType($request->type);
        }

        // Filter by status if provided
        if ($request->filled('status') && in_array($request->status, Transaction::getStatuses())) {
            $query->withStatus($request->status);
        }

        // Filter by date range if provided
        if ($request->filled('start_date') && $request->filled('end_date')) {
            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();
            $query->dateRange($startDate, $endDate);
        }

        // Order by most recent first
        $transactions = $query->orderBy('created_at', 'desc')
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('Transactions/Index', [
            'transactions' => $transactions,
            'filters' => $request->only(['type', 'status', 'start_date', 'end_date']),
            'transactionTypes' => Transaction::getTypes(),
            'transactionStatuses' => Transaction::getStatuses(),
        ]);
    }

    /**
     * Display the specified transaction.
     */
    public function show(Transaction $transaction)
    {
        // Ensure user can only view their own transactions
        if ($transaction->user_id !== auth()->id()) {
            abort(403);
        }

        $transaction->load(['investment', 'referral', 'giftCodeClaim']);

        return Inertia::render('Transactions/Show', [
            'transaction' => $transaction,
        ]);
    }

    /**
     * Get transaction statistics for the authenticated user.
     */
    public function statistics(Request $request)
    {
        $user = auth()->user();

        // Get date range (default to current month)
        $startDate = $request->filled('start_date')
            ? Carbon::parse($request->start_date)->startOfDay()
            : Carbon::now()->startOfMonth();

        $endDate = $request->filled('end_date')
            ? Carbon::parse($request->end_date)->endOfDay()
            : Carbon::now()->endOfMonth();

        $statistics = [
            'total_transactions' => $user->transactions()->dateRange($startDate, $endDate)->count(),
            'completed_transactions' => $user->transactions()->dateRange($startDate, $endDate)->withStatus(Transaction::STATUS_COMPLETED)->count(),
            'pending_transactions' => $user->transactions()->dateRange($startDate, $endDate)->withStatus(Transaction::STATUS_PENDING)->count(),
            'failed_transactions' => $user->transactions()->dateRange($startDate, $endDate)->withStatus(Transaction::STATUS_FAILED)->count(),
            'total_income' => $user->transactions()->dateRange($startDate, $endDate)->whereIn('type', [
                Transaction::TYPE_DAILY_INCOME,
                Transaction::TYPE_REFERRAL_BONUS,
                Transaction::TYPE_GIFT_CODE,
                Transaction::TYPE_WELCOME_BONUS
            ])->withStatus(Transaction::STATUS_COMPLETED)->sum('amount'),
            'total_investments' => $user->transactions()->dateRange($startDate, $endDate)->ofType(Transaction::TYPE_INVESTMENT)
                ->withStatus(Transaction::STATUS_COMPLETED)->sum('amount'),
            'total_withdrawals' => $user->transactions()->dateRange($startDate, $endDate)->ofType(Transaction::TYPE_WITHDRAWAL)
                ->withStatus(Transaction::STATUS_COMPLETED)->sum('amount'),
            'by_type' => [],
        ];

        // Get breakdown by transaction type
        foreach (Transaction::getTypes() as $type) {
            $statistics['by_type'][$type] = [
                'count' => $user->transactions()->dateRange($startDate, $endDate)->ofType($type)->count(),
                'total_amount' => $user->transactions()->dateRange($startDate, $endDate)->ofType($type)->withStatus(Transaction::STATUS_COMPLETED)->sum('amount'),
            ];
        }

        return response()->json($statistics);
    }

    /**
     * Export transactions to CSV.
     */
    public function export(Request $request)
    {
        $query = auth()->user()->transactions()->with(['investment', 'referral', 'giftCodeClaim']);

        // Apply same filters as index
        if ($request->filled('type') && in_array($request->type, Transaction::getTypes())) {
            $query->ofType($request->type);
        }

        if ($request->filled('status') && in_array($request->status, Transaction::getStatuses())) {
            $query->withStatus($request->status);
        }

        if ($request->filled('start_date') && $request->filled('end_date')) {
            $startDate = Carbon::parse($request->start_date)->startOfDay();
            $endDate = Carbon::parse($request->end_date)->endOfDay();
            $query->dateRange($startDate, $endDate);
        }

        $transactions = $query->orderBy('created_at', 'desc')->get();

        $filename = 'transactions_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        // Build CSV content
        $csvContent = '';

        // CSV headers
        $headerRow = ['ID', 'Date', 'Type', 'Amount', 'Description', 'Status', 'Reference ID'];
        $csvContent .= implode(',', $headerRow) . "\n";

        // CSV data
        foreach ($transactions as $transaction) {
            $row = [
                $transaction->id,
                $transaction->created_at->format('Y-m-d H:i:s'),
                $transaction->type,
                $transaction->amount,
                '"' . str_replace('"', '""', $transaction->description) . '"',
                $transaction->status,
                $transaction->reference_id ?? '',
            ];
            $csvContent .= implode(',', $row) . "\n";
        }

        return response($csvContent, 200, $headers);
    }
}
