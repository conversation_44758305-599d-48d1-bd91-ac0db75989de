<?php

namespace App\Http\Controllers;

use App\Services\OtpayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Http\Controllers\Controller;
use Inertia\Inertia;

class PaymentController extends Controller
{
    protected $otpayService;

    public function __construct(OtpayService $otpayService)
    {
        $this->otpayService = $otpayService;
        $this->middleware('auth');
    }

    /**
     * Initialize a payment
     */
    public function initialize(Request $request)
    {
        try {
            $request->validate([
                'amount' => 'required|numeric|min:100', // minimum 100 kobo (₦1)
                'email' => 'required|email',
                'plan_id' => 'required_if:type,investment|exists:plans,id',
                'reference' => 'sometimes|string|unique:payments,reference',
                'callback_url' => 'sometimes|url',
                'type' => 'sometimes|in:investment,deposit',
                'metadata' => 'sometimes|array',
            ]);

            // Set default type to 'deposit' if not provided
            $paymentType = $request->type ?? 'deposit';

            $user = $request->user();

            if (!$user) {
                Log::error('Payment initialization failed: User not authenticated', [
                    'auth_check' => auth()->check(),
                    'auth_id' => auth()->id(),
                    'request' => $request->except(['_token', 'password'])
                ]);
                return response()->json([
                    'status' => 'error',
                    'message' => 'User not authenticated. Please log in and try again.',
                ], 401);
            }

            $reference = $request->reference ?? 'OTP' . time() . $user->id . rand(1000, 9999);
            $paymentType = $request->type ?? 'investment';

            // Create payment record
            $paymentData = [
                'reference' => $reference,
                'amount' => $request->amount, // Store amount in kobo
                'status' => 'pending',
                'payment_method' => 'otpay',
                'type' => $paymentType, // Add type to payment data
                'metadata' => array_merge($request->metadata ?? [], [
                    'type' => $paymentType,
                    'raw_request' => $request->except(['_token', 'password']) // Log the request for debugging
                ]),
            ];

            // Add plan_id only if this is an investment and plan_id is provided
            if ($paymentType === 'investment' && $request->has('plan_id')) {
                $paymentData['plan_id'] = $request->plan_id;
            }

            // For deposits, ensure plan_id is not set
            if ($paymentType === 'deposit') {
                $paymentData['plan_id'] = null;
            }

            $payment = $user->payments()->create($paymentData);

            // Prepare data for OTPay
            $otpayData = [
                'amount' => $request->amount, // Amount in kobo
                'email' => $user->email,
                'reference' => $reference,
                'callback_url' => $request->callback_url ?? route('payment.callback'),
                'metadata' => [
                    'payment_id' => $payment->id,
                    'user_id' => $user->id,
                    'type' => $paymentType,
                ],
            ];

            // Add plan_id to metadata if this is an investment
            if ($paymentType === 'investment' && $request->has('plan_id')) {
                $otpayData['metadata']['plan_id'] = $request->plan_id;
            }

            // For deposits, create virtual account instead of direct payment
            if ($paymentType === 'deposit') {
                Log::info('Creating virtual account for deposit', [
                    'user_id' => $user->id,
                    'payment_id' => $payment->id,
                    'user_email' => $user->email
                ]);

                // Check if user already has an active virtual account
                $existingVirtualAccount = $user->virtualAccounts()->active()->first();

                if ($existingVirtualAccount) {
                    Log::info('Using existing virtual account', [
                        'user_id' => $user->id,
                        'virtual_account_id' => $existingVirtualAccount->id,
                        'account_number' => $existingVirtualAccount->account_number
                    ]);

                    return response()->json([
                        'status' => 'success',
                        'message' => 'Virtual account details retrieved successfully',
                        'data' => [
                            'virtual_account' => [
                                'account_number' => $existingVirtualAccount->account_number,
                                'account_name' => $existingVirtualAccount->account_name,
                                'bank_name' => $existingVirtualAccount->bank_name,
                            ],
                            'payment_id' => $payment->id,
                            'reference' => $reference,
                            'redirect_url' => route('payment.virtual-account', [
                                'payment' => $payment->id,
                                'virtual_account' => $existingVirtualAccount->id
                            ])
                        ]
                    ]);
                }

                // Create new virtual account
                $virtualAccountData = [
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->phone ?? '***********', // Default phone if not provided
                    'bank_code' => [100033], // Default to Access Bank
                ];

                $response = $this->otpayService->createVirtualAccount($virtualAccountData);

                if ($response['success']) {
                    // Save virtual account to database
                    $virtualAccount = $user->virtualAccounts()->create([
                        'account_number' => $response['data']['accounts'][0]['number'],
                        'account_name' => $response['data']['accounts'][0]['name'],
                        'bank_name' => $response['data']['accounts'][0]['bank'],
                        'provider_reference' => $response['data']['accounts'][0]['ref'],
                        'provider' => 'otpay',
                        'metadata' => $response['data'],
                    ]);

                    Log::info('Virtual account created successfully', [
                        'user_id' => $user->id,
                        'virtual_account_id' => $virtualAccount->id,
                        'account_number' => $virtualAccount->account_number
                    ]);

                    return response()->json([
                        'status' => 'success',
                        'message' => 'Virtual account created successfully',
                        'data' => [
                            'virtual_account' => [
                                'account_number' => $virtualAccount->account_number,
                                'account_name' => $virtualAccount->account_name,
                                'bank_name' => $virtualAccount->bank_name,
                            ],
                            'payment_id' => $payment->id,
                            'reference' => $reference,
                            'redirect_url' => route('payment.virtual-account', [
                                'payment' => $payment->id,
                                'virtual_account' => $virtualAccount->id
                            ])
                        ]
                    ]);
                } else {
                    Log::error('Virtual account creation failed', [
                        'user_id' => $user->id,
                        'response' => $response
                    ]);

                    return response()->json([
                        'status' => 'error',
                        'message' => $response['message'] ?? 'Failed to create virtual account'
                    ], 500);
                }
            }

            // For non-deposit payments (investments), use regular payment initialization
            Log::info('Initializing OTPay payment', [
                'user_id' => $user->id,
                'payment_id' => $payment->id,
                'otpay_data' => $otpayData
            ]);

            // Initialize payment with OTPay
            $response = $this->otpayService->initializePayment($otpayData);

            Log::info('OTPay payment initialization response', [
                'user_id' => $user->id,
                'payment_id' => $payment->id,
                'response' => $response
            ]);

            if (!isset($response['success']) || !$response['success']) {
                $payment->update(['status' => 'failed']);
                Log::error('OTPay Payment Initialization Failed', [
                    'response' => $response,
                    'payment_id' => $payment->id,
                    'user_id' => $user->id,
                    'otpay_data' => $otpayData
                ]);
                return response()->json([
                    'status' => 'error',
                    'message' => $response['message'] ?? 'Failed to initialize payment',
                    'response' => $response
                ], 400);
            }

            // Extract the authorization URL from the response
            $authorizationUrl = $response['data']['authorization_url'] ??
                              ($response['data']['data']['authorization_url'] ?? null);

            if (!$authorizationUrl) {
                Log::error('OTPay Authorization URL Missing', [
                    'response' => $response,
                    'payment_id' => $payment->id,
                    'user_id' => $user->id,
                    'otpay_data' => $otpayData
                ]);
                return response()->json([
                    'status' => 'error',
                    'message' => 'Authorization URL not found in response',
                    'response' => $response
                ], 500);
            }

            // Update payment with OTPay reference if available
            $otpayReference = $response['data']['reference'] ??
                            ($response['data']['data']['reference'] ?? null);

            $payment->update([
                'provider_reference' => $otpayReference ?? $reference,
                'metadata' => array_merge($payment->metadata ?? [], [
                    'otpay_reference' => $otpayReference,
                    'authorization_url' => $authorizationUrl,
                    'raw_response' => $response
                ]),
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Payment initialized successfully',
                'data' => [
                    'authorization_url' => $authorizationUrl,
                    'reference' => $reference,
                    'payment_id' => $payment->id,
                ],
                'success' => true,
                'authorization_url' => $authorizationUrl,
                'reference' => $reference,
                'payment' => $payment->fresh()
            ]);

        } catch (\Exception $e) {
            Log::error('Payment Initialization Error: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'request' => $request->except(['_token', 'password'])
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while initializing payment: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle payment callback
     */
    public function callback(Request $request)
    {
        $reference = $request->query('reference');

        if (!$reference) {
            return redirect()->route('dashboard')->with('error', 'Invalid payment reference');
        }

        // Verify payment with OTPay
        $verification = $this->otpayService->verifyPayment($reference);

        Log::info('OTPay Payment Verification', [
            'reference' => $reference,
            'verification' => $verification
        ]);

        if (!$verification['success']) {
            Log::error('OTPay Payment Verification Failed', [
                'reference' => $reference,
                'verification' => $verification
            ]);
            return redirect()->route('dashboard')->with('error', 'Payment verification failed: ' . ($verification['message'] ?? 'Unknown error'));
        }

        // Check if the payment was successful in the verification response
        $paymentData = $verification['data']['data'] ?? [];
        if (($paymentData['status'] ?? '') !== 'success') {
            Log::error('OTPay Payment Not Successful', [
                'reference' => $reference,
                'payment_status' => $paymentData['status'] ?? 'unknown',
                'verification' => $verification
            ]);
            return redirect()->route('dashboard')->with('error', 'Payment was not successful. Status: ' . ($paymentData['status'] ?? 'unknown'));
        }

        $payment = \App\Models\Payment::where('reference', $reference)->first();

        if (!$payment) {
            return redirect()->route('dashboard')->with('error', 'Payment record not found');
        }

        // Skip if payment is already completed
        if ($payment->status === 'completed') {
            return redirect()->route('dashboard')->with('info', 'Payment was already processed');
        }

        // Get payment type from metadata or default to 'investment'
        $paymentType = $payment->metadata['type'] ?? 'investment';
        $user = $payment->user;

        // Update payment status
        $payment->update([
            'status' => 'completed',
            'paid_at' => now(),
            'metadata' => array_merge($payment->metadata ?? [], [
                'otpay_response' => $verification['data'],
                'verified_at' => now()->toDateTimeString(),
            ])
        ]);

        // Process the payment based on its type
        if ($paymentType === 'investment' && $payment->plan_id) {
            // Create investment record if not exists
            if (!$user->investments()->where('payment_id', $payment->id)->exists()) {
                $plan = $payment->plan;
                $user->investments()->create([
                    'plan_id' => $plan->id,
                    'amount' => $payment->amount,
                    'status' => 'active',
                    'payment_id' => $payment->id,
                    'start_date' => now(),
                    'end_date' => now()->addDays($plan->duration_days),
                    'expected_profit' => $payment->amount * ($plan->return_rate / 100),
                    'roi' => $plan->return_rate,
                ]);
            }
            return redirect()->route('investments.index')->with('success', 'Investment created successfully!');
        } else if ($paymentType === 'deposit') {
            // Update user's balance for deposit
            $user->increment('balance', $payment->amount);
            return redirect()->route('transactions.index')->with('success', 'Deposit completed successfully!');
        }

        return redirect()->route('dashboard')->with('success', 'Payment completed successfully!');
    }

    /**
     * Handle webhook notifications
     */
    public function webhook(Request $request)
    {
        $signature = $request->header('x-otpay-signature');
        $payload = $request->getContent();

        // Only verify signature if one is provided and we have a secret configured
        if ($signature && config('otpay.webhook_secret')) {
            if (!$this->otpayService->verifyWebhookSignature($payload, $signature)) {
                Log::error('OTPay Webhook: Invalid signature');
                return response()->json(['status' => 'error', 'message' => 'Invalid signature'], 400);
            }
        }

        $event = json_decode($payload, true);

        if ($event['event'] === 'charge.success') {
            $reference = $event['data']['reference'];
            $payment = \App\Models\Payment::where('reference', $reference)->first();

            if ($payment) {
                // Skip if payment is already completed
                if ($payment->status === 'completed') {
                    Log::info("OTPay Webhook: Payment {$reference} already processed");
                    return response()->json(['status' => 'success', 'message' => 'Payment already processed']);
                }

                // Update payment status
                $payment->update([
                    'status' => 'completed',
                    'paid_at' => now(),
                    'metadata' => array_merge($payment->metadata ?? [], [
                        'otpay_webhook' => $event,
                        'processed_at' => now()->toDateTimeString(),
                    ])
                ]);

                // Get payment type from metadata or default to 'investment'
                $paymentType = $payment->metadata['type'] ?? 'investment';
                $user = $payment->user;

                if ($paymentType === 'investment' && $payment->plan_id) {
                    // Create investment record if not exists
                    if (!$user->investments()->where('payment_id', $payment->id)->exists()) {
                        $plan = $payment->plan;
                        $user->investments()->create([
                            'plan_id' => $plan->id,
                            'amount' => $payment->amount,
                            'status' => 'active',
                            'payment_id' => $payment->id,
                            'start_date' => now(),
                            'end_date' => now()->addDays($plan->duration_days),
                            'expected_profit' => $payment->amount * ($plan->return_rate / 100),
                            'roi' => $plan->return_rate,
                        ]);
                        Log::info("OTPay Webhook: Created investment for payment {$reference}");
                    }
                } else if ($paymentType === 'deposit') {
                    // Update user's balance for deposit
                    $user->increment('balance', $payment->amount);
                    Log::info("OTPay Webhook: Updated balance for user {$user->id} with payment {$reference}");
                }
            } else {
                Log::warning("OTPay Webhook: Payment not found for reference {$reference}");
            }
        } else {
            Log::info("OTPay Webhook: Received unhandled event type: {$event['event']}");
        }

        return response()->json(['status' => 'success']);
    }

    /**
     * Show virtual account payment page
     */
    public function showVirtualAccount(Request $request, $paymentId, $virtualAccountId)
    {
        $user = $request->user();

        // Get the payment record
        $payment = $user->payments()->findOrFail($paymentId);

        // Get the virtual account
        $virtualAccount = $user->virtualAccounts()->findOrFail($virtualAccountId);

        return Inertia::render('Actions/VirtualAccountPayment', [
            'virtualAccount' => $virtualAccount,
            'payment' => $payment,
            'amount' => $payment->amount,
        ]);
    }
}
