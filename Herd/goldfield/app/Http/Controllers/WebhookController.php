<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\VirtualAccount;
use App\Services\OtpayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class WebhookController extends Controller
{
    protected $otpayService;

    public function __construct(OtpayService $otpayService)
    {
        $this->otpayService = $otpayService;
    }

    /**
     * Handle incoming OTPay webhook
     */
    public function handleWebhook(Request $request)
    {
        // Verify webhook signature
        $signature = $request->header('X-OTPay-Signature');
        $payload = $request->getContent();

        if (!$this->otpayService->verifyWebhookSignature($payload, $signature)) {
            Log::warning('OTPay Webhook: Invalid signature', [
                'ip' => $request->ip(),
                'headers' => $request->headers->all(),
                'payload' => $request->all(),
            ]);
            return response()->json(['status' => 'error', 'message' => 'Invalid signature'], 401);
        }

        $event = $request->input('event');
        $data = $request->input('data');

        try {
            switch ($event) {
                case 'payment.success':
                    return $this->handlePaymentSuccess($data);
                case 'virtual_account.credit':
                    return $this->handleVirtualAccountCredit($data);
                case 'transfer.success':
                    return $this->handleTransferSuccess($data);
                default:
                    Log::info("OTPay Webhook: Unhandled event type - {$event}", $data);
                    return response()->json(['status' => 'success', 'message' => 'Event received but not processed']);
            }
        } catch (\Exception $e) {
            Log::error('OTPay Webhook Error: ' . $e->getMessage(), [
                'exception' => $e,
                'event' => $event,
                'data' => $data
            ]);
            return response()->json(['status' => 'error', 'message' => $e->getMessage()], 500);
        }
    }

    /**
     * Handle successful payment event
     */
    protected function handlePaymentSuccess(array $data)
    {
        $payment = Payment::where('reference', $data['reference'])->firstOrFail();
        
        // Update payment status
        $payment->update([
            'status' => 'completed',
            'paid_at' => now(),
            'metadata' => array_merge($payment->metadata ?? [], [
                'otpay_response' => $data,
                'webhook_received_at' => now()->toDateTimeString(),
            ])
        ]);

        // TODO: Trigger any post-payment actions (e.g., send email, update user balance, etc.)
        
        return response()->json(['status' => 'success']);
    }

    /**
     * Handle virtual account credit event
     */
    protected function handleVirtualAccountCredit(array $data)
    {
        $accountNumber = $data['account_number'];
        $amount = $data['amount'];
        $reference = $data['reference'];
        
        // Find the virtual account
        $virtualAccount = VirtualAccount::where('account_number', $accountNumber)
            ->where('is_active', true)
            ->firstOrFail();

        // Create a payment record
        $payment = Payment::create([
            'user_id' => $virtualAccount->user_id,
            'amount' => $amount,
            'reference' => $reference,
            'status' => 'completed', // Assuming OTPay only sends webhook for successful transactions
            'payment_method' => 'virtual_account',
            'currency' => $data['currency'] ?? 'NGN',
            'metadata' => [
                'type' => 'deposit',
                'virtual_account_id' => $virtualAccount->id,
                'bank_name' => $virtualAccount->bank_name,
                'account_name' => $virtualAccount->account_name,
                'otpay_data' => $data,
            ]
        ]);

        // TODO: Update user balance or trigger other business logic
        // $user = $virtualAccount->user;
        // $user->increment('balance', $amount);
        
        return response()->json(['status' => 'success']);
    }

    /**
     * Handle successful transfer event
     */
    protected function handleTransferSuccess(array $data)
    {
        // TODO: Implement transfer success handling
        // This would be used if you're making transfers to other banks via OTPay
        
        Log::info('OTPay Transfer Success:', $data);
        return response()->json(['status' => 'success']);
    }
}
