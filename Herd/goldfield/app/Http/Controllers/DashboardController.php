<?php

namespace App\Http\Controllers;

use App\Services\DashboardCacheService;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DashboardController extends Controller
{
    protected DashboardCacheService $cacheService;

    public function __construct(DashboardCacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }

    /**
     * Display the dashboard with aggregated data.
     */
    public function index()
    {
        $user = Auth::user();

        try {
            $dashboardData = [
                'user' => $user,
                'statistics' => $this->cacheService->getCachedStatistics($user),
                'recent_transactions' => $this->cacheService->getCachedRecentTransactions($user),
                'active_investments' => $this->cacheService->getCachedActiveInvestments($user),
                'investment_packages' => $this->cacheService->getCachedInvestmentPackages(),
                'gift_code_status' => $this->cacheService->getCachedGiftCodeStatus($user),
                'referral_stats' => $this->cacheService->getCachedReferralStats($user),
            ];
        } catch (\Exception $e) {
            // Fallback data if cache service fails
            $dashboardData = [
                'user' => $user,
                'statistics' => [
                    'total_balance' => $user->balance ?? 0,
                    'total_earnings' => $user->total_earnings ?? 0,
                    'active_investments' => 0,
                    'total_referrals' => 0,
                ],
                'recent_transactions' => [],
                'active_investments' => [],
                'investment_packages' => $this->cacheService->getInvestmentPackages(),
                'gift_code_status' => [
                    'can_claim' => false,
                    'next_claim_time' => null,
                ],
                'referral_stats' => [
                    'total_referrals' => 0,
                    'total_earnings' => 0,
                ],
            ];
        }

        return Inertia::render('dashboard', $dashboardData);
    }

    /**
     * Get dashboard statistics API endpoint.
     */
    public function statistics()
    {
        $user = Auth::user();

        return response()->json([
            'statistics' => $this->cacheService->getCachedStatistics($user),
            'referral_stats' => $this->cacheService->getCachedReferralStats($user),
        ]);
    }

    /**
     * Get recent activities API endpoint.
     */
    public function activities()
    {
        $user = Auth::user();

        return response()->json([
            'recent_transactions' => $this->cacheService->getCachedRecentTransactions($user),
            'active_investments' => $this->cacheService->getCachedActiveInvestments($user),
        ]);
    }

    /**
     * Get comprehensive dashboard data API endpoint.
     */
    public function data()
    {
        $user = Auth::user();

        return response()->json([
            'user' => [
                'id' => $user->id,
                'name' => $user->name,
                'email' => $user->email,
                'balance' => $user->balance,
                'total_earnings' => $user->total_earnings,
                'referral_code' => $user->getReferralCode(),
                'referral_link' => $user->getReferralLink(),
            ],
            'statistics' => $this->cacheService->getCachedStatistics($user),
            'recent_transactions' => $this->cacheService->getCachedRecentTransactions($user),
            'active_investments' => $this->cacheService->getCachedActiveInvestments($user),
            'investment_packages' => $this->cacheService->getCachedInvestmentPackages(),
            'gift_code_status' => $this->cacheService->getCachedGiftCodeStatus($user),
            'referral_stats' => $this->cacheService->getCachedReferralStats($user),
        ]);
    }

    /**
     * Refresh dashboard data (clear cache and return fresh data).
     */
    public function refresh()
    {
        $user = Auth::user();

        // Clear user-specific cache
        $this->cacheService->invalidateUserCache($user);

        return response()->json([
            'message' => 'Dashboard data refreshed successfully',
            'data' => [
                'statistics' => $this->cacheService->getCachedStatistics($user),
                'recent_transactions' => $this->cacheService->getCachedRecentTransactions($user),
                'active_investments' => $this->cacheService->getCachedActiveInvestments($user),
                'gift_code_status' => $this->cacheService->getCachedGiftCodeStatus($user),
                'referral_stats' => $this->cacheService->getCachedReferralStats($user),
            ]
        ]);
    }
}
