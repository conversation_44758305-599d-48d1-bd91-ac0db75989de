<?php

namespace App\Http\Controllers;

use App\Models\VirtualAccount;
use App\Services\OtpayService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Inertia\Inertia;

class VirtualAccountController extends Controller
{
    protected $otpayService;

    public function __construct(OtpayService $otpayService)
    {
        $this->otpayService = $otpayService;
    }

    /**
     * Show the form for creating a new virtual account.
     */
    public function create()
    {
        return Inertia::render('VirtualAccount/Create');
    }

    /**
     * Store a newly created virtual account.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email',
            'bank_code' => 'sometimes|array',
            'bank_code.*' => 'integer',
        ]);

        try {
            // Create virtual account with OTPay
            $result = $this->otpayService->createVirtualAccount([
                'name' => $request->name,
                'email' => $request->email,
                'phone' => $request->phone,
                'bank_code' => $request->bank_code ?? [100033], // Default to Access Bank
            ]);

            if (!$result['success']) {
                throw new \Exception($result['message'] ?? 'Failed to create virtual account');
            }

            // Save virtual account to database
            $virtualAccount = VirtualAccount::create([
                'user_id' => Auth::id(),
                'account_number' => $result['data']['accounts'][0]['number'],
                'account_name' => $result['data']['accounts'][0]['name'],
                'bank_name' => $result['data']['accounts'][0]['bank'],
                'provider_reference' => $result['data']['accounts'][0]['ref'],
                'provider' => 'otpay',
                'metadata' => $result['data'],
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Virtual account created successfully',
                'data' => $virtualAccount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified virtual account.
     */
    public function show($id)
    {
        $virtualAccount = VirtualAccount::where('user_id', Auth::id())
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $virtualAccount
        ]);
    }

    /**
     * Get all virtual accounts for the authenticated user.
     */
    public function index()
    {
        $virtualAccounts = VirtualAccount::where('user_id', Auth::id())
            ->latest()
            ->get();

        return Inertia::render('VirtualAccount/Index', [
            'virtualAccounts' => $virtualAccounts
        ]);
    }
}
