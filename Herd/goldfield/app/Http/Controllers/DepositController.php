<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class DepositController extends Controller
{
    public function create()
    {
        return Inertia::render('Actions/Deposit', [
            'balance' => auth()->user()->balance ?? 0,
            'quickAmounts' => [5000, 10000, 20000, 50000, 100000, 200000],
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:100', // minimum 100 kobo (₦1)
            'payment_method' => 'required|string',
        ]);

        try {
            // Initialize payment with OTPay
            $paymentController = app(\App\Http\Controllers\PaymentController::class);
            $response = $paymentController->initialize(new Request([
                'amount' => $request->amount * 100, // Convert to kobo
                'email' => auth()->user()->email,
                'type' => 'deposit',
                'metadata' => [
                    'payment_method' => $request->payment_method,
                    'bank_name' => $request->bank_name ?? null,
                    'account_number' => $request->account_number ?? null,
                    'account_name' => $request->account_name ?? null,
                ]
            ]));

            $responseData = json_decode($response->getContent(), true);

            if (isset($responseData['authorization_url'])) {
                return redirect()->away($responseData['authorization_url']);
            }

            return redirect()->back()->with('error', $responseData['message'] ?? 'Failed to initialize payment');

        } catch (\Exception $e) {
            \Log::error('Deposit Error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while processing your deposit. Please try again.');
        }
    }

    public function history(Request $request)
    {
        $query = Transaction::where('user_id', Auth::id())
            ->where('type', 'deposit')
            ->orderBy('created_at', 'desc');

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('reference', 'like', "%{$search}%")
                  ->orWhereJsonContains('metadata->transaction_reference', $search);
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        if ($request->boolean('export')) {
            $deposits = $query->get();
            return $this->exportDeposits($deposits);
        }

        $deposits = $query->paginate(15);

        return Inertia::render('Actions/DepositHistory', [
            'deposits' => $deposits,
            'filters' => $request->only(['search', 'status', 'date_from', 'date_to']),
        ]);
    }

    private function exportDeposits($deposits)
    {
        $filename = 'deposits_' . now()->format('Y_m_d_H_i_s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($deposits) {
            $file = fopen('php://output', 'w');
            
            fputcsv($file, [
                'Reference',
                'Amount (₦)',
                'Status',
                'Payment Method',
                'Bank',
                'Account Number',
                'Account Name',
                'Transaction Reference',
                'Date',
            ]);

            foreach ($deposits as $deposit) {
                $metadata = $deposit->metadata ?? [];
                
                fputcsv($file, [
                    $deposit->reference,
                    number_format($deposit->amount / 100, 2),
                    ucfirst($deposit->status),
                    $metadata['payment_method'] ?? 'N/A',
                    $metadata['bank_name'] ?? 'N/A',
                    $metadata['account_number'] ?? 'N/A',
                    $metadata['account_name'] ?? 'N/A',
                    $metadata['transaction_reference'] ?? 'N/A',
                    $deposit->created_at->format('Y-m-d H:i:s'),
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    private function generateDepositDescription(Request $request): string
    {
        $method = ucfirst(str_replace('_', ' ', $request->payment_method));
        return $request->payment_method === 'bank_transfer' 
            ? "Deposit via {$method} - {$request->bank_name} ({$request->account_number})"
            : "Deposit via {$method}";
    }

    private function generateTransactionReference(): string
    {
        do {
            $reference = 'DEP' . strtoupper(substr(md5(uniqid()), 0, 10));
        } while (Transaction::where('reference', $reference)->exists());

        return $reference;
    }
}
