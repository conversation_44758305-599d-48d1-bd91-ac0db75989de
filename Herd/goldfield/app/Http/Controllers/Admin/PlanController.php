<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use App\Services\DashboardCacheService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Inertia\Inertia;

class PlanController extends Controller
{
    protected $cacheService;

    public function __construct(DashboardCacheService $cacheService)
    {
        $this->cacheService = $cacheService;
    }
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        // Debug: Log the raw SQL query
        \Log::info('Fetching plans from database');
        $plans = Plan::whereNull('deleted_at')
                    ->latest()
                    ->paginate(10);
        
        // Debug: Log the results
        \Log::info('Plans found: ' . $plans->count());
        \Log::info('Plans data: ' . json_encode($plans->items()));
        
        return Inertia::render('Admin/Plans/Index', [
            'plans' => $plans
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/Plans/Create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'min_deposit' => 'required|numeric|min:0',
            'max_deposit' => 'nullable|numeric|min:' . $request->input('min_deposit', 0),
            'type' => 'required|in:fixed,flexible',
            'return' => 'required_if:type,fixed|nullable|numeric|min:0',
            'return_type' => 'required_if:type,fixed|in:fixed,percent',
            'return_periods' => 'required_if:type,flexible|array',
            'duration' => 'required|integer|min:0',
            'features' => 'nullable|array',
            'is_active' => 'boolean',
            'referral_bonus' => 'nullable|numeric|min:0|max:100',
        ]);

        // If it's a fixed plan, make sure return_periods is null
        if ($validated['type'] === 'fixed') {
            $validated['return_periods'] = null;
        } else {
            // For flexible plans, ensure return is null
            $validated['return'] = null;
            $validated['return_type'] = null;
        }

        $plan = Plan::create($validated);
        
        // Clear the investment packages cache
        $this->cacheService->clearInvestmentPackagesCache();

        return redirect()
            ->route('admin.plans.index')
            ->with('message', [
                'type' => 'success',
                'text' => 'Plan created successfully.'
            ]);
    }

    /**
     * Display the specified resource.
     */
    public function show(Plan $plan)
    {
        return Inertia::render('Admin/Plans/Show', [
            'plan' => $plan
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Plan $plan)
    {
        return Inertia::render('Admin/Plans/Edit', [
            'plan' => $plan
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Plan $plan)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'min_deposit' => 'required|numeric|min:0',
            'max_deposit' => 'nullable|numeric|min:' . $request->input('min_deposit', 0),
            'type' => 'required|in:fixed,flexible',
            'return' => 'required_if:type,fixed|nullable|numeric|min:0',
            'return_type' => 'required_if:type,fixed|in:fixed,percent',
            'return_periods' => 'required_if:type,flexible|array',
            'duration' => 'required|integer|min:0',
            'features' => 'nullable|array',
            'is_active' => 'boolean',
            'referral_bonus' => 'nullable|numeric|min:0|max:100',
        ]);

        // If it's a fixed plan, make sure return_periods is null
        if ($validated['type'] === 'fixed') {
            $validated['return_periods'] = null;
        } else {
            // For flexible plans, ensure return is null
            $validated['return'] = null;
            $validated['return_type'] = null;
        }

        $plan->update($validated);
        
        // Clear the investment packages cache
        $this->cacheService->clearInvestmentPackagesCache();

        return redirect()->route('admin.plans.index')
            ->with('message', [
                'type' => 'success',
                'text' => 'Plan updated successfully.'
            ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Plan $plan)
    {
        $plan->delete();
        
        // Clear the investment packages cache
        $this->cacheService->clearInvestmentPackagesCache();

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Plan deleted successfully.'
            ]);
        }

        return redirect()
            ->route('admin.plans.index')
            ->with('message', [
                'type' => 'success',
                'text' => 'Plan deleted successfully.'
            ]);
    }
}
