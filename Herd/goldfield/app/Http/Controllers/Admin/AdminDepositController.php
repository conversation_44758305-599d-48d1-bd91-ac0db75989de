<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AdminDepositController extends Controller
{
    /**
     * Approve a deposit request.
     */
    public function approve(Request $request, Transaction $transaction)
    {
        $request->validate([
            'admin_notes' => 'nullable|string|max:500',
        ]);

        if ($transaction->type !== 'deposit' || $transaction->status !== 'pending') {
            return response()->json(['message' => 'Invalid deposit transaction'], 400);
        }

        try {
            DB::beginTransaction();

            // Update transaction status
            $transaction->update([
                'status' => 'completed',
                'description' => $transaction->description . ($request->admin_notes ? ' | Admin Notes: ' . $request->admin_notes : ''),
                'processed_at' => now(),
            ]);

            // Credit user's account
            $user = $transaction->user;
            $user->increment('balance', $transaction->amount);

            DB::commit();

            return response()->json([
                'message' => 'Deposit approved successfully',
                'transaction' => $transaction->fresh()
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json(['message' => 'Failed to approve deposit'], 500);
        }
    }

    /**
     * Reject a deposit request.
     */
    public function reject(Request $request, Transaction $transaction)
    {
        $request->validate([
            'admin_notes' => 'required|string|max:500',
        ]);

        if ($transaction->type !== 'deposit' || $transaction->status !== 'pending') {
            return response()->json(['message' => 'Invalid deposit transaction'], 400);
        }

        try {
            $transaction->update([
                'status' => 'failed',
                'description' => $transaction->description . ' | Admin Notes: ' . $request->admin_notes,
                'processed_at' => now(),
            ]);

            return response()->json([
                'message' => 'Deposit rejected successfully',
                'transaction' => $transaction->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json(['message' => 'Failed to reject deposit'], 500);
        }
    }

    /**
     * Get deposit details for admin review.
     */
    public function show(Transaction $transaction)
    {
        if ($transaction->type !== 'deposit') {
            return response()->json(['message' => 'Invalid deposit transaction'], 400);
        }

        $transaction->load('user:id,name,email');

        return response()->json([
            'transaction' => $transaction,
            'metadata' => $transaction->metadata,
            'user' => $transaction->user,
        ]);
    }
}
