<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Investment;
use App\Models\Transaction;
use App\Models\InvestmentPackage;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Carbon\Carbon;

class AdminDashboardController extends Controller
{
    /**
     * Show the admin dashboard.
     */
    public function index()
    {
        $stats = $this->getDashboardStats();
        $recentUsers = $this->getRecentUsers();
        $recentTransactions = $this->getRecentTransactions();
        $recentInvestments = $this->getRecentInvestments();

        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'recentUsers' => $recentUsers,
            'recentTransactions' => $recentTransactions,
            'recentInvestments' => $recentInvestments,
        ]);
    }

    /**
     * Get dashboard statistics.
     */
    private function getDashboardStats()
    {
        $totalUsers = User::count();
        $totalInvestments = Investment::sum('amount');
        $totalTransactions = Transaction::where('status', 'completed')->sum('amount');
        $activeInvestments = Investment::where('status', 'active')->count();

        // Users registered today
        $usersToday = User::whereDate('created_at', Carbon::today())->count();

        // Investments made today
        $investmentsToday = Investment::whereDate('created_at', Carbon::today())->sum('amount');

        // Pending withdrawals
        $pendingWithdrawals = Transaction::where('type', 'withdrawal')
            ->where('status', 'pending')
            ->count();

        return [
            'total_users' => $totalUsers,
            'total_investments' => $totalInvestments,
            'total_transactions' => $totalTransactions,
            'active_investments' => $activeInvestments,
            'users_today' => $usersToday,
            'investments_today' => $investmentsToday,
            'pending_withdrawals' => $pendingWithdrawals,
        ];
    }

    /**
     * Get recent users.
     */
    private function getRecentUsers()
    {
        return User::latest()
            ->take(10)
            ->get(['id', 'name', 'email', 'balance', 'total_earnings', 'created_at'])
            ->map(function ($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'balance' => $user->balance,
                    'total_earnings' => $user->total_earnings,
                    'created_at' => $user->created_at->format('M d, Y'),
                ];
            });
    }

    /**
     * Get recent transactions.
     */
    private function getRecentTransactions()
    {
        return Transaction::with('user:id,name,email')
            ->latest()
            ->take(10)
            ->get()
            ->map(function ($transaction) {
                return [
                    'id' => $transaction->id,
                    'user' => $transaction->user ? [
                        'name' => $transaction->user->name,
                        'email' => $transaction->user->email,
                    ] : null,
                    'amount' => $transaction->amount,
                    'type' => $transaction->type,
                    'status' => $transaction->status,
                    'description' => $transaction->description,
                    'created_at' => $transaction->created_at->format('M d, Y H:i'),
                ];
            });
    }

    /**
     * Get recent investments.
     */
    private function getRecentInvestments()
    {
        return Investment::with(['user:id,name,email', 'package:id,name'])
            ->latest()
            ->take(10)
            ->get()
            ->map(function ($investment) {
                return [
                    'id' => $investment->id,
                    'user' => $investment->user ? [
                        'name' => $investment->user->name,
                        'email' => $investment->user->email,
                    ] : null,
                    'package' => $investment->package ? [
                        'name' => $investment->package->name,
                    ] : null,
                    'amount' => $investment->amount,
                    'status' => $investment->status,
                    'daily_return' => $investment->daily_return,
                    'total_earned' => $investment->total_earned,
                    'created_at' => $investment->created_at->format('M d, Y'),
                ];
            });
    }
}
