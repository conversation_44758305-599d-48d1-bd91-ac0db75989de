<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Admin\AdminDepositController;
use App\Models\Transaction;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;
use Carbon\Carbon;

class DepositsController extends Controller
{
    /**
     * Get deposit statistics from both Transaction and Payment models.
     */
    public function getDepositStats()
    {
        // Stats from Transaction model (old system)
        $transactionStats = [
            'total_deposits' => Transaction::where('type', 'deposit')
                ->where('status', 'completed')
                ->sum('amount'),
            'pending_deposits' => Transaction::where('type', 'deposit')
                ->where('status', 'pending')
                ->count(),
            'approved_today' => Transaction::where('type', 'deposit')
                ->where('status', 'completed')
                ->whereDate('updated_at', today())
                ->count(),
            'total_today' => Transaction::where('type', 'deposit')
                ->where('status', 'completed')
                ->whereDate('updated_at', today())
                ->sum('amount'),
        ];

        // Stats from Payment model (new system)
        $paymentStats = [
            'total_deposits' => Payment::where('metadata->type', 'deposit')
                ->where('status', 'completed')
                ->sum('amount'),
            'pending_deposits' => Payment::where('metadata->type', 'deposit')
                ->where('status', 'pending')
                ->count(),
            'approved_today' => Payment::where('metadata->type', 'deposit')
                ->where('status', 'completed')
                ->whereDate('updated_at', today())
                ->count(),
            'total_today' => Payment::where('metadata->type', 'deposit')
                ->where('status', 'completed')
                ->whereDate('updated_at', today())
                ->sum('amount'),
        ];

        // Combine stats from both systems
        return [
            'total_deposits' => $transactionStats['total_deposits'] + $paymentStats['total_deposits'],
            'pending_deposits' => $transactionStats['pending_deposits'] + $paymentStats['pending_deposits'],
            'approved_today' => $transactionStats['approved_today'] + $paymentStats['approved_today'],
            'total_today' => $transactionStats['total_today'] + $paymentStats['total_today'],
        ];
    }

    /**
     * Get deposit statistics.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats()
    {
        $stats = $this->getDepositStats();
        return response()->json($stats);
    }

    /**
     * Display a listing of the deposits from both Transaction and Payment models.
     */
    public function index(Request $request)
    {
        $status = $request->query('status', 'pending'); // Default to pending

        // Get deposits from both Transaction and Payment models
        $transactionDeposits = collect();
        $paymentDeposits = collect();

        // Get Transaction deposits (old system)
        $transactionQuery = Transaction::with('user')
            ->where('type', 'deposit');

        if ($status !== 'all') {
            $transactionQuery->where('status', $status);
        }

        $transactionDeposits = $transactionQuery->latest()->get()->map(function ($transaction) {
            return [
                'id' => $transaction->id,
                'type' => 'transaction', // To distinguish between models
                'user' => [
                    'name' => $transaction->user->name,
                    'email' => $transaction->user->email,
                ],
                'amount' => $transaction->amount,
                'method' => 'bank_transfer', // Default for old transactions
                'status' => $transaction->status,
                'reference' => $transaction->reference ?? 'TXN-' . $transaction->id,
                'created_at' => $transaction->created_at->toISOString(),
                'metadata' => $transaction->metadata ?? [],
            ];
        });

        // Get Payment deposits (new system)
        $paymentQuery = Payment::with('user')
            ->where('metadata->type', 'deposit');

        if ($status !== 'all') {
            $paymentQuery->where('status', $status);
        }

        $paymentDeposits = $paymentQuery->latest()->get()->map(function ($payment) {
            $pendingDuration = Carbon::parse($payment->created_at)->diffInSeconds(now());

            return [
                'id' => $payment->id,
                'type' => 'payment', // To distinguish between models
                'user' => [
                    'name' => $payment->user->name,
                    'email' => $payment->user->email,
                ],
                'amount' => $payment->amount,
                'method' => $payment->payment_method ?? 'otpay',
                'status' => $payment->status,
                'reference' => $payment->reference,
                'created_at' => $payment->created_at->toISOString(),
                'metadata' => $payment->metadata ?? [],
                'pending_duration' => $pendingDuration,
                'eligible_for_approval' => $pendingDuration > 30 && $payment->status === 'pending',
            ];
        });

        // Combine and sort by created_at (limit to 50 for performance)
        $allDeposits = $transactionDeposits->concat($paymentDeposits)
            ->sortByDesc('created_at')
            ->take(50)
            ->values();

        $stats = $this->getDepositStats();

        // Return JSON only for explicit API requests (not Inertia requests)
        if (($request->wantsJson() || $request->ajax()) && !$request->header('X-Inertia')) {
            return response()->json([
                'data' => $allDeposits,
                'stats' => $stats,
            ]);
        }

        return Inertia::render('Admin/Deposits', [
            'deposits' => $allDeposits,
            'stats' => $stats,
        ]);
    }



    /**
     * Approve a deposit.
     */
    public function approve(Transaction $deposit)
    {
        $this->authorize('update', $deposit);

        if ($deposit->type !== 'deposit' || $deposit->status !== 'pending') {
            return response()->json(['message' => 'Invalid deposit transaction'], 400);
        }

        try {
            DB::beginTransaction();

            // Update transaction status
            $deposit->update([
                'status' => 'completed',
                'processed_at' => now(),
            ]);

            // Credit user's account
            $user = $deposit->user;
            $user->increment('balance', $deposit->amount);

            // Create transaction record if not exists
            if (!$deposit->metadata || !isset($deposit->metadata['transaction_created'])) {
                $user->transactions()->create([
                    'type' => 'deposit',
                    'amount' => $deposit->amount,
                    'status' => 'completed',
                    'description' => 'Deposit approved by admin',
                    'reference' => $deposit->reference,
                    'metadata' => [
                        'admin_approved' => true,
                        'admin_id' => auth('admin')->id(),
                    ]
                ]);
            }

            DB::commit();

            return response()->json([
                'message' => 'Deposit approved successfully',
                'deposit' => $deposit->fresh()
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error approving deposit: ' . $e->getMessage());
            return response()->json(['message' => 'Failed to approve deposit'], 500);
        }
    }

    /**
     * Reject a deposit.
     */
    public function reject(Request $request, Transaction $deposit)
    {
        $this->authorize('update', $deposit);

        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        if ($deposit->type !== 'deposit' || $deposit->status !== 'pending') {
            return response()->json(['message' => 'Invalid deposit transaction'], 400);
        }

        try {
            $deposit->update([
                'status' => 'rejected',
                'metadata' => array_merge($deposit->metadata ?? [], [
                    'rejection_reason' => $request->reason,
                    'rejected_by' => auth('admin')->id(),
                    'rejected_at' => now(),
                ]),
            ]);

            // Notify user about rejection
            // TODO: Implement notification

            return response()->json([
                'message' => 'Deposit rejected successfully',
                'deposit' => $deposit->fresh()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error rejecting deposit: ' . $e->getMessage());
            return response()->json(['message' => 'Failed to reject deposit'], 500);
        }
    }

    /**
     * Display the specified deposit.
     */
    public function show(Transaction $deposit)
    {
        $this->authorize('view', $deposit);

        $deposit->load('user');

        return response()->json([
            'deposit' => $deposit,
        ]);
    }

    /**
     * Approve any deposit - automatically detects if it's a Transaction or Payment.
     */
    public function approveAny(Request $request, $id)
    {
        try {
            // First, try to find it as a Payment
            $payment = Payment::where('id', $id)
                ->where('status', 'pending')
                ->where('metadata->type', 'deposit')
                ->first();

            if ($payment) {
                // It's a Payment deposit, use the Payment approval logic
                return $this->approvePayment($request, $id);
            }

            // If not found as Payment, try as Transaction
            $transaction = Transaction::where('id', $id)
                ->where('status', 'pending')
                ->where('type', 'deposit')
                ->first();

            if ($transaction) {
                // It's a Transaction deposit, delegate to the existing controller
                $adminDepositController = app(AdminDepositController::class);
                return $adminDepositController->approve($request, $transaction);
            }

            // Not found in either model
            return response()->json([
                'status' => 'error',
                'message' => 'Deposit not found or not eligible for approval',
            ], 404);

        } catch (\Exception $e) {
            \Log::error('Admin deposit approval failed', [
                'deposit_id' => $id,
                'admin_id' => auth('admin')->id(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to approve deposit: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reject any deposit - automatically detects if it's a Transaction or Payment.
     */
    public function rejectAny(Request $request, $id)
    {
        try {
            // First, try to find it as a Payment
            $payment = Payment::where('id', $id)
                ->where('status', 'pending')
                ->where('metadata->type', 'deposit')
                ->first();

            if ($payment) {
                // It's a Payment deposit, use the Payment rejection logic
                return $this->rejectPayment($request, $id);
            }

            // If not found as Payment, try as Transaction
            $transaction = Transaction::where('id', $id)
                ->where('status', 'pending')
                ->where('type', 'deposit')
                ->first();

            if ($transaction) {
                // It's a Transaction deposit, delegate to the existing controller
                // But first, map 'reason' to 'admin_notes' for compatibility
                $adminRequest = new \Illuminate\Http\Request();
                $adminRequest->replace([
                    'admin_notes' => $request->input('reason', 'Rejected by admin')
                ]);
                $adminRequest->setUserResolver($request->getUserResolver());

                $adminDepositController = app(AdminDepositController::class);
                $response = $adminDepositController->reject($adminRequest, $transaction);

                // Convert the response format to match frontend expectations
                if ($response->getStatusCode() === 200) {
                    $responseData = json_decode($response->getContent(), true);
                    return response()->json([
                        'status' => 'success',
                        'message' => $responseData['message'],
                        'deposit' => [
                            'id' => $transaction->id,
                            'user' => [
                                'name' => $transaction->user->name,
                                'email' => $transaction->user->email,
                            ],
                            'amount' => $transaction->amount,
                            'method' => 'transaction',
                            'status' => 'failed',
                            'reference' => $transaction->reference ?? 'N/A',
                            'created_at' => $transaction->created_at->toISOString(),
                        ]
                    ]);
                }

                return $response;
            }

            // Not found in either model
            return response()->json([
                'status' => 'error',
                'message' => 'Deposit not found or not eligible for rejection',
            ], 404);

        } catch (\Exception $e) {
            \Log::error('Admin deposit approval failed', [
                'deposit_id' => $id,
                'admin_id' => auth('admin')->id(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to approve deposit: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Approve a Payment deposit (new system).
     */
    public function approvePayment(Request $request, $paymentId)
    {
        try {
            DB::beginTransaction();

            // Find the payment
            $payment = Payment::with('user')
                ->where('id', $paymentId)
                ->where('status', 'pending')
                ->where('metadata->type', 'deposit')
                ->firstOrFail();

            // Check if payment is eligible for manual approval (pending for more than 30 seconds)
            $pendingDuration = Carbon::parse($payment->created_at)->diffInSeconds(now());
            if ($pendingDuration < 30) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Deposit must be pending for at least 30 seconds before manual approval',
                ], 400);
            }

            $user = $payment->user;

            // Update payment status
            $payment->update([
                'status' => 'completed',
                'paid_at' => now(),
                'metadata' => array_merge($payment->metadata ?? [], [
                    'approved_by' => auth('admin')->id(),
                    'approved_at' => now()->toDateTimeString(),
                    'approval_method' => 'manual_admin_approval',
                    'pending_duration_seconds' => $pendingDuration,
                ])
            ]);

            // Update user's balance
            $previousBalance = $user->balance;
            $user->increment('balance', $payment->amount);

            // Create transaction record
            $transaction = $user->transactions()->create([
                'type' => 'deposit',
                'amount' => $payment->amount,
                'status' => 'completed',
                'description' => 'Wallet deposit via virtual account (Admin Approved)',
                'reference' => $payment->reference,
                'metadata' => [
                    'payment_id' => $payment->id,
                    'payment_method' => 'virtual_account',
                    'source' => 'admin_approval',
                    'approved_by' => auth('admin')->id(),
                    'previous_balance' => $previousBalance,
                    'new_balance' => $user->fresh()->balance,
                ]
            ]);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Deposit approved successfully',
                'deposit' => [
                    'id' => $payment->id,
                    'user' => [
                        'name' => $user->name,
                        'email' => $user->email,
                    ],
                    'amount' => $payment->amount,
                    'method' => $payment->payment_method,
                    'status' => 'completed',
                    'reference' => $payment->reference,
                    'created_at' => $payment->created_at->toISOString(),
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            \Log::error('Admin payment approval failed', [
                'payment_id' => $paymentId,
                'admin_id' => auth('admin')->id(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to approve deposit: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Reject a Payment deposit (new system).
     */
    public function rejectPayment(Request $request, $paymentId)
    {
        $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            // Find the payment
            $payment = Payment::with('user')
                ->where('id', $paymentId)
                ->where('status', 'pending')
                ->where('metadata->type', 'deposit')
                ->firstOrFail();

            $user = $payment->user;
            $reason = $request->input('reason', 'Rejected by admin');

            // Update payment status
            $payment->update([
                'status' => 'failed',
                'metadata' => array_merge($payment->metadata ?? [], [
                    'rejected_by' => auth('admin')->id(),
                    'rejected_at' => now()->toDateTimeString(),
                    'rejection_reason' => $reason,
                    'rejection_method' => 'manual_admin_rejection',
                ])
            ]);

            // Log the admin rejection
            \Log::info('Admin rejected deposit manually', [
                'payment_id' => $payment->id,
                'user_id' => $user->id,
                'admin_id' => auth('admin')->id(),
                'amount' => $payment->amount,
                'reference' => $payment->reference,
                'reason' => $reason,
            ]);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Deposit rejected successfully',
                'deposit' => [
                    'id' => $payment->id,
                    'user' => [
                        'name' => $user->name,
                        'email' => $user->email,
                    ],
                    'amount' => $payment->amount,
                    'method' => $payment->payment_method,
                    'status' => 'failed',
                    'reference' => $payment->reference,
                    'created_at' => $payment->created_at->toISOString(),
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            \Log::error('Admin payment rejection failed', [
                'payment_id' => $paymentId,
                'admin_id' => auth('admin')->id(),
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to reject deposit: ' . $e->getMessage(),
            ], 500);
        }
    }
}
