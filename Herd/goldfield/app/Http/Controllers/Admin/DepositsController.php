<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Inertia\Inertia;

class DepositsController extends Controller
{
    /**
     * Display a listing of the deposits.
     */
    public function getDepositStats()
    {
        return [
            'total_deposits' => Transaction::where('type', 'deposit')
                ->where('status', 'completed')
                ->sum('amount'),
            'pending_deposits' => Transaction::where('type', 'deposit')
                ->where('status', 'pending')
                ->count(),
            'approved_today' => Transaction::where('type', 'deposit')
                ->where('status', 'completed')
                ->whereDate('updated_at', today())
                ->count(),
            'total_today' => Transaction::where('type', 'deposit')
                ->where('status', 'completed')
                ->whereDate('updated_at', today())
                ->sum('amount'),
        ];
    }

    /**
     * Get deposit statistics.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function stats()
    {
        $stats = $this->getDepositStats();
        return response()->json($stats);
    }

    /**
     * Display a listing of the deposits.
     */
    public function index(Request $request)
    {
        $status = $request->query('status', 'all');
        
        $query = Transaction::with('user')
            ->where('type', 'deposit');
            
        // Apply status filter if not 'all'
        if ($status !== 'all') {
            $query->where('status', $status);
        }
            
        $deposits = $query->latest()->paginate(15);

        $stats = $this->getDepositStats();

        // Return JSON for API requests
        if ($request->wantsJson() || $request->ajax()) {
            return response()->json([
                'data' => $deposits->items(),
                'links' => $deposits->toArray()['links']
            ]);
        }

        return Inertia::render('Admin/Deposits', [
            'deposits' => $deposits,
            'stats' => $stats,
        ]);
    }

    /**
     * Approve a deposit.
     */
    public function approve(Transaction $deposit)
    {
        $this->authorize('update', $deposit);

        if ($deposit->type !== 'deposit' || $deposit->status !== 'pending') {
            return response()->json(['message' => 'Invalid deposit transaction'], 400);
        }

        try {
            DB::beginTransaction();

            // Update transaction status
            $deposit->update([
                'status' => 'completed',
                'processed_at' => now(),
            ]);

            // Credit user's account
            $user = $deposit->user;
            $user->increment('balance', $deposit->amount);

            // Create transaction record if not exists
            if (!$deposit->metadata || !isset($deposit->metadata['transaction_created'])) {
                $user->transactions()->create([
                    'type' => 'deposit',
                    'amount' => $deposit->amount,
                    'status' => 'completed',
                    'description' => 'Deposit approved by admin',
                    'reference' => $deposit->reference,
                    'metadata' => [
                        'admin_approved' => true,
                        'admin_id' => auth('admin')->id(),
                    ]
                ]);
            }

            DB::commit();

            return response()->json([
                'message' => 'Deposit approved successfully',
                'deposit' => $deposit->fresh()
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Error approving deposit: ' . $e->getMessage());
            return response()->json(['message' => 'Failed to approve deposit'], 500);
        }
    }

    /**
     * Reject a deposit.
     */
    public function reject(Request $request, Transaction $deposit)
    {
        $this->authorize('update', $deposit);

        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        if ($deposit->type !== 'deposit' || $deposit->status !== 'pending') {
            return response()->json(['message' => 'Invalid deposit transaction'], 400);
        }

        try {
            $deposit->update([
                'status' => 'rejected',
                'metadata' => array_merge($deposit->metadata ?? [], [
                    'rejection_reason' => $request->reason,
                    'rejected_by' => auth('admin')->id(),
                    'rejected_at' => now(),
                ]),
            ]);

            // Notify user about rejection
            // TODO: Implement notification

            return response()->json([
                'message' => 'Deposit rejected successfully',
                'deposit' => $deposit->fresh()
            ]);
        } catch (\Exception $e) {
            \Log::error('Error rejecting deposit: ' . $e->getMessage());
            return response()->json(['message' => 'Failed to reject deposit'], 500);
        }
    }

    /**
     * Display the specified deposit.
     */
    public function show(Transaction $deposit)
    {
        $this->authorize('view', $deposit);

        $deposit->load('user');

        return response()->json([
            'deposit' => $deposit,
        ]);
    }
}
