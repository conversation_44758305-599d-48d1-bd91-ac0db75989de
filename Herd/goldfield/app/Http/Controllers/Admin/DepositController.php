<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Carbon\Carbon;

class DepositController extends Controller
{
    /**
     * Display pending deposits for admin approval
     */
    public function index(Request $request)
    {
        // Get pending deposits with user and virtual account information
        $pendingDeposits = Payment::with(['user', 'virtualAccount'])
            ->where('status', 'pending')
            ->where('metadata->type', 'deposit')
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($payment) {
                $pendingDuration = Carbon::parse($payment->created_at)->diffInSeconds(now());

                return [
                    'id' => $payment->id,
                    'user' => [
                        'id' => $payment->user->id,
                        'name' => $payment->user->name,
                        'email' => $payment->user->email,
                    ],
                    'amount' => $payment->amount,
                    'reference' => $payment->reference,
                    'status' => $payment->status,
                    'created_at' => $payment->created_at->toISOString(),
                    'virtual_account' => $payment->virtualAccount ? [
                        'id' => $payment->virtualAccount->id,
                        'account_number' => $payment->virtualAccount->account_number,
                        'account_name' => $payment->virtualAccount->account_name,
                        'bank_name' => $payment->virtualAccount->bank_name,
                    ] : null,
                    'pending_duration' => $pendingDuration,
                ];
            });

        return Inertia::render('Admin/PendingDeposits', [
            'pendingDeposits' => $pendingDeposits,
        ]);
    }

    /**
     * Approve a pending deposit manually
     */
    public function approve(Request $request, $paymentId)
    {
        try {
            DB::beginTransaction();

            // Find the payment
            $payment = Payment::with(['user', 'virtualAccount'])
                ->where('id', $paymentId)
                ->where('status', 'pending')
                ->where('metadata->type', 'deposit')
                ->firstOrFail();

            // Check if payment is eligible for manual approval (pending for more than 30 seconds)
            $pendingDuration = Carbon::parse($payment->created_at)->diffInSeconds(now());
            if ($pendingDuration < 30) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Deposit must be pending for at least 30 seconds before manual approval',
                ], 400);
            }

            $user = $payment->user;

            // Update payment status
            $payment->update([
                'status' => 'completed',
                'paid_at' => now(),
                'metadata' => array_merge($payment->metadata ?? [], [
                    'approved_by' => $request->user()->id,
                    'approved_at' => now()->toDateTimeString(),
                    'approval_method' => 'manual_admin_approval',
                    'pending_duration_seconds' => $pendingDuration,
                ])
            ]);

            // Update user's balance
            $previousBalance = $user->balance;
            $user->increment('balance', $payment->amount);

            // Create transaction record
            $transaction = $user->transactions()->create([
                'type' => 'deposit',
                'amount' => $payment->amount,
                'status' => 'completed',
                'description' => 'Wallet deposit via virtual account (Admin Approved)',
                'reference' => $payment->reference,
                'metadata' => [
                    'payment_id' => $payment->id,
                    'payment_method' => 'virtual_account',
                    'source' => 'admin_approval',
                    'approved_by' => $request->user()->id,
                    'previous_balance' => $previousBalance,
                    'new_balance' => $user->fresh()->balance,
                    'virtual_account_id' => $payment->virtualAccount?->id,
                ]
            ]);

            // Log the admin approval
            Log::info('Admin approved deposit manually', [
                'payment_id' => $payment->id,
                'user_id' => $user->id,
                'admin_id' => $request->user()->id,
                'amount' => $payment->amount,
                'reference' => $payment->reference,
                'pending_duration' => $pendingDuration,
                'transaction_id' => $transaction->id,
            ]);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Deposit approved successfully',
                'data' => [
                    'payment_id' => $payment->id,
                    'user_name' => $user->name,
                    'amount' => $payment->amount,
                    'reference' => $payment->reference,
                    'transaction_id' => $transaction->id,
                    'new_balance' => $user->fresh()->balance,
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Admin deposit approval failed', [
                'payment_id' => $paymentId,
                'admin_id' => $request->user()->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to approve deposit: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get pending deposits statistics for dashboard
     */
    public function statistics()
    {
        $now = now();

        $stats = [
            'total_pending' => Payment::where('status', 'pending')
                ->where('metadata->type', 'deposit')
                ->count(),

            'eligible_for_approval' => Payment::where('status', 'pending')
                ->where('metadata->type', 'deposit')
                ->where('created_at', '<=', $now->subSeconds(30))
                ->count(),

            'total_pending_amount' => Payment::where('status', 'pending')
                ->where('metadata->type', 'deposit')
                ->sum('amount'),

            'oldest_pending' => Payment::where('status', 'pending')
                ->where('metadata->type', 'deposit')
                ->orderBy('created_at', 'asc')
                ->first(),
        ];

        if ($stats['oldest_pending']) {
            $stats['oldest_pending_duration'] = Carbon::parse($stats['oldest_pending']->created_at)
                ->diffInSeconds($now);
        }

        return response()->json([
            'status' => 'success',
            'data' => $stats,
        ]);
    }

    /**
     * Bulk approve multiple deposits
     */
    public function bulkApprove(Request $request)
    {
        $request->validate([
            'payment_ids' => 'required|array|min:1',
            'payment_ids.*' => 'integer|exists:payments,id',
        ]);

        try {
            DB::beginTransaction();

            $approvedCount = 0;
            $errors = [];

            foreach ($request->payment_ids as $paymentId) {
                try {
                    // Find the payment
                    $payment = Payment::with(['user', 'virtualAccount'])
                        ->where('id', $paymentId)
                        ->where('status', 'pending')
                        ->where('metadata->type', 'deposit')
                        ->first();

                    if (!$payment) {
                        $errors[] = "Payment {$paymentId} not found or not eligible";
                        continue;
                    }

                    // Check if eligible for approval
                    $pendingDuration = Carbon::parse($payment->created_at)->diffInSeconds(now());
                    if ($pendingDuration < 30) {
                        $errors[] = "Payment {$paymentId} not pending long enough";
                        continue;
                    }

                    $user = $payment->user;

                    // Update payment status
                    $payment->update([
                        'status' => 'completed',
                        'paid_at' => now(),
                        'metadata' => array_merge($payment->metadata ?? [], [
                            'approved_by' => $request->user()->id,
                            'approved_at' => now()->toDateTimeString(),
                            'approval_method' => 'bulk_admin_approval',
                            'pending_duration_seconds' => $pendingDuration,
                        ])
                    ]);

                    // Update user balance and create transaction
                    $user->increment('balance', $payment->amount);

                    $user->transactions()->create([
                        'type' => 'deposit',
                        'amount' => $payment->amount,
                        'status' => 'completed',
                        'description' => 'Wallet deposit via virtual account (Bulk Admin Approved)',
                        'reference' => $payment->reference,
                        'metadata' => [
                            'payment_id' => $payment->id,
                            'payment_method' => 'virtual_account',
                            'source' => 'bulk_admin_approval',
                            'approved_by' => $request->user()->id,
                        ]
                    ]);

                    $approvedCount++;

                } catch (\Exception $e) {
                    $errors[] = "Payment {$paymentId}: " . $e->getMessage();
                }
            }

            DB::commit();

            Log::info('Bulk deposit approval completed', [
                'admin_id' => $request->user()->id,
                'approved_count' => $approvedCount,
                'errors' => $errors,
                'payment_ids' => $request->payment_ids,
            ]);

            return response()->json([
                'status' => 'success',
                'message' => "Successfully approved {$approvedCount} deposits",
                'data' => [
                    'approved_count' => $approvedCount,
                    'errors' => $errors,
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Bulk deposit approval failed', [
                'admin_id' => $request->user()->id,
                'payment_ids' => $request->payment_ids,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'status' => 'error',
                'message' => 'Bulk approval failed: ' . $e->getMessage(),
            ], 500);
        }
    }
}
