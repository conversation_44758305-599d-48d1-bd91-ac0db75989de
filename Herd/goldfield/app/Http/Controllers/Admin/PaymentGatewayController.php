<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentGatewaySetting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;
use Inertia\Inertia;

class PaymentGatewayController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $paymentGateways = PaymentGatewaySetting::all();
        
        return Inertia::render('Admin/PaymentGateways/Index', [
            'paymentGateways' => $paymentGateways->isEmpty() ? [] : $paymentGateways->map(function ($gateway) {
                return [
                    'id' => $gateway->id,
                    'gateway_name' => $gateway->gateway_name,
                    'gateway_type' => $gateway->gateway_type,
                    'is_active' => $gateway->is_active,
                    'credentials' => $gateway->credentials,
                ];
            })->toArray()
        ], [
            'component' => 'Admin/PaymentGateways/Index',
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return Inertia::render('Admin/PaymentGateways/Create', [], [
            'component' => 'Admin/PaymentGateways/Create',
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'gateway_name' => 'required|string|max:255',
            'gateway_type' => 'required|string|in:monnify,paystack,flutterwave',
            'is_active' => 'boolean',
            'api_key' => 'required_if:is_active,true',
            'secret_key' => 'required_if:is_active,true',
            'contract_code' => 'required_if:gateway_type,monnify',
        ]);

        $credentials = [
            'api_key' => $validated['api_key'] ?? null,
            'secret_key' => $validated['secret_key'] ?? null,
            'contract_code' => $validated['contract_code'] ?? null,
        ];

        unset($validated['api_key'], $validated['secret_key'], $validated['contract_code']);
        $validated['credentials'] = $credentials;

        $gateway = PaymentGatewaySetting::create($validated);

        return redirect()->route('admin.payment-gateways.index')
            ->with('success', 'Payment gateway created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        // Check if deleted_at column exists
        $hasDeletedAt = Schema::hasColumn('payment_gateway_settings', 'deleted_at');
        
        // Get the gateway, ignoring soft deletes if the column doesn't exist
        $gateway = $hasDeletedAt 
            ? PaymentGatewaySetting::findOrFail($id)
            : PaymentGatewaySetting::withoutGlobalScopes()->findOrFail($id);
        
        // Explicitly set the component path with correct case
        return Inertia::render('Admin/PaymentGateways/Edit', [
            'paymentGateway' => [
                'id' => $gateway->id,
                'gateway_name' => $gateway->gateway_name,
                'gateway_type' => $gateway->gateway_type,
                'is_active' => $gateway->is_active,
                'credentials' => $gateway->credentials,
            ]
        ], [
            'component' => 'Admin/PaymentGateways/Edit',
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $validated = $request->validate([
            'gateway_name' => 'required|string|max:255',
            'is_active' => 'boolean',
            'api_key' => 'required_if:is_active,true',
            'secret_key' => 'required_if:is_active,true',
            'contract_code' => 'required_if:is_active,true',
        ]);

        $gateway = PaymentGatewaySetting::findOrFail($id);

        $credentials = [
            'api_key' => $validated['api_key'] ?? null,
            'secret_key' => $validated['secret_key'] ?? null,
            'contract_code' => $validated['contract_code'] ?? null,
        ];

        unset($validated['api_key'], $validated['secret_key'], $validated['contract_code']);
        $validated['credentials'] = $credentials;

        $gateway->update($validated);

        return redirect()
            ->route('admin.payment-gateways.index')
            ->with('success', 'Payment gateway updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $gateway = PaymentGatewaySetting::findOrFail($id);
        $gateway->delete();

        if (request()->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Payment gateway deleted successfully.'
            ]);
        }

        return redirect()
            ->route('admin.payment-gateways.index')
            ->with('success', 'Payment gateway deleted successfully.');
    }
}
