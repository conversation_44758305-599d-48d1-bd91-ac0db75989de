<?php

namespace App\Http\Controllers;

use App\Models\Referral;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\ValidationException;

class ReferralController extends Controller
{
    /**
     * Get referral statistics for the authenticated user.
     */
    public function getStats(): JsonResponse
    {
        $user = Auth::user();
        $stats = $user->getReferralStats();

        return response()->json([
            'success' => true,
            'data' => [
                'referral_code' => $user->getReferralCode(),
                'referral_link' => url('/register?ref=' . $user->getReferralCode()),
                'stats' => $stats,
            ]
        ]);
    }

    /**
     * Get referral tree data for visualization.
     */
    public function getReferralTree(): JsonResponse
    {
        $user = Auth::user();

        // Get level 1 referrals
        $level1Referrals = $user->referralRelationships()
            ->where('level', 1)
            ->with(['referee' => function ($query) {
                $query->select('id', 'name', 'email', 'created_at');
            }])
            ->get()
            ->map(function ($referral) {
                return [
                    'id' => $referral->referee->id,
                    'name' => $referral->referee->name,
                    'email' => $referral->referee->email,
                    'joined_at' => $referral->referee->created_at,
                    'level' => 1,
                    'total_earned' => $referral->total_earned,
                    'bonus_percentage' => $referral->bonus_percentage,
                ];
            });

        // Get level 2 referrals
        $level2Referrals = $user->referralRelationships()
            ->where('level', 2)
            ->with(['referee' => function ($query) {
                $query->select('id', 'name', 'email', 'created_at');
            }])
            ->get()
            ->map(function ($referral) {
                return [
                    'id' => $referral->referee->id,
                    'name' => $referral->referee->name,
                    'email' => $referral->referee->email,
                    'joined_at' => $referral->referee->created_at,
                    'level' => 2,
                    'total_earned' => $referral->total_earned,
                    'bonus_percentage' => $referral->bonus_percentage,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'level_1' => $level1Referrals,
                'level_2' => $level2Referrals,
            ]
        ]);
    }

    /**
     * Get referral earnings history.
     */
    public function getEarningsHistory(Request $request): JsonResponse
    {
        $user = Auth::user();

        $query = $user->transactions()
            ->where('type', 'referral_bonus')
            ->orderBy('created_at', 'desc');

        // Apply filters if provided
        if ($request->has('level')) {
            $level = $request->input('level');
            $query->whereHas('referral', function ($q) use ($level) {
                $q->where('level', $level);
            });
        }

        if ($request->has('date_from')) {
            $query->whereDate('created_at', '>=', $request->input('date_from'));
        }

        if ($request->has('date_to')) {
            $query->whereDate('created_at', '<=', $request->input('date_to'));
        }

        $transactions = $query->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $transactions
        ]);
    }

    /**
     * Validate and process referral code during registration.
     */
    public function validateReferralCode(Request $request): JsonResponse
    {
        $request->validate([
            'referral_code' => 'required|string|max:20'
        ]);

        $referrer = User::findByReferralCode($request->referral_code);

        if (!$referrer) {
            throw ValidationException::withMessages([
                'referral_code' => ['Invalid referral code.']
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'referrer_name' => $referrer->name,
                'referrer_id' => $referrer->id,
            ]
        ]);
    }

    /**
     * Get referral bonus rates.
     */
    public function getBonusRates(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'level_1' => [
                    'percentage' => Referral::getBonusPercentage(1),
                    'description' => 'Direct referral bonus'
                ],
                'level_2' => [
                    'percentage' => Referral::getBonusPercentage(2),
                    'description' => 'Second level referral bonus'
                ]
            ]
        ]);
    }

    /**
     * Generate new referral code for user.
     */
    public function generateNewCode(): JsonResponse
    {
        $user = Auth::user();
        $newCode = $user->generateReferralCode();

        return response()->json([
            'success' => true,
            'data' => [
                'referral_code' => $newCode,
                'referral_link' => url('/register?ref=' . $newCode),
            ]
        ]);
    }

    /**
     * Get detailed referral analytics.
     */
    public function getAnalytics(): JsonResponse
    {
        $user = Auth::user();

        // Get monthly referral statistics (SQLite compatible)
        $monthlyStats = $user->referralRelationships()
            ->selectRaw("strftime('%Y', created_at) as year, strftime('%m', created_at) as month, COUNT(*) as count, SUM(total_earned) as earnings")
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->limit(12)
            ->get();

        // Get top performing referrals
        $topReferrals = $user->referralRelationships()
            ->with(['referee' => function ($query) {
                $query->select('id', 'name', 'email', 'created_at');
            }])
            ->orderBy('total_earned', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($referral) {
                return [
                    'referee_name' => $referral->referee->name,
                    'referee_email' => $referral->referee->email,
                    'level' => $referral->level,
                    'total_earned' => $referral->total_earned,
                    'joined_at' => $referral->referee->created_at,
                ];
            });

        // Get recent referral activities
        $recentActivities = $user->transactions()
            ->where('type', 'referral_bonus')
            ->with(['referral.referee' => function ($query) {
                $query->select('id', 'name');
            }])
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($transaction) {
                $referral = $transaction->referral;
                return [
                    'amount' => $transaction->amount,
                    'referee_name' => $referral && $referral->referee ? $referral->referee->name : 'Unknown',
                    'level' => $referral ? $referral->level : 0,
                    'created_at' => $transaction->created_at,
                    'description' => $transaction->description,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => [
                'monthly_stats' => $monthlyStats,
                'top_referrals' => $topReferrals,
                'recent_activities' => $recentActivities,
            ]
        ]);
    }

    /**
     * Get referral performance summary.
     */
    public function getPerformanceSummary(): JsonResponse
    {
        $user = Auth::user();

        // Calculate conversion rates and performance metrics
        $totalReferrals = $user->referralRelationships()->count();
        $activeReferrals = $user->referralRelationships()
            ->whereHas('referee.investments', function ($query) {
                $query->where('status', 'active');
            })
            ->count();

        $totalEarnings = $user->getTotalReferralEarnings();
        $thisMonthEarnings = $user->referralRelationships()
            ->whereMonth('created_at', now()->month)
            ->whereYear('created_at', now()->year)
            ->sum('total_earned');

        $lastMonthEarnings = $user->referralRelationships()
            ->whereMonth('created_at', now()->subMonth()->month)
            ->whereYear('created_at', now()->subMonth()->year)
            ->sum('total_earned');

        $growthRate = $lastMonthEarnings > 0
            ? (($thisMonthEarnings - $lastMonthEarnings) / $lastMonthEarnings) * 100
            : 0;

        // Average earnings per referral
        $avgEarningsPerReferral = $totalReferrals > 0 ? $totalEarnings / $totalReferrals : 0;

        return response()->json([
            'success' => true,
            'data' => [
                'total_referrals' => $totalReferrals,
                'active_referrals' => $activeReferrals,
                'conversion_rate' => $totalReferrals > 0 ? ($activeReferrals / $totalReferrals) * 100 : 0,
                'total_earnings' => $totalEarnings,
                'this_month_earnings' => $thisMonthEarnings,
                'last_month_earnings' => $lastMonthEarnings,
                'growth_rate' => $growthRate,
                'avg_earnings_per_referral' => $avgEarningsPerReferral,
            ]
        ]);
    }
}
