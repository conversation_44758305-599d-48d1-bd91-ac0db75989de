<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DailyGiftCode;
use App\Models\GiftCodeClaim;
use App\Models\Transaction;
use App\Models\GiftCode;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Carbon\Carbon;

class GiftCodeController extends Controller
{
    /**
     * Get today's gift code information
     */
    public function getTodaysCode()
    {
        $giftCode = DailyGiftCode::getTodaysCode();

        if (!$giftCode) {
            return response()->json([
                'success' => false,
                'message' => 'No gift code available for today',
                'data' => null
            ]);
        }

        $user = auth()->user();
        $hasClaimedToday = $giftCode->hasBeenClaimedByUser($user->id);

        return response()->json([
            'success' => true,
            'data' => [
                'code' => $giftCode->code,
                'amount' => $giftCode->amount,
                'valid_date' => $giftCode->valid_date->format('Y-m-d'),
                'is_claimable' => $giftCode->isClaimable(),
                'has_claimed' => $hasClaimedToday,
                'claims_remaining' => $giftCode->usage_limit - $giftCode->used_count,
                'gift_time' => DailyGiftCode::GIFT_CODE_TIME,
            ]
        ]);
    }

    /**
     * Claim today's gift code
     */
    public function claimGiftCode(Request $request)
    {
        $user = auth()->user();
        $giftCode = DailyGiftCode::getTodaysCode();

        // Validate gift code exists
        if (!$giftCode) {
            return response()->json([
                'success' => false,
                'message' => 'No gift code available for today'
            ], 404);
        }

        // Check if gift code is claimable
        if (!$giftCode->isClaimable()) {
            $giftDateTime = Carbon::parse($giftCode->valid_date->format('Y-m-d') . ' ' . DailyGiftCode::GIFT_CODE_TIME);

            if (Carbon::now()->lt($giftDateTime)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Gift code is not yet available. Please wait until 5:30 PM.'
                ], 400);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Gift code has expired for today'
                ], 400);
            }
        }

        // Check if user has already claimed today
        if ($giftCode->hasBeenClaimedByUser($user->id)) {
            return response()->json([
                'success' => false,
                'message' => 'You have already claimed today\'s gift code'
            ], 400);
        }

        // Check if gift code has reached usage limit
        if ($giftCode->hasReachedLimit()) {
            return response()->json([
                'success' => false,
                'message' => 'Gift code usage limit has been reached'
            ], 400);
        }

        try {
            DB::beginTransaction();

            // Create gift code claim
            $claim = GiftCodeClaim::create([
                'user_id' => $user->id,
                'gift_code_id' => $giftCode->id,
                'amount' => $giftCode->amount,
            ]);

            // Update user balance
            $user->increment('balance', $giftCode->amount);
            $user->increment('total_earnings', $giftCode->amount);

            // Create transaction record
            Transaction::create([
                'user_id' => $user->id,
                'type' => 'gift_code',
                'amount' => $giftCode->amount,
                'description' => "Daily gift code bonus - {$giftCode->code}",
                'reference_id' => $claim->id,
                'status' => 'completed',
            ]);

            // Increment gift code usage count
            $giftCode->increment('used_count');

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Gift code claimed successfully!',
                'data' => [
                    'amount' => $giftCode->amount,
                    'new_balance' => $user->fresh()->balance,
                    'claimed_at' => $claim->created_at->format('Y-m-d H:i:s')
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to claim gift code. Please try again.'
            ], 500);
        }
    }

    /**
     * Get user's gift code claim history
     */
    public function getClaimHistory()
    {
        $user = auth()->user();

        $claims = GiftCodeClaim::with('giftCode')
            ->where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $claims
        ]);
    }
}
