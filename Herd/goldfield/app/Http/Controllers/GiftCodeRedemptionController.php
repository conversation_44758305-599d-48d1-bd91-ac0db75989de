<?php

namespace App\Http\Controllers;

use App\Models\GiftCode;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class GiftCodeRedemptionController extends Controller
{
    /**
     * Show the gift code redemption form.
     */
    public function showRedeemForm()
    {
        return Inertia::render('GiftCode/Redeem');
    }

    /**
     * Redeem a gift code.
     */
    public function redeem(Request $request)
    {
        $request->validate([
            'code' => 'required|string|max:50',
        ]);

        $user = $request->user();
        $result = $user->applyGiftCode($request->code);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'amount' => $result['amount']
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => $result['message']
        ], 422);
    }

    /**
     * Get the user's gift code history.
     */
    public function history()
    {
        $giftCodes = Auth::user()->giftCodes()
            ->select('gift_codes.code', 'gift_code_user.amount', 'gift_code_user.created_at')
            ->get()
            ->map(function ($code) {
                return [
                    'code' => $code->code,
                    'amount' => (float) $code->pivot->amount,
                    'redeemed_at' => $code->pivot->created_at->toDateTimeString(),
                ];
            });

        return response()->json($giftCodes);
    }
}
