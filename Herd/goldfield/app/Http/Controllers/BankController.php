<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class BankController extends Controller
{
    /**
     * Verify bank account details using Paystack
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    /**
     * Verify a bank account
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verifyAccount(Request $request)
    {
        // Ensure user is authenticated
        if (!auth()->check()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated.'
            ], 401);
        }
        // Log the incoming request for debugging
        Log::info('Bank verification request received', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'data' => $request->all(),
            'headers' => $request->headers->all()
        ]);

        // Log the authenticated user
        Log::info('Authenticated user', [
            'user_id' => auth()->id(),
            'user_name' => auth()->user()?->name ?? 'Guest'
        ]);

        // Log the incoming request for debugging
        Log::info('Bank verification request received', [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'data' => $request->all(),
            'headers' => $request->headers->all(),
            'session' => session()->all(),
            'authenticated' => auth()->check()
        ]);

        // Validate the request
        $validated = $request->validate([
            'account_number' => 'required|string|max:20',
            'bank_code' => 'required|string|max:10',
        ]);

        // Get Paystack configuration
        $paystackConfig = config('services.paystack');
        $paystackSecretKey = $paystackConfig['secret_key'] ?? null;
        $paystackBaseUrl = rtrim($paystackConfig['payment_url'] ?? 'https://api.paystack.co', '/');
        
        // Log the Paystack configuration (without exposing the secret key)
        Log::debug('Paystack configuration', [
            'has_secret_key' => !empty($paystackSecretKey),
            'base_url' => $paystackBaseUrl,
            'config_keys' => array_keys($paystackConfig)
        ]);
        
        if (empty($paystackSecretKey)) {
            $error = 'Paystack secret key is not configured';
            Log::error($error, [
                'config' => $paystackConfig,
                'env' => [
                    'has_paystack_public' => !empty(env('PAYSTACK_PUBLIC_KEY')),
                    'has_paystack_secret' => !empty(env('PAYSTACK_SECRET_KEY')),
                ]
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Payment service is currently unavailable. Please try again later.',
                'debug' => config('app.debug') ? $error : null
            ], 500);
        }

        try {
            // Log the API request
            Log::debug('Calling Paystack API', [
                'url' => "{$paystackBaseUrl}/bank/resolve",
                'params' => [
                    'account_number' => $validated['account_number'],
                    'bank_code' => $validated['bank_code']
                ]
            ]);

            // Call Paystack API to resolve account number
            $response = Http::withToken($paystackSecretKey)
                ->timeout(15) // 15 seconds timeout
                ->retry(2, 100) // Retry twice with 100ms delay
                ->get("{$paystackBaseUrl}/bank/resolve", [
                    'account_number' => $validated['account_number'],
                    'bank_code' => $validated['bank_code'],
                ]);

            $responseData = $response->json();
            
            // Log the full response for debugging
            Log::debug('Paystack API response', [
                'status' => $response->status(),
                'response' => $responseData
            ]);

            if ($response->successful() && ($responseData['status'] ?? false) === true) {
                $accountName = $responseData['data']['account_name'] ?? null;
                
                if (!$accountName) {
                    throw new \Exception('Invalid account name received from Paystack');
                }

                $result = [
                    'success' => true,
                    'message' => 'Account details retrieved successfully',
                    'data' => [
                        'account_name' => $accountName,
                        'account_number' => $validated['account_number'],
                        'bank_code' => $validated['bank_code'],
                        'reference' => 'BANK-VERIFY-' . Str::upper(Str::random(10)),
                    ]
                ];

                Log::info('Bank account verified successfully', [
                    'account_number' => $validated['account_number'],
                    'bank_code' => $validated['bank_code']
                ]);

                return response()->json($result);
            }

            // Handle API errors
            $statusCode = $response->status();
            $errorMessage = $responseData['message'] ?? 'Could not verify account details. Please check and try again.';
            
            // Special handling for common errors
            if ($statusCode === 400) {
                $errorMessage = 'Invalid bank account details. Please check and try again.';
            } elseif ($statusCode === 401) {
                $errorMessage = 'Authentication failed. Please contact support.';
                Log::error('Paystack authentication failed', [
                    'status' => $statusCode,
                    'response' => $responseData
                ]);
            } elseif ($statusCode === 404) {
                $errorMessage = 'Bank account not found. Please check the details and try again.';
            }

            // Log the error for debugging
            Log::error('Paystack API error', [
                'status' => $statusCode,
                'response' => $responseData,
                'request' => $validated
            ]);

            return response()->json([
                'success' => false,
                'message' => $errorMessage,
                'debug' => config('app.debug') ? $responseData : null
            ], 422);

        } catch (\Exception $e) {
            $errorId = uniqid('ERR-', true);
            $errorDetails = [
                'exception' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'code' => $e->getCode(),
                    'trace' => config('app.debug') ? $e->getTraceAsString() : null
                ],
                'request' => [
                    'method' => $request->method(),
                    'url' => $request->fullUrl(),
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'input' => $request->except(['_token', 'password'])
                ],
                'config' => [
                    'app_debug' => config('app.debug'),
                    'app_env' => config('app.env'),
                    'paystack_configured' => !empty(config('services.paystack.secret_key'))
                ]
            ];

            Log::error("Bank verification error [{$errorId}]: " . $e->getMessage(), $errorDetails);

            // Create a user-friendly error message
            $userMessage = 'An error occurred while verifying your account. Please try again.';
            if (config('app.debug')) {
                $userMessage = $e->getMessage();
            }

            return response()->json([
                'success' => false,
                'message' => $userMessage,
                'reference' => $errorId,
                'debug' => config('app.debug') ? [
                    'error' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'type' => get_class($e)
                ] : null
            ], 500);
        }
    }
}
