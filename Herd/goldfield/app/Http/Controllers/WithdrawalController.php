<?php

namespace App\Http\Controllers;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\ValidationException;

class WithdrawalController extends Controller
{
    // Minimum withdrawal amount in Naira
    const MINIMUM_WITHDRAWAL = 1000;

    // Withdrawal charge percentage
    const WITHDRAWAL_CHARGE_PERCENTAGE = 10;

    /**
     * Request a withdrawal.
     */
    public function requestWithdrawal(Request $request)
    {
        $request->validate([
            'amount' => [
                'required',
                'numeric',
                'min:' . self::MINIMUM_WITHDRAWAL,
            ],
        ]);

        $user = Auth::user();
        $requestedAmount = $request->amount;

        // Check if user has sufficient balance
        if ($user->balance < $requestedAmount) {
            throw ValidationException::withMessages([
                'amount' => ['Insufficient balance. Your current balance is ₦' . number_format($user->balance, 2)]
            ]);
        }

        // Calculate withdrawal charge (10%)
        $withdrawalCharge = $requestedAmount * (self::WITHDRAWAL_CHARGE_PERCENTAGE / 100);
        $netAmount = $requestedAmount - $withdrawalCharge;

        try {
            DB::beginTransaction();

            // Create withdrawal transaction
            $transaction = Transaction::create([
                'user_id' => $user->id,
                'type' => Transaction::TYPE_WITHDRAWAL,
                'amount' => $requestedAmount,
                'description' => "Withdrawal request - Amount: ₦{$requestedAmount}, Charge: ₦{$withdrawalCharge}, Net: ₦{$netAmount}",
                'status' => Transaction::STATUS_PENDING,
            ]);

            // Deduct the full amount from user balance immediately (including charge)
            $user->decrement('balance', $requestedAmount);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Withdrawal request submitted successfully',
                'data' => [
                    'transaction_id' => $transaction->id,
                    'requested_amount' => $requestedAmount,
                    'withdrawal_charge' => $withdrawalCharge,
                    'net_amount' => $netAmount,
                    'status' => $transaction->status,
                    'remaining_balance' => $user->fresh()->balance,
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to process withdrawal request',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get withdrawal history for the authenticated user.
     */
    public function getWithdrawalHistory(Request $request)
    {
        $user = Auth::user();

        $query = $user->transactions()
            ->ofType(Transaction::TYPE_WITHDRAWAL)
            ->orderBy('created_at', 'desc');

        // Apply status filter if provided
        if ($request->has('status') && $request->status !== '') {
            $query->withStatus($request->status);
        }

        // Apply date range filter if provided
        if ($request->has('start_date') && $request->has('end_date')) {
            $query->dateRange($request->start_date, $request->end_date);
        }

        $withdrawals = $query->paginate(10);

        return response()->json([
            'success' => true,
            'data' => $withdrawals->items(),
            'pagination' => [
                'current_page' => $withdrawals->currentPage(),
                'last_page' => $withdrawals->lastPage(),
                'per_page' => $withdrawals->perPage(),
                'total' => $withdrawals->total(),
            ]
        ]);
    }

    /**
     * Get withdrawal statistics for the authenticated user.
     */
    public function getWithdrawalStats()
    {
        $user = Auth::user();

        $stats = [
            'total_withdrawals' => $user->transactions()
                ->ofType(Transaction::TYPE_WITHDRAWAL)
                ->withStatus(Transaction::STATUS_COMPLETED)
                ->sum('amount'),

            'pending_withdrawals' => $user->transactions()
                ->ofType(Transaction::TYPE_WITHDRAWAL)
                ->withStatus(Transaction::STATUS_PENDING)
                ->sum('amount'),

            'failed_withdrawals' => $user->transactions()
                ->ofType(Transaction::TYPE_WITHDRAWAL)
                ->withStatus(Transaction::STATUS_FAILED)
                ->sum('amount'),

            'withdrawal_count' => $user->transactions()
                ->ofType(Transaction::TYPE_WITHDRAWAL)
                ->count(),

            'current_balance' => $user->balance,
            'minimum_withdrawal' => self::MINIMUM_WITHDRAWAL,
            'withdrawal_charge_percentage' => self::WITHDRAWAL_CHARGE_PERCENTAGE,
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Calculate withdrawal details (amount after charges).
     */
    public function calculateWithdrawal(Request $request)
    {
        $request->validate([
            'amount' => [
                'required',
                'numeric',
                'min:' . self::MINIMUM_WITHDRAWAL,
            ],
        ]);

        $requestedAmount = $request->amount;
        $withdrawalCharge = $requestedAmount * (self::WITHDRAWAL_CHARGE_PERCENTAGE / 100);
        $netAmount = $requestedAmount - $withdrawalCharge;

        return response()->json([
            'success' => true,
            'data' => [
                'requested_amount' => $requestedAmount,
                'withdrawal_charge' => $withdrawalCharge,
                'net_amount' => $netAmount,
                'charge_percentage' => self::WITHDRAWAL_CHARGE_PERCENTAGE,
            ]
        ]);
    }

    /**
     * Admin function to process (approve/reject) withdrawal requests.
     * This would typically be in an admin controller, but included here for completeness.
     */
    public function processWithdrawal(Request $request, $transactionId)
    {
        $request->validate([
            'action' => 'required|in:approve,reject',
            'admin_notes' => 'nullable|string|max:500',
        ]);

        $transaction = Transaction::where('id', $transactionId)
            ->ofType(Transaction::TYPE_WITHDRAWAL)
            ->withStatus(Transaction::STATUS_PENDING)
            ->firstOrFail();

        try {
            DB::beginTransaction();

            if ($request->action === 'approve') {
                // Mark transaction as completed
                $transaction->update([
                    'status' => Transaction::STATUS_COMPLETED,
                    'description' => $transaction->description . ' | Admin Notes: ' . ($request->admin_notes ?? 'Approved')
                ]);

                $message = 'Withdrawal approved successfully';
            } else {
                // Mark transaction as failed and refund the amount
                $transaction->update([
                    'status' => Transaction::STATUS_FAILED,
                    'description' => $transaction->description . ' | Admin Notes: ' . ($request->admin_notes ?? 'Rejected')
                ]);

                // Refund the amount to user balance
                $transaction->user->increment('balance', $transaction->amount);

                $message = 'Withdrawal rejected and amount refunded';
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => [
                    'transaction_id' => $transaction->id,
                    'status' => $transaction->status,
                    'user_balance' => $transaction->user->fresh()->balance,
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Failed to process withdrawal',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
