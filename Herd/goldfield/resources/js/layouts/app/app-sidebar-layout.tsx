import { AppBottomNav } from '@/components/app-bottom-nav';
import { AppContent } from '@/components/app-content';
import { AppShell } from '@/components/app-shell';
import { AppSidebar } from '@/components/app-sidebar';
import { AppSidebarHeader } from '@/components/app-sidebar-header';
import { type BreadcrumbItem } from '@/types';
import { type PropsWithChildren } from 'react';

export default function AppSidebarLayout({ children, breadcrumbs = [] }: PropsWithChildren<{ breadcrumbs?: BreadcrumbItem[] }>) {
    return (
        <AppShell variant="sidebar">
            <AppSidebar className="md:block hidden" />
            <AppContent variant="sidebar" className="overflow-x-hidden pb-16 md:pb-0">
                <AppSidebarHeader breadcrumbs={breadcrumbs} />
                <div className="min-h-[calc(100vh-4rem)] md:min-h-screen">
                    {children}
                </div>
            </AppContent>
            <AppBottomNav />
        </AppShell>
    );
}
