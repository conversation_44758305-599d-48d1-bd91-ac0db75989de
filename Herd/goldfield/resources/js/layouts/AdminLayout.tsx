import { Button } from '@/components/ui/button-new';
import { cn } from '@/lib/utils';
import { Link, router } from '@inertiajs/react';
import { useState } from 'react';
import { Toaster } from '@/components/ui/toaster';
import {
    Bell,
    ChevronDown,
    CreditCard,
    DollarSign,
    Gift,
    LayoutDashboard,
    LogOut,
    Menu,
    Package,
    Search,
    Settings,
    Shield,
    TrendingUp,
    UserCheck,
    Users,
    Wallet,
    X,
} from 'lucide-react';

interface AdminLayoutProps {
    children: React.ReactNode;
    title?: string;
}

interface SidebarItem {
    name: string;
    href: string;
    icon: React.ComponentType<{ className?: string }>;
    current?: boolean;
    badge?: number;
}

const navigation: SidebarItem[] = [
    { name: 'Dashboard', href: 'admin.dashboard', icon: LayoutDashboard, current: true },
    { name: 'Users', href: 'admin.users', icon: Users },
    { name: 'Plans', href: 'admin.plans.index', icon: Package },
    { name: 'Investments', href: 'admin.investments', icon: TrendingUp },
    { name: 'Transactions', href: 'admin.transactions', icon: DollarSign },
    { name: 'Deposits', href: 'admin.deposits', icon: Wallet },
    { name: 'Withdrawals', href: 'admin.withdrawals', icon: CreditCard, badge: 3 },
    { name: 'Payment Gateways', href: 'admin.payment-gateways.index', icon: CreditCard },
    { name: 'Gift Codes', href: 'admin.gift-codes', icon: Gift },
    { name: 'Referrals', href: 'admin.referrals', icon: UserCheck },
    { name: 'Settings', href: 'admin.settings', icon: Settings },
];

export default function AdminLayout({ children, title = 'Admin Dashboard' }: AdminLayoutProps) {
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [userMenuOpen, setUserMenuOpen] = useState(false);
    const [notificationOpen, setNotificationOpen] = useState(false);

    const handleLogout = () => {
        router.post(route('admin.logout'));
    };

    const currentRoute = route().current();

    // Mock notifications data
    const notifications = [
        {
            id: 1,
            title: 'New Withdrawal Request',
            message: 'Adebayo Ogundimu requested withdrawal of ₦850,000',
            time: '2 minutes ago',
            type: 'withdrawal',
            unread: true
        },
        {
            id: 2,
            title: 'New User Registration',
            message: 'Fatima Abdullahi just registered with referral code GF12AB34CD',
            time: '15 minutes ago',
            type: 'user',
            unread: true
        },
        {
            id: 3,
            title: 'Investment Completed',
            message: 'Chinedu Okoro\'s Gold Plan investment has completed',
            time: '1 hour ago',
            type: 'investment',
            unread: false
        },
        {
            id: 4,
            title: 'New Deposit',
            message: 'Aisha Mohammed deposited ₦1,200,000',
            time: '2 hours ago',
            type: 'deposit',
            unread: false
        },
        {
            id: 5,
            title: 'Gift Code Redeemed',
            message: 'Gift code WELCOME2025 was redeemed by Ibrahim Yusuf',
            time: '3 hours ago',
            type: 'gift',
            unread: false
        }
    ];

    const unreadCount = notifications.filter(n => n.unread).length;

    const getNotificationIcon = (type: string) => {
        switch (type) {
            case 'withdrawal':
                return '💳';
            case 'deposit':
                return '💰';
            case 'user':
                return '👤';
            case 'investment':
                return '📈';
            case 'gift':
                return '🎁';
            default:
                return '🔔';
        }
    };

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Mobile sidebar overlay */}
            {sidebarOpen && (
                <div className="fixed inset-0 z-40 lg:hidden" onClick={() => setSidebarOpen(false)}>
                    <div className="bg-opacity-75 fixed inset-0 bg-gray-600" />
                </div>
            )}

            {/* Mobile sidebar */}
            <div
                className={cn(
                    'fixed inset-y-0 left-0 z-50 w-64 transform bg-white shadow-lg transition-transform duration-300 ease-in-out lg:hidden',
                    sidebarOpen ? 'translate-x-0' : '-translate-x-full',
                )}
            >
                <div className="flex h-16 items-center justify-between border-b border-gray-200 px-4">
                    <div className="flex items-center space-x-3">
                        <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500 to-blue-600">
                            <Shield className="h-5 w-5 text-white" />
                        </div>
                        <span className="text-lg font-semibold text-gray-900">Admin Panel</span>
                    </div>
                    <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(false)}>
                        <X className="h-5 w-5" />
                    </Button>
                </div>
                <SidebarContent currentRoute={currentRoute} />
            </div>

            {/* Desktop sidebar */}
            <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
                <div className="flex flex-grow flex-col border-r border-gray-200 bg-white shadow-sm">
                    <div className="flex h-16 items-center border-b border-gray-200 px-4">
                        <div className="flex items-center space-x-3">
                            <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-purple-500 to-blue-600">
                                <Shield className="h-5 w-5 text-white" />
                            </div>
                            <span className="text-lg font-semibold text-gray-900">Admin Panel</span>
                        </div>
                    </div>
                    <SidebarContent currentRoute={currentRoute} />
                </div>
            </div>

            {/* Main content */}
            <div className="lg:pl-64">
                {/* Top navigation */}
                <div className="sticky top-0 z-10 border-b border-gray-200 bg-white shadow-sm">
                    <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
                        <div className="flex items-center space-x-4">
                            <Button variant="ghost" size="sm" className="lg:hidden" onClick={() => setSidebarOpen(true)}>
                                <Menu className="h-5 w-5" />
                            </Button>
                            <h1 className="text-xl font-semibold text-gray-900">{title}</h1>
                        </div>

                        <div className="flex items-center space-x-4">
                            {/* Search */}
                            <div className="hidden md:block">
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                                    <input
                                        type="text"
                                        placeholder="Search..."
                                        className="rounded-lg border border-gray-300 py-2 pr-4 pl-10 text-sm focus:border-transparent focus:ring-2 focus:ring-purple-500 focus:outline-none"
                                    />
                                </div>
                            </div>

                            {/* Notifications */}
                            <div className="relative">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="relative"
                                    onClick={() => setNotificationOpen(!notificationOpen)}
                                >
                                    <Bell className="w-5 h-5" />
                                    {unreadCount > 0 && (
                                        <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                            {unreadCount}
                                        </span>
                                    )}
                                </Button>

                                {notificationOpen && (
                                    <>
                                        <div
                                            className="fixed inset-0 z-40"
                                            onClick={() => setNotificationOpen(false)}
                                        />
                                        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-hidden">
                                            <div className="px-4 py-3 border-b border-gray-100">
                                                <div className="flex items-center justify-between">
                                                    <h3 className="text-sm font-medium text-gray-900">Notifications</h3>
                                                    {unreadCount > 0 && (
                                                        <span className="text-xs text-blue-600 font-medium">
                                                            {unreadCount} new
                                                        </span>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="max-h-80 overflow-y-auto">
                                                {notifications.map((notification) => (
                                                    <div
                                                        key={notification.id}
                                                        className={`px-4 py-3 border-b border-gray-50 hover:bg-gray-50 cursor-pointer ${
                                                            notification.unread ? 'bg-blue-50' : ''
                                                        }`}
                                                    >
                                                        <div className="flex items-start space-x-3">
                                                            <div className="flex-shrink-0 text-lg">
                                                                {getNotificationIcon(notification.type)}
                                                            </div>
                                                            <div className="flex-1 min-w-0">
                                                                <div className="flex items-center justify-between">
                                                                    <p className="text-sm font-medium text-gray-900 truncate">
                                                                        {notification.title}
                                                                    </p>
                                                                    {notification.unread && (
                                                                        <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0 ml-2"></div>
                                                                    )}
                                                                </div>
                                                                <p className="text-sm text-gray-600 mt-1">
                                                                    {notification.message}
                                                                </p>
                                                                <p className="text-xs text-gray-400 mt-1">
                                                                    {notification.time}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                            <div className="px-4 py-3 border-t border-gray-100 bg-gray-50">
                                                <button className="text-sm text-blue-600 hover:text-blue-700 font-medium w-full text-center">
                                                    View all notifications
                                                </button>
                                            </div>
                                        </div>
                                    </>
                                )}
                            </div>

                            {/* User menu */}
                            <div className="relative">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => setUserMenuOpen(!userMenuOpen)}
                                    className="flex items-center space-x-2"
                                >
                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-br from-purple-500 to-blue-600">
                                        <span className="text-sm font-medium text-white">A</span>
                                    </div>
                                    <ChevronDown className="h-4 w-4" />
                                </Button>

                                {userMenuOpen && (
                                    <>
                                        <div className="fixed inset-0 z-40" onClick={() => setUserMenuOpen(false)} />
                                        <div className="absolute right-0 z-50 mt-2 w-48 rounded-lg border border-gray-200 bg-white py-1 shadow-lg">
                                            <div className="border-b border-gray-100 px-4 py-2">
                                                <p className="text-sm font-medium text-gray-900">Admin Panel</p>
                                                <p className="text-sm text-gray-500">Goldfield Investment</p>
                                            </div>
                                            <button
                                                onClick={handleLogout}
                                                className="flex w-full items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                                            >
                                                <LogOut className="mr-3 h-4 w-4" />
                                                Sign out
                                            </button>
                                        </div>
                                    </>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Page content */}
                <main className="flex-1">{children}</main>
            </div>
            <Toaster />
        </div>
    );
}

function SidebarContent({ currentRoute }: { currentRoute: string | undefined }) {
    return (
        <nav className="flex-1 space-y-1 px-2 py-4">
            {navigation.map((item) => {
                const isActive = currentRoute === item.href;
                return (
                    <Link
                        key={item.name}
                        href={route(item.href)}
                        className={cn(
                            'group flex items-center rounded-md px-2 py-2 text-sm font-medium transition-colors',
                            isActive
                                ? 'border-r-2 border-purple-500 bg-purple-50 text-purple-700'
                                : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900',
                        )}
                    >
                        <item.icon
                            className={cn('mr-3 h-5 w-5 flex-shrink-0', isActive ? 'text-purple-500' : 'text-gray-400 group-hover:text-gray-500')}
                        />
                        <span className="flex-1">{item.name}</span>
                        {item.badge && (
                            <span className="ml-3 inline-block rounded-full bg-red-100 px-2 py-0.5 text-xs font-medium text-red-800">
                                {item.badge}
                            </span>
                        )}
                    </Link>
                );
            })}
        </nav>
    );
}
