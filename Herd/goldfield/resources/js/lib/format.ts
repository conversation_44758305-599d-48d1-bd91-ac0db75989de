/**
 * Format a number as currency
 * @param amount - The amount to format
 * @param currency - The currency code (default: 'NGN')
 * @returns Formatted currency string
 */
export const formatCurrency = (amount: number | string, currency: string = 'NGN'): string => {
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    
    return new Intl.NumberFormat('en-NG', {
        style: 'currency',
        currency,
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
    }).format(numAmount);
};
