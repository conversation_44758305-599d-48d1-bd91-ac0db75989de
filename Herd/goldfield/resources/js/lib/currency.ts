/**
 * Format amount as Nigerian Naira currency
 */
export const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-NG', {
        style: 'currency',
        currency: 'NGN',
    }).format(amount);
};

/**
 * Format amount as Nigerian Naira with compact notation for large numbers
 */
export const formatCurrencyCompact = (amount: number): string => {
    return new Intl.NumberFormat('en-NG', {
        style: 'currency',
        currency: 'NGN',
        notation: 'compact',
        maximumFractionDigits: 1,
    }).format(amount);
};

/**
 * Format amount as plain number with Naira symbol
 */
export const formatNaira = (amount: number): string => {
    return `₦${amount.toLocaleString('en-NG')}`;
};
