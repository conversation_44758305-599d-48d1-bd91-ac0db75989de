import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button-new';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AdminLayout from '@/layouts/AdminLayout';
import { formatCurrency } from '@/lib/currency';
import { CheckCircle, Clock, Eye, Loader2, TrendingUp, Wallet, XCircle } from 'lucide-react';
import { Head, router } from '@inertiajs/react';
import { useCallback, useEffect, useState } from 'react';
import { toast } from '@/components/ui/use-toast';

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PaginationData {
    currentPage: number;
    links: PaginationLink[];
}

interface Deposit {
    id: number;
    user: {
        name: string;
        email: string;
    };
    amount: number;
    method: string;
    status: string;
    created_at: string;
    reference: string;
    metadata?: Record<string, unknown>;
    type?: 'transaction' | 'payment'; // To distinguish between old and new system
    pending_duration?: number;
    eligible_for_approval?: boolean;
}

interface DepositStats {
    total_deposits: number;
    pending_deposits: number;
    approved_today: number;
    total_today: number;
}

interface PageProps {
    deposits: Deposit[];
    stats: DepositStats;
}

// Default values for stats
const defaultStats: DepositStats = {
    total_deposits: 0,
    pending_deposits: 0,
    approved_today: 0,
    total_today: 0
};

export default function AdminDeposits({
    deposits: initialDeposits = [],
    stats: initialStats
}: Partial<PageProps>) {
    const [deposits, setDeposits] = useState<Deposit[]>(initialDeposits || []);
    const [stats, setStats] = useState<DepositStats>(initialStats || defaultStats);
    const [loading, setLoading] = useState<Record<number, 'approve' | 'reject' | null>>({});
    const [pagination, setPagination] = useState<PaginationData>({
        currentPage: 1,
        links: [],
    });
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingStats, setIsLoadingStats] = useState(!initialStats);
    const [statusFilter, setStatusFilter] = useState<string>('pending'); // Default to show pending deposits

    const fetchDeposits = useCallback(async () => {
        try {
            setIsLoading(true);
            const response = await fetch(`/admin/deposits?page=${pagination.currentPage}&status=${statusFilter}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });

            if (!response.ok) {
                throw new Error('Failed to fetch deposits');
            }

            const { data, links } = await response.json();
            setDeposits(data);
            setPagination(prev => ({
                ...prev,
                links: links || [],
            }));
        } catch (error) {
            console.error('Error fetching deposits:', error);
            toast({
                title: 'Error',
                description: 'Failed to load deposits',
                variant: 'destructive',
            });
        } finally {
            setIsLoading(false);
        }
    }, [pagination.currentPage, statusFilter]);

    // Fetch stats if not provided
    const fetchStats = useCallback(async () => {
        try {
            setIsLoadingStats(true);
            const response = await fetch('/admin/deposits/stats', {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });

            if (response.ok) {
                const data = await response.json();
                setStats(data);
            }
        } catch (error) {
            console.error('Error fetching stats:', error);
            toast({
                title: 'Error',
                description: 'Failed to load statistics',
                variant: 'destructive',
            });
        } finally {
            setIsLoadingStats(false);
        }
    }, []);

    useEffect(() => {
        if (!initialStats) {
            fetchStats();
        }
    }, [fetchStats, initialStats]);

    useEffect(() => {
        fetchDeposits();
    }, [fetchDeposits]);

    const updateDepositStatus = async (deposit: Deposit, action: 'approve' | 'reject', reason?: string) => {
        try {
            setLoading(prev => ({ ...prev, [deposit.id]: action }));

            // Determine the correct URL based on deposit type and action
            let url: string;
            if (action === 'approve' && deposit.type === 'payment') {
                url = `/admin/deposits/payments/${deposit.id}/approve`;
            } else if (action === 'approve') {
                url = `/admin/deposits/${deposit.id}/approve`;
            } else {
                url = `/admin/deposits/${deposit.id}/reject`;
            }

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                },
                body: action === 'reject' && reason ? JSON.stringify({ reason }) : undefined,
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('API Error:', {
                    status: response.status,
                    statusText: response.statusText,
                    url: url,
                    errorData: errorData
                });
                throw new Error(errorData.message || `Failed to ${action} deposit (${response.status})`);
            }

            const responseData = await response.json();
            console.log('API Success:', responseData);
            const { deposit: updatedDeposit } = responseData;

            // Update the deposit in the list or remove it if it no longer matches the filter
            setDeposits(prevDeposits => {
                // If the current filter is 'pending' and the deposit was rejected/approved, remove it from the list
                if (statusFilter === 'pending' && (updatedDeposit.status === 'failed' || updatedDeposit.status === 'completed')) {
                    return prevDeposits.filter(d => d.id !== deposit.id);
                }

                // If the current filter is 'completed' and the deposit was rejected, remove it from the list
                if (statusFilter === 'completed' && updatedDeposit.status === 'failed') {
                    return prevDeposits.filter(d => d.id !== deposit.id);
                }

                // If the current filter is 'failed' and the deposit was approved, remove it from the list
                if (statusFilter === 'failed' && updatedDeposit.status === 'completed') {
                    return prevDeposits.filter(d => d.id !== deposit.id);
                }

                // For 'all' filter or if the status matches the filter, update the deposit status
                return prevDeposits.map(d =>
                    d.id === deposit.id
                        ? { ...d, status: updatedDeposit.status }
                        : d
                );
            });

            // Update stats
            setStats(prevStats => ({
                ...prevStats,
                pending_deposits: prevStats.pending_deposits - 1,
                approved_today: action === 'approve' ? prevStats.approved_today + 1 : prevStats.approved_today,
                total_today: action === 'approve' ? prevStats.total_today + deposit.amount : prevStats.total_today,
                total_deposits: action === 'approve' ? prevStats.total_deposits + deposit.amount : prevStats.total_deposits,
            }));

            // Show success toast
            toast({
                title: `Deposit ${action === 'approve' ? 'Approved' : 'Rejected'}`,
                description: `The deposit has been ${action === 'approve' ? 'approved' : 'rejected'} successfully.`,
                variant: 'default',
            });
        } catch (error) {
            console.error(`Error ${action}ing deposit:`, error);

            toast({
                title: `Failed to ${action} deposit`,
                description: error instanceof Error ? error.message : 'An error occurred while processing your request.',
                variant: 'destructive',
            });
        } finally {
            setLoading(prev => ({ ...prev, [deposit.id]: null }));
        }
    };

    const handleReject = (deposit: Deposit) => {
        const reason = prompt('Please provide a reason for rejection:');
        if (reason) {
            updateDepositStatus(deposit, 'reject', reason);
        }
    };

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            approved: 'default',
            pending: 'secondary',
            rejected: 'destructive',
            processing: 'outline',
        };

        return <Badge variant={variants[status] || 'outline'}>{status.charAt(0).toUpperCase() + status.slice(1)}</Badge>;
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'approved':
                return <CheckCircle className="h-4 w-4 text-green-500" />;
            case 'pending':
                return <Clock className="h-4 w-4 text-yellow-500" />;
            case 'rejected':
                return <XCircle className="h-4 w-4 text-red-500" />;
            default:
                return <Clock className="h-4 w-4 text-gray-500" />;
        }
    };

    return (
        <AdminLayout title="Deposits">
            <Head title="Deposits - Admin" />

            <div className="p-6">
                {/* Stats Grid */}
                {isLoadingStats ? (
                    <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                        {[1, 2, 3, 4].map((i) => (
                            <Card key={i}>
                                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                    <div className="h-4 w-24 bg-gray-200 rounded animate-pulse"></div>
                                    <div className="h-4 w-4 bg-gray-200 rounded-full"></div>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-8 w-24 bg-gray-200 rounded animate-pulse mb-1"></div>
                                    <div className="h-3 w-20 bg-gray-100 rounded"></div>
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                ) : (
                    <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Deposits</CardTitle>
                                <Wallet className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatCurrency(stats.total_deposits)}</div>
                                <p className="text-xs text-muted-foreground">All time deposits</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Pending Deposits</CardTitle>
                                <Clock className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.pending_deposits}</div>
                                <p className="text-xs text-muted-foreground">Awaiting approval</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Approved Today</CardTitle>
                                <CheckCircle className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.approved_today}</div>
                                <p className="text-xs text-muted-foreground">Processed today</p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Today's Volume</CardTitle>
                                <TrendingUp className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{formatCurrency(stats.total_today)}</div>
                                <p className="text-xs text-muted-foreground">Total deposited today</p>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Deposits Table */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle>Recent Deposits</CardTitle>
                                <CardDescription>Manage and review deposit requests</CardDescription>
                            </div>
                            <div className="flex items-center space-x-2">
                                <select
                                    value={statusFilter}
                                    onChange={(e) => {
                                        setStatusFilter(e.target.value);
                                        setPagination(prev => ({
                                            ...prev,
                                            currentPage: 1, // Reset to first page when filter changes
                                        }));
                                    }}
                                    className="h-9 rounded-md border border-input bg-background px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring"
                                >
                                    <option value="all">All Deposits</option>
                                    <option value="pending">Pending Approval</option>
                                    <option value="completed">Completed</option>
                                    <option value="failed">Failed</option>
                                </select>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {isLoading ? (
                            <div className="flex items-center justify-center py-12">
                                <Loader2 className="mr-2 h-6 w-6 animate-spin" />
                                <span>Loading deposits...</span>
                            </div>
                        ) : deposits.length > 0 ? (
                            <>
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>User</TableHead>
                                            <TableHead>Amount</TableHead>
                                            <TableHead>Method</TableHead>
                                            <TableHead>Reference</TableHead>
                                            <TableHead>Date</TableHead>
                                            <TableHead>Status</TableHead>
                                            <TableHead className="text-right">Actions</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {deposits.map((deposit) => (
                                            <TableRow key={deposit.id}>
                                                <TableCell>
                                                    <div className="flex items-center space-x-3">
                                                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                                                            {getStatusIcon(deposit.status)}
                                                        </div>
                                                        <div>
                                                            <p className="font-medium">{deposit.user.name}</p>
                                                            <p className="text-sm text-muted-foreground">{deposit.user.email}</p>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <span className="font-semibold">{formatCurrency(deposit.amount)}</span>
                                                </TableCell>
                                                <TableCell>
                                                    <span className="text-sm">{deposit.method}</span>
                                                </TableCell>
                                                <TableCell>
                                                    <span className="font-mono text-sm">{deposit.reference}</span>
                                                </TableCell>
                                                <TableCell>
                                                    <span className="text-sm">
                                                        {new Date(deposit.created_at).toLocaleDateString()}
                                                    </span>
                                                </TableCell>
                                                <TableCell>
                                                    {getStatusBadge(deposit.status)}
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex items-center justify-end space-x-2">
                                                        <Button
                                                            variant="outline"
                                                            size="sm"
                                                            className="h-8 w-8 p-0"
                                                            onClick={() => router.get(`/admin/deposits/${deposit.id}`)}
                                                        >
                                                            <Eye className="h-4 w-4" />
                                                        </Button>

                                                        {deposit.status === 'pending' && (
                                                            <>
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="h-8 w-8 p-0 text-green-600 hover:text-green-700 border-green-200 hover:bg-green-50"
                                                                    onClick={() => updateDepositStatus(deposit, 'approve')}
                                                                    disabled={!!loading[deposit.id]}
                                                                    title="Approve deposit"
                                                                >
                                                                    {loading[deposit.id] === 'approve' ? (
                                                                        <Loader2 className="h-4 w-4 animate-spin" />
                                                                    ) : (
                                                                        <CheckCircle className="h-4 w-4" />
                                                                    )}
                                                                </Button>
                                                                <Button
                                                                    variant="outline"
                                                                    size="sm"
                                                                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 border-red-200 hover:bg-red-50"
                                                                    onClick={() => handleReject(deposit)}
                                                                    disabled={!!loading[deposit.id]}
                                                                    title="Reject deposit"
                                                                >
                                                                    {loading[deposit.id] === 'reject' ? (
                                                                        <Loader2 className="h-4 w-4 animate-spin" />
                                                                    ) : (
                                                                        <XCircle className="h-4 w-4" />
                                                                    )}
                                                                </Button>
                                                            </>
                                                        )}
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>

                                {pagination?.links && pagination.links.length > 3 && (
                                    <div className="flex items-center justify-end space-x-2 py-4 px-6">
                                        {pagination.links.map((link: PaginationLink, index: number) => {
                                            if (!link.url) return null;

                                            const isActive = link.active;
                                            const isDisabled = !link.url || isActive;

                                            return (
                                                <Button
                                                    key={index}
                                                    variant={isActive ? 'default' : 'outline'}
                                                    size="sm"
                                                    onClick={() => {
                                                        const url = new URL(link.url as string);
                                                        const page = url.searchParams.get('page') || '1';
                                                        setPagination(prev => ({
                                                            ...prev,
                                                            currentPage: parseInt(page, 10),
                                                        }));
                                                    }}
                                                    disabled={isDisabled}
                                                >
                                                    {link.label
                                                        .replace('&laquo; Previous', '«')
                                                        .replace('Next &raquo;', '»')
                                                    }
                                                </Button>
                                            );
                                        })}
                                    </div>
                                )}
                            </>
                        ) : (
                            <div className="flex flex-col items-center justify-center py-12 text-center">
                                <Wallet className="h-12 w-12 text-gray-400 mb-4" />
                                <h3 className="text-lg font-medium text-gray-900">No deposits found</h3>
                                <p className="text-sm text-gray-500">There are no deposits to display at this time.</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
