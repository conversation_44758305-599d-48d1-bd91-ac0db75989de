export interface Plan {
    id: number;
    name: string;
    description: string | null;
    min_deposit: number;
    max_deposit: number | null;
    type: 'fixed' | 'flexible';
    return: number | null;
    return_type: 'fixed' | 'percent' | null;
    return_periods: Record<string, number> | null;
    duration: number;
    features: string[];
    is_active: boolean;
    referral_bonus: number;
    created_at: string;
    updated_at: string;
    deleted_at?: string | null;
}

import { FormDataConvertible } from '@inertiajs/core';

type PlanFormData = {
    name: string;
    description: string;
    min_deposit: number | string;
    max_deposit: number | string | null;
    type: 'fixed' | 'flexible';
    return: number | string | null;
    return_type: 'fixed' | 'percent' | null;
    return_periods: Record<string, number> | null;
    duration: number | string;
    features: string[];
    is_active: boolean;
    referral_bonus: number | string;
} & {
    [key: string]: FormDataConvertible; // For Inertia's FormData compatibility
};

export type { PlanFormData };
