import * as React from 'react';

declare module '@/components/ui/table' {
    export const Table: React.FC<React.HTMLAttributes<HTMLTableElement>>;
    export const TableHeader: React.FC<React.HTMLAttributes<HTMLTableSectionElement>>;
    export const TableBody: React.FC<React.HTMLAttributes<HTMLTableSectionElement>>;
    export const TableFooter: React.FC<React.HTMLAttributes<HTMLTableSectionElement>>;
    export const TableHead: React.FC<React.ThHTMLAttributes<HTMLTableCellElement>>;
    export const TableRow: React.FC<React.HTMLAttributes<HTMLTableRowElement>>;
    export const TableCell: React.FC<React.TdHTMLAttributes<HTMLTableCellElement>>;
    export const TableCaption: React.FC<React.HTMLAttributes<HTMLTableCaptionElement>>;
}

declare module '@/components/ui/card' {
    export const Card: React.FC<React.HTMLAttributes<HTMLDivElement>> & {
        Header: React.FC<React.HTMLAttributes<HTMLDivElement>>;
        Title: React.FC<React.HTMLAttributes<HTMLHeadingElement>>;
        Description: React.FC<React.HTMLAttributes<HTMLDivElement>>;
        Content: React.FC<React.HTMLAttributes<HTMLDivElement>>;
        Footer: React.FC<React.HTMLAttributes<HTMLDivElement>>;
    };
    
    export const CardHeader: React.FC<React.HTMLAttributes<HTMLDivElement>>;
    export const CardTitle: React.FC<React.HTMLAttributes<HTMLHeadingElement>>;
    export const CardDescription: React.FC<React.HTMLAttributes<HTMLDivElement>>;
    export const CardContent: React.FC<React.HTMLAttributes<HTMLDivElement>>;
    export const CardFooter: React.FC<React.HTMLAttributes<HTMLDivElement>>;
}

declare module '@/components/ui/button' {
    export const Button: React.FC<React.ButtonHTMLAttributes<HTMLButtonElement>>;
}

declare module '@/components/ui/input' {
    export const Input: React.FC<React.InputHTMLAttributes<HTMLInputElement>>;
}
