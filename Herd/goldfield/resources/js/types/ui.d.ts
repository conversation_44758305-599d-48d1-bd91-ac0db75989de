// Type definitions for UI components
declare module '@/components/ui/table' {
    import { ComponentType, HTMLAttributes, ThHTMLAttributes, TdHTMLAttributes } from 'react';

    export const Table: ComponentType<HTMLAttributes<HTMLTableElement>>;
    export const TableHeader: ComponentType<HTMLAttributes<HTMLTableSectionElement>>;
    export const TableBody: ComponentType<HTMLAttributes<HTMLTableSectionElement>>;
    export const TableFooter: ComponentType<HTMLAttributes<HTMLTableSectionElement>>;
    export const TableHead: ComponentType<ThHTMLAttributes<HTMLTableCellElement>>;
    export const TableRow: ComponentType<HTMLAttributes<HTMLTableRowElement>>;
    export const TableCell: ComponentType<TdHTMLAttributes<HTMLTableCellElement>>;
    export const TableCaption: ComponentType<HTMLAttributes<HTMLTableCaptionElement>>;
}

declare module '@/components/ui/card' {
    import { ComponentType, HTMLAttributes } from 'react';

    export const Card: ComponentType<HTMLAttributes<HTMLDivElement>> & {
        Header: ComponentType<HTMLAttributes<HTMLDivElement>>;
        Title: ComponentType<HTMLAttributes<HTMLHeadingElement>>;
        Description: ComponentType<HTMLAttributes<HTMLDivElement>>;
        Content: ComponentType<HTMLAttributes<HTMLDivElement>>;
        Footer: ComponentType<HTMLAttributes<HTMLDivElement>>;
    };
    
    export const CardHeader: ComponentType<HTMLAttributes<HTMLDivElement>>;
    export const CardTitle: ComponentType<HTMLAttributes<HTMLHeadingElement>>;
    export const CardDescription: ComponentType<HTMLAttributes<HTMLDivElement>>;
    export const CardContent: ComponentType<HTMLAttributes<HTMLDivElement>>;
    export const CardFooter: ComponentType<HTMLAttributes<HTMLDivElement>>;
}

declare module '@/components/ui/button' {
    import { ComponentType, ButtonHTMLAttributes } from 'react';
    export const Button: ComponentType<ButtonHTMLAttributes<HTMLButtonElement>>;
}

declare module '@/components/ui/input' {
    import { ComponentType, InputHTMLAttributes } from 'react';
    export const Input: ComponentType<InputHTMLAttributes<HTMLInputElement>>;
}
