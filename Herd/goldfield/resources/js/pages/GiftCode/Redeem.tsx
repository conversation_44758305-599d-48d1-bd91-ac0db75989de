import React, { useState, useEffect } from 'react';
import { <PERSON>, Link } from '@inertiajs/react';
import AppLayout from '../../layouts/app-layout';
// UI Components
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Gift, ArrowLeft, CheckCircle2, XCircle } from 'lucide-react';

type GiftCodeHistory = {
    code: string;
    amount: number;
    redeemed_at: string;
};

export default function RedeemGiftCode() {
    const [code, setCode] = useState('');
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [message, setMessage] = useState<{ type: 'success' | 'error' | ''; text: string }>({ type: '', text: '' });
    const [history, setHistory] = useState<GiftCodeHistory[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        // Load gift code history
        const loadHistory = async () => {
            try {
                const response = await fetch(route('api.gift-codes.history'));
                if (response.ok) {
                    const data = await response.json();
                    setHistory(data);
                }
            } catch (error) {
                console.error('Failed to load gift code history:', error);
            } finally {
                setIsLoading(false);
            }
        };

        loadHistory();
    }, []);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!code.trim()) {
            setMessage({ type: 'error', text: 'Please enter a gift code.' });
            return;
        }

        setIsSubmitting(true);
        setMessage({ type: '', text: '' });

        try {
            const response = await fetch(route('gift-codes.redeem.submit'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({ code }),
            });

            const data = await response.json();

            if (data.success) {
                setMessage({ type: 'success', text: data.message });
                setCode('');
                // Refresh history
                const historyResponse = await fetch(route('api.gift-codes.history'));
                if (historyResponse.ok) {
                    const historyData = await historyResponse.json();
                    setHistory(historyData);
                }
            } else {
                setMessage({ type: 'error', text: data.message });
            }
        } catch (error) {
            setMessage({ type: 'error', text: 'An error occurred while processing your request.' });
            console.error('Error redeeming gift code:', error);
        } finally {
            setIsSubmitting(false);
        }
    };

    return (
        <AppLayout>
            <Head title="Redeem Gift Code" />

            <div className="py-8">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="mb-6">
                        <Link
                            href={route('dashboard')}
                            className="inline-flex items-center text-sm text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300"
                        >
                            <ArrowLeft className="h-4 w-4 mr-1" />
                            Back to Dashboard
                        </Link>
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mt-2">Redeem Gift Code</h1>
                    </div>

                    <div className="grid gap-6 md:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center">
                                    <Gift className="h-5 w-5 mr-2 text-orange-500" />
                                    Enter Gift Code
                                </CardTitle>
                                <CardDescription>
                                    Enter your gift code below to redeem it for account credit.
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <form onSubmit={handleSubmit}>
                                    <div className="space-y-4">
                                        <div>
                                            <label htmlFor="code" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                                Gift Code
                                            </label>
                                            <Input
                                                id="code"
                                                type="text"
                                                value={code}
                                                onChange={(e) => setCode(e.target.value.toUpperCase())}
                                                placeholder="Enter gift code"
                                                className="w-full"
                                                disabled={isSubmitting}
                                            />
                                        </div>

                                        {message.text && (
                                            <div className={`p-3 rounded-md ${message.type === 'success' ? 'bg-green-50 dark:bg-green-900/30 text-green-800 dark:text-green-200' : 'bg-red-50 dark:bg-red-900/30 text-red-800 dark:text-red-200'}`}>
                                                <div className="flex items-center">
                                                    {message.type === 'success' ? (
                                                        <CheckCircle2 className="h-5 w-5 mr-2" />
                                                    ) : (
                                                        <XCircle className="h-5 w-5 mr-2" />
                                                    )}
                                                    <span>{message.text}</span>
                                                </div>
                                            </div>
                                        )}

                                        <Button
                                            type="submit"
                                            className="w-full bg-orange-600 hover:bg-orange-700 text-white"
                                            disabled={isSubmitting || !code.trim()}
                                        >
                                            {isSubmitting ? 'Processing...' : 'Redeem Code'}
                                        </Button>
                                    </div>
                                </form>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Gift Code History</CardTitle>
                                <CardDescription>
                                    Your previously redeemed gift codes
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                {isLoading ? (
                                    <div className="flex justify-center py-4">
                                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
                                    </div>
                                ) : history.length === 0 ? (
                                    <p className="text-center text-gray-500 dark:text-gray-400 py-4">
                                        No gift codes redeemed yet.
                                    </p>
                                ) : (
                                    <div className="overflow-x-auto">
                                        <Table>
                                            <TableHeader>
                                                <TableRow>
                                                    <TableHead>Code</TableHead>
                                                    <TableHead>Amount</TableHead>
                                                    <TableHead>Date</TableHead>
                                                </TableRow>
                                            </TableHeader>
                                            <TableBody>
                                                {history.map((item, index) => (
                                                    <TableRow key={index}>
                                                        <TableCell className="font-mono">{item.code}</TableCell>
                                                        <TableCell className="text-green-600 dark:text-green-400">
                                                            ${item.amount.toFixed(2)}
                                                        </TableCell>
                                                        <TableCell>
                                                            {new Date(item.redeemed_at).toLocaleDateString()}
                                                        </TableCell>
                                                    </TableRow>
                                                ))}
                                            </TableBody>
                                        </Table>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
