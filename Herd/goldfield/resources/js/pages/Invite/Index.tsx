import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button-fixed';
import { Input } from '@/components/ui/input';
import { Copy, Share2, Users } from 'lucide-react';
import { PageProps } from '@/types';
import AppLayout from '@/layouts/app-layout';
import { toast } from 'sonner';

interface InvitePageProps extends PageProps {
    referral_link: string;
    referral_code: string;
}

export default function InvitePage({ referral_link, referral_code }: InvitePageProps) {
    const copyToClipboard = (text: string, message: string = 'Copied to clipboard!') => {
        navigator.clipboard.writeText(text);
        toast.success(message);
    };

    const shareLink = async () => {
        if (navigator.share) {
            try {
                await navigator.share({
                    title: 'Join me on Goldfield',
                    text: 'Sign up using my referral code and get a bonus!',
                    url: referral_link,
                });
            } catch (err) {
                console.error('Error sharing:', err);
            }
        } else {
            copyToClipboard(referral_link, 'Link copied to clipboard!');
        }
    };

    return (
        <AppLayout>
            <div className="container mx-auto px-4 py-8">
                <div className="max-w-3xl mx-auto">
                    <div className="text-center mb-8">
                        <h1 className="text-3xl font-bold tracking-tight">Invite Friends</h1>
                        <p className="text-muted-foreground mt-2">
                            Share your referral link and earn rewards when your friends sign up
                        </p>
                    </div>

                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Users className="h-5 w-5 text-orange-500" />
                                Your Referral Link
                            </CardTitle>
                            <CardDescription>
                                Share this link with your friends to earn rewards when they sign up.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex flex-col space-y-4">
                                <div className="flex items-center space-x-2">
                                    <div className="flex-1">
                                        <Input
                                            readOnly
                                            value={referral_link}
                                            className="font-mono"
                                        />
                                    </div>
                                    <Button
                                        size="icon"
                                        variant="outline"
                                        onClick={() => copyToClipboard(referral_link)}
                                    >
                                        <Copy className="h-4 w-4" />
                                    </Button>
                                    <Button onClick={shareLink}>
                                        <Share2 className="mr-2 h-4 w-4" />
                                        Share
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Your Referral Code</CardTitle>
                            <CardDescription>
                                Share this code with friends who already have an account.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="flex items-center space-x-2">
                                <div className="flex-1">
                                    <Input
                                        readOnly
                                        value={referral_code}
                                        className="font-mono text-center text-lg font-bold"
                                    />
                                </div>
                                <Button
                                    variant="outline"
                                    onClick={() => copyToClipboard(referral_code, 'Referral code copied!')}
                                >
                                    <Copy className="mr-2 h-4 w-4" />
                                    Copy
                                </Button>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="mt-8">
                        <CardHeader>
                            <CardTitle>Referral Rewards</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-start space-x-4">
                                    <div className="flex-1 space-y-1">
                                        <p className="text-sm font-medium leading-none">
                                            For You
                                        </p>
                                        <p className="text-sm text-muted-foreground">
                                            Earn 5% of your friend's first deposit
                                        </p>
                                    </div>
                                    <div className="text-sm font-medium">Up to $100</div>
                                </div>
                                <div className="flex items-start space-x-4">
                                    <div className="flex-1 space-y-1">
                                        <p className="text-sm font-medium leading-none">
                                            For Your Friend
                                        </p>
                                        <p className="text-sm text-muted-foreground">
                                            Get 10% bonus on first deposit
                                        </p>
                                    </div>
                                    <div className="text-sm font-medium">Up to $50</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
