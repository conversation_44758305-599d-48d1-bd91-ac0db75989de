import { Head } from '@inertiajs/react';
import { type PageProps, type User } from '@/types';

interface DashboardPageProps extends PageProps {
    user: User;
}

export default function TestDashboard({ user }: DashboardPageProps) {
    return (
        <>
            <Head title="Test Dashboard" />
            <div className="min-h-screen bg-gray-100 p-8">
                <div className="max-w-4xl mx-auto">
                    <h1 className="text-3xl font-bold text-gray-900 mb-6">Test Dashboard</h1>
                    <div className="bg-white rounded-lg shadow p-6">
                        <h2 className="text-xl font-semibold mb-4">User Information</h2>
                        <p><strong>Name:</strong> {user?.name || 'No name'}</p>
                        <p><strong>Email:</strong> {user?.email || 'No email'}</p>
                        <p><strong>Balance:</strong> ₦{user?.balance || '0.00'}</p>
                        <p><strong>Total Earnings:</strong> ₦{user?.total_earnings || '0.00'}</p>
                    </div>
                </div>
            </div>
        </>
    );
}
