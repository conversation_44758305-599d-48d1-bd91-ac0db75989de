import AppLayout from '@/layouts/app-layout';
import { Head, Link, useForm } from '@inertiajs/react';
import { ArrowLeft, Banknote, CheckCircle2 } from 'lucide-react';
import { Button } from '@/components/ui/button-new';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { useState, useEffect, useCallback } from 'react';

export default function AddBankAccount() {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [isSuccess, setIsSuccess] = useState(false);
    const [isValidating, setIsValidating] = useState(false);
    const [validationError, setValidationError] = useState('');

    const { data, setData, processing, errors } = useForm({
        account_name: '',
        account_number: '',
        bank_name: '',
        bank_code: '',
        account_type: 'savings',
    });

    const nigerianBanks = [
        { name: 'Access Bank', code: '044' },
        { name: 'First Bank of Nigeria', code: '011' },
        { name: 'Guaranty Trust Bank', code: '058' },
        { name: 'Zenith Bank', code: '057' },
        { name: 'United Bank for Africa', code: '033' },
        { name: 'Fidelity Bank', code: '070' },
        { name: 'Union Bank of Nigeria', code: '032' },
        { name: 'Stanbic IBTC Bank', code: '221' },
        { name: 'First City Monument Bank', code: '214' },
        { name: 'Ecobank Nigeria', code: '050' },
    ];

    // Function to validate account number and fetch account name
    const validateAccountNumber = useCallback(async (accountNumber: string, bankCode: string) => {
        if (!bankCode || accountNumber.length < 10) {
            setData('account_name', '');
            setValidationError('Please select a bank and enter a 10-digit account number');
            return;
        }

        setIsValidating(true);
        setValidationError('');
        
        try {
            const requestData = {
                account_number: accountNumber,
                bank_code: bankCode,
            };

            console.log('Sending bank verification request:', requestData);
            
            // Get CSRF token from meta tag
            let token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            
            if (!token) {
                console.error('CSRF token not found in meta tag');
                // Try to get a fresh CSRF token
                try {
                    const response = await fetch('/sanctum/csrf-cookie', {
                        credentials: 'include',
                    });
                    if (!response.ok) {
                        throw new Error('Failed to get CSRF token');
                    }
                    // Try to get the token again
                    const newToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
                    if (!newToken) {
                        throw new Error('CSRF token still not found after refresh');
                    }
                    token = newToken;
                } catch (error) {
                    console.error('Error getting CSRF token:', error);
                    throw new Error('Failed to initialize CSRF token');
                }
            }
            
            // Create form data
            const formData = new FormData();
            formData.append('_token', token);
            formData.append('account_number', requestData.account_number);
            formData.append('bank_code', requestData.bank_code);
            
            // Call our backend API which will then call Paystack
            const response = await fetch('/api/bank/verify-account', {
                method: 'POST',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': token,
                },
                credentials: 'same-origin',
                body: formData,
            });

            console.log('Response status:', response.status);
            const result = await response.json().catch(e => {
                console.error('Error parsing JSON response:', e);
                return { success: false, message: 'Invalid response from server' };
            });
            
            console.log('API Response:', result);
            
            if (response.ok && result.success) {
                console.log('Account verification successful:', result.data);
                setData('account_name', result.data.account_name);
                setValidationError('');
            } else {
                console.error('Account verification failed:', result.message);
                setData('account_name', '');
                setValidationError(result.message || 'Could not verify account. Please check the details and try again.');
            }
        } catch (error) {
            console.error('Error validating account:', error);
            let errorMessage = 'An error occurred while validating the account. Please try again.';
            
            if (error instanceof Error) {
                errorMessage = error.message;
                if (error.cause) {
                    console.error('Error cause:', error.cause);
                }
            } else if (typeof error === 'string') {
                errorMessage = error;
            } else if (error && typeof error === 'object' && 'message' in error) {
                errorMessage = String(error.message);
            }
            
            console.error('Full error object:', JSON.stringify(error, null, 2));
            setValidationError(errorMessage);
        } finally {
            setIsValidating(false);
        }
    }, [setData]);

    // Effect to validate account number when it changes and has 10 digits
    useEffect(() => {
        const accountNumber = data.account_number;
        const bankCode = data.bank_code;
        
        if (accountNumber.length === 10 && bankCode) {
            const timer = setTimeout(() => {
                validateAccountNumber(accountNumber, bankCode);
            }, 1000); // Debounce for 1 second
            
            return () => clearTimeout(timer);
        } else if (accountNumber.length === 0) {
            setData('account_name', '');
            setValidationError('');
        }
    }, [data.account_number, data.bank_code, setData, validateAccountNumber]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        setIsSubmitting(true);
        
        // Simulate API call
        setTimeout(() => {
            console.log('Bank account details:', data);
            setIsSubmitting(false);
            setIsSuccess(true);
            
            // Reset form after success
            setTimeout(() => {
                // In a real app, you would redirect or update the UI accordingly
                window.location.href = '/withdraw';
            }, 2000);
        }, 1500);
    };

    if (isSuccess) {
        return (
            <AppLayout>
                <div className="container py-10 px-4">
                    <div className="max-w-md mx-auto text-center">
                        <div className="bg-green-100 p-4 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
                            <CheckCircle2 className="h-10 w-10 text-green-600" />
                        </div>
                        <h2 className="text-2xl font-bold mb-2">Bank Account Added</h2>
                        <p className="text-muted-foreground mb-8">
                            Your bank account has been successfully added and is now available for withdrawals.
                        </p>
                        <Button asChild>
                            <Link href="/withdraw">Back to Withdraw</Link>
                        </Button>
                    </div>
                </div>
            </AppLayout>
        );
    }

    return (
        <AppLayout>
            <Head title="Add Bank Account" />
            <div className="container py-6 px-4">
                <div className="flex items-center gap-4 mb-8">
                    <Link href="/withdraw" className="text-muted-foreground hover:text-foreground">
                        <ArrowLeft className="h-5 w-5" />
                    </Link>
                    <h1 className="text-2xl font-bold">Add Bank Account</h1>
                </div>

                <Card className="max-w-2xl mx-auto">
                    <CardHeader>
                        <div className="flex items-center gap-3 mb-2">
                            <div className="bg-orange-100 p-2 rounded-lg">
                                <Banknote className="h-6 w-6 text-orange-600" />
                            </div>
                            <div>
                                <CardTitle>Bank Account Details</CardTitle>
                                <CardDescription>
                                    Enter your bank account information
                                </CardDescription>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div className="space-y-4">
                                <div className="grid gap-2">
                                    <Label htmlFor="bank_name">Bank Name</Label>
                                    <Select 
                                        value={data.bank_code}
                                        onValueChange={(value) => {
                                            const bank = nigerianBanks.find(bank => bank.code === value);
                                            setData({
                                                ...data,
                                                bank_code: value,
                                                bank_name: bank?.name || ''
                                            });
                                        }}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select your bank" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {nigerianBanks.map((bank) => (
                                                <SelectItem key={bank.code} value={bank.code}>
                                                    {bank.name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.bank_code && (
                                        <p className="text-sm text-red-500 mt-1">{errors.bank_code}</p>
                                    )}
                                </div>

                                <div className="grid gap-2">
                                    <Label htmlFor="account_number">Account Number</Label>
                                    <div className="relative">
                                        <Input
                                            id="account_number"
                                            type="text"
                                            inputMode="numeric"
                                            pattern="[0-9]*"
                                            value={data.account_number}
                                            onChange={(e) => {
                                                // Only allow numbers
                                                const value = e.target.value.replace(/\D/g, '');
                                                setData('account_number', value);
                                            }}
                                            placeholder="Enter account number"
                                            maxLength={10}
                                            disabled={!data.bank_code}
                                            className={isValidating ? 'pr-10' : ''}
                                        />
                                        {isValidating && (
                                            <div className="absolute right-3 top-1/2 -translate-y-1/2">
                                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500"></div>
                                            </div>
                                        )}
                                    </div>
                                    {!data.bank_code ? (
                                        <p className="text-sm text-amber-600 mt-1">Please select a bank first</p>
                                    ) : errors.account_number ? (
                                        <p className="text-sm text-red-500 mt-1">{errors.account_number}</p>
                                    ) : null}
                                </div>

                                <div className="grid gap-2">
                                    <Label htmlFor="account_name">Account Name</Label>
                                    <div className="relative">
                                        <Input
                                            id="account_name"
                                            type="text"
                                            value={data.account_name}
                                            onChange={(e) => setData('account_name', e.target.value)}
                                            placeholder="Account name will appear here"
                                            disabled={true}
                                            className={data.account_name ? 'font-medium text-green-700' : ''}
                                        />
                                        {data.account_name && (
                                            <CheckCircle2 className="h-5 w-5 text-green-500 absolute right-3 top-1/2 -translate-y-1/2" />
                                        )}
                                    </div>
                                    {validationError ? (
                                        <p className="text-sm text-red-500 mt-1">{validationError}</p>
                                    ) : data.account_number && data.account_number.length < 10 ? (
                                        <p className="text-xs text-muted-foreground mt-1">
                                            Enter a 10-digit account number to verify account name
                                        </p>
                                    ) : data.account_number && data.account_number.length === 10 && !data.account_name && !isValidating && (
                                        <p className="text-xs text-amber-600 mt-1">
                                            Could not verify account. Please check the details and try again.
                                        </p>
                                    )}
                                </div>

                                <div className="grid gap-2">
                                    <Label htmlFor="account_type">Account Type</Label>
                                    <Select 
                                        value={data.account_type}
                                        onValueChange={(value) => setData('account_type', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select account type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="savings">Savings Account</SelectItem>
                                            <SelectItem value="current">Current Account</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.account_type && (
                                        <p className="text-sm text-red-500 mt-1">{errors.account_type}</p>
                                    )}
                                </div>
                            </div>

                            <div className="pt-2">
                                <Button 
                                    type="submit" 
                                    className="w-full h-12 text-base"
                                    disabled={isSubmitting || processing || !data.account_name}
                                >
                                    {isSubmitting ? 'Adding Account...' : 'Add Bank Account'}
                                </Button>
                            </div>
                        </form>
                    </CardContent>
                </Card>

                <div className="mt-8 max-w-2xl mx-auto bg-blue-50 p-4 rounded-lg">
                    <h3 className="font-medium text-blue-800 mb-2">Important Information</h3>
                    <ul className="text-sm text-blue-700 space-y-1">
                        <li>• Ensure your account details are correct to avoid failed transactions</li>
                        <li>• Withdrawals typically take 1-3 business days to process</li>
                        <li>• A small fee may apply for withdrawals to certain banks</li>
                    </ul>
                </div>
            </div>
        </AppLayout>
    );
}
