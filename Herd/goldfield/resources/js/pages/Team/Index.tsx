import AppLayout from '@/layouts/app-layout';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { type BreadcrumbItem } from '@/types';
import { But<PERSON> } from '@/components/ui/button-new';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, UserPlus, ChevronRight, Mail, Phone, UserCheck, UserX } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

type TeamMember = {
    id: number;
    name: string;
    email: string;
    phone: string;
    role: string;
    status: 'active' | 'pending' | 'inactive';
    avatar: string | null;
    join_date: string;
};

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/user/dashboard',
    },
    {
        title: 'Team',
        href: '/team',
    },
];

const teamMembers: TeamMember[] = [
    {
        id: 1,
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+****************',
        role: 'Team Lead',
        status: 'active',
        avatar: null,
        join_date: '2023-01-15',
    },
    {
        id: 2,
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: '+****************',
        role: 'Developer',
        status: 'active',
        avatar: null,
        join_date: '2023-02-20',
    },
    {
        id: 3,
        name: '<PERSON> <PERSON>',
        email: '<EMAIL>',
        phone: '+****************',
        role: 'Designer',
        status: 'pending',
        avatar: null,
        join_date: '2023-03-10',
    },
];

export default function Team() {
    const getStatusBadge = (status: TeamMember['status']) => {
        const statusClasses = {
            active: 'bg-green-100 text-green-800',
            pending: 'bg-yellow-100 text-yellow-800',
            inactive: 'bg-gray-100 text-gray-800',
        };

        const statusIcons = {
            active: <UserCheck className="h-3.5 w-3.5" />,
            pending: <UserX className="h-3.5 w-3.5" />,
            inactive: <UserX className="h-3.5 w-3.5" />,
        };

        return (
            <span className={`inline-flex items-center gap-1.5 rounded-full px-2.5 py-1 text-xs font-medium ${statusClasses[status]}`}>
                {statusIcons[status]}
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </span>
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Team Management" />
            
            <div className="space-y-6 p-4">
                <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
                    <div>
                        <h1 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">Team Management</h1>
                        <p className="text-gray-600 dark:text-gray-300">
                            Manage your team members and their permissions
                        </p>
                    </div>
                    <Button asChild>
                        <Link href="/team/invite">
                            <UserPlus className="mr-2 h-4 w-4" />
                            Invite Team Member
                        </Link>
                    </Button>
                </div>

                <Card className="overflow-hidden border-none bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20">
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle className="text-gray-900 dark:text-white">Team Members</CardTitle>
                                <CardDescription className="text-gray-600 dark:text-gray-300">
                                    People who have access to this team
                                </CardDescription>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button 
                                    variant="outline" 
                                    size="sm"
                                    className="bg-white/50 hover:bg-white/70 dark:bg-gray-800/50 dark:hover:bg-gray-700/50"
                                >
                                    Export
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-6">
                            {teamMembers.map((member) => (
                                <div key={member.id} className="flex items-center justify-between rounded-lg bg-white/50 p-4 backdrop-blur-sm dark:bg-gray-800/50">
                                    <div className="flex items-center space-x-4">
                                        <Avatar className="h-10 w-10">
                                            <AvatarImage src={member.avatar || undefined} alt={member.name} />
                                            <AvatarFallback>
                                                {member.name
                                                    .split(' ')
                                                    .map((n) => n[0])
                                                    .join('')}
                                            </AvatarFallback>
                                        </Avatar>
                                        <div className="space-y-1">
                                            <div className="flex items-center space-x-2">
                                                <p className="font-medium">{member.name}</p>
                                                {getStatusBadge(member.status)}
                                            </div>
                                            <div className="flex items-center text-sm text-muted-foreground">
                                                <span>{member.role}</span>
                                                <span className="mx-2">•</span>
                                                <span>Joined {new Date(member.join_date).toLocaleDateString()}</span>
                                            </div>
                                            <div className="flex items-center space-x-4 text-sm">
                                                <a href={`mailto:${member.email}`} className="flex items-center text-muted-foreground hover:text-primary">
                                                    <Mail className="mr-1.5 h-4 w-4" />
                                                    {member.email}
                                                </a>
                                                <a href={`tel:${member.phone}`} className="flex items-center text-muted-foreground hover:text-primary">
                                                    <Phone className="mr-1.5 h-4 w-4" />
                                                    {member.phone}
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    <Button variant="ghost" size="sm" asChild>
                                        <Link href={`/team/members/${member.id}`}>
                                            View Details
                                            <ChevronRight className="ml-1 h-4 w-4" />
                                        </Link>
                                    </Button>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>

                <div className="grid gap-6 md:grid-cols-2">
                    <Card className="overflow-hidden border-none bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20">
                        <CardHeader>
                            <CardTitle className="text-gray-900 dark:text-white">Team Stats</CardTitle>
                            <CardDescription className="text-gray-600 dark:text-gray-300">Overview of your team's activity</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <Users className="h-5 w-5 text-muted-foreground" />
                                        <span className="text-sm font-medium">Total Members</span>
                                    </div>
                                    <span className="font-medium">{teamMembers.length}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <UserCheck className="h-5 w-5 text-green-500" />
                                        <span className="text-sm font-medium">Active</span>
                                    </div>
                                    <span className="font-medium">
                                        {teamMembers.filter(m => m.status === 'active').length}
                                    </span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-2">
                                        <UserX className="h-5 w-5 text-yellow-500" />
                                        <span className="text-sm font-medium">Pending Invitations</span>
                                    </div>
                                    <span className="font-medium">
                                        {teamMembers.filter(m => m.status === 'pending').length}
                                    </span>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card className="overflow-hidden border-none bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20">
                        <CardHeader>
                            <CardTitle className="text-gray-900 dark:text-white">Quick Actions</CardTitle>
                            <CardDescription className="text-gray-600 dark:text-gray-300">Common team management tasks</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                <Button 
                                    variant="outline" 
                                    className="w-full justify-start bg-white/50 hover:bg-white/70 dark:bg-gray-800/50 dark:hover:bg-gray-700/50" 
                                    asChild
                                >
                                    <Link href="/team/invite">
                                        <UserPlus className="mr-2 h-4 w-4" />
                                        Invite New Member
                                    </Link>
                                </Button>
                                <Button 
                                    variant="outline" 
                                    className="w-full justify-start bg-white/50 hover:bg-white/70 dark:bg-gray-800/50 dark:hover:bg-gray-700/50"
                                >
                                    <Mail className="mr-2 h-4 w-4" />
                                    Send Team Update
                                </Button>
                                <Button 
                                    variant="outline" 
                                    className="w-full justify-start bg-white/50 hover:bg-white/70 dark:bg-gray-800/50 dark:hover:bg-gray-700/50"
                                >
                                    <Users className="mr-2 h-4 w-4" />
                                    Manage Permissions
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppLayout>
    );
}
