import { Head } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button-new';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { formatCurrency } from '@/lib/currency';
import {
    DollarSign,
    TrendingUp,
    TrendingDown,
    Search,
    Filter,
    Eye,
    Download,
    Calendar,
    Users,
    CreditCard,
    Wallet,
    Gift,
    ArrowUpRight,
    ArrowDownLeft,
    RefreshCw,
    CheckCircle,
    Clock,
    XCircle
} from 'lucide-react';
import { useState } from 'react';

export default function AdminTransactions() {
    const [searchTerm, setSearchTerm] = useState('');
    const [filterType, setFilterType] = useState('all');

    // Mock data for transactions
    const transactionStats = {
        total_transactions: 1247,
        total_volume: 245750000, // ₦245.75M
        deposits: 89750000, // ₦89.75M
        withdrawals: 45200000, // ₦45.2M
        investments: ********, // ₦98.5M
        returns: ********, // ₦12.3M
        pending_count: 23,
        completed_today: 156
    };

    const transactions = [
        {
            id: 1,
            user: {
                name: 'Adebayo Ogundimu',
                email: '<EMAIL>',
                referral_code: 'GF12AB34CD'
            },
            type: 'withdrawal',
            amount: 850000, // ₦850K
            status: 'pending',
            description: 'Withdrawal to First Bank - **********',
            reference: 'TXN001WTH',
            created_at: '2025-01-27 11:30:00',
            updated_at: '2025-01-27 11:30:00'
        },
        {
            id: 2,
            user: {
                name: 'Fatima Abdullahi',
                email: '<EMAIL>',
                referral_code: 'GF56EF78GH'
            },
            type: 'deposit',
            amount: 1200000, // ₦1.2M
            status: 'completed',
            description: 'Bank deposit - GTBank transfer',
            reference: 'TXN002DEP',
            created_at: '2025-01-27 09:45:00',
            updated_at: '2025-01-27 10:15:00'
        },
        {
            id: 3,
            user: {
                name: 'Chinedu Okoro',
                email: '<EMAIL>',
                referral_code: 'GF90IJ12KL'
            },
            type: 'investment',
            amount: 2500000, // ₦2.5M
            status: 'completed',
            description: 'Investment in Gold Package - 30 days',
            reference: 'TXN003INV',
            created_at: '2025-01-27 08:20:00',
            updated_at: '2025-01-27 08:20:00'
        },
        {
            id: 4,
            user: {
                name: 'Aisha Mohammed',
                email: '<EMAIL>',
                referral_code: 'GF34MN56OP'
            },
            type: 'return',
            amount: 125000, // ₦125K
            status: 'completed',
            description: 'Daily return - Gold Package Day 15',
            reference: 'TXN004RET',
            created_at: '2025-01-27 00:00:00',
            updated_at: '2025-01-27 00:00:00'
        },
        {
            id: 5,
            user: {
                name: 'Ibrahim Yusuf',
                email: '<EMAIL>',
                referral_code: 'GF78QR90ST'
            },
            type: 'gift_code',
            amount: 50000, // ₦50K
            status: 'completed',
            description: 'Gift code redemption - WELCOME2025',
            reference: 'TXN005GFT',
            created_at: '2025-01-26 16:45:00',
            updated_at: '2025-01-26 16:45:00'
        },
        {
            id: 6,
            user: {
                name: 'Kemi Adebayo',
                email: '<EMAIL>',
                referral_code: 'GF45UV67WX'
            },
            type: 'referral',
            amount: 75000, // ₦75K
            status: 'completed',
            description: 'Level 1 referral bonus from Fatima Abdullahi',
            reference: 'TXN006REF',
            created_at: '2025-01-26 14:30:00',
            updated_at: '2025-01-26 14:30:00'
        },
        {
            id: 7,
            user: {
                name: 'Olumide Balogun',
                email: '<EMAIL>',
                referral_code: 'GF89YZ01AB'
            },
            type: 'deposit',
            amount: 750000, // ₦750K
            status: 'failed',
            description: 'Bank deposit - Payment verification failed',
            reference: 'TXN007DEP',
            created_at: '2025-01-26 12:15:00',
            updated_at: '2025-01-26 13:00:00'
        },
        {
            id: 8,
            user: {
                name: 'Blessing Okafor',
                email: '<EMAIL>',
                referral_code: 'GF23CD45EF'
            },
            type: 'withdrawal',
            amount: 450000, // ₦450K
            status: 'processing',
            description: 'Withdrawal to Access Bank - **********',
            reference: 'TXN008WTH',
            created_at: '2025-01-26 10:20:00',
            updated_at: '2025-01-26 15:30:00'
        }
    ];

    const getTransactionIcon = (type: string) => {
        switch (type) {
            case 'deposit':
                return <ArrowDownLeft className="w-4 h-4 text-green-600" />;
            case 'withdrawal':
                return <ArrowUpRight className="w-4 h-4 text-red-600" />;
            case 'investment':
                return <TrendingUp className="w-4 h-4 text-blue-600" />;
            case 'return':
                return <RefreshCw className="w-4 h-4 text-purple-600" />;
            case 'gift_code':
                return <Gift className="w-4 h-4 text-orange-600" />;
            case 'referral':
                return <Users className="w-4 h-4 text-indigo-600" />;
            default:
                return <DollarSign className="w-4 h-4 text-gray-600" />;
        }
    };

    const getTransactionTypeColor = (type: string) => {
        const colors: Record<string, string> = {
            deposit: 'bg-green-100 text-green-800',
            withdrawal: 'bg-red-100 text-red-800',
            investment: 'bg-blue-100 text-blue-800',
            return: 'bg-purple-100 text-purple-800',
            gift_code: 'bg-orange-100 text-orange-800',
            referral: 'bg-indigo-100 text-indigo-800',
        };
        return colors[type] || 'bg-gray-100 text-gray-800';
    };

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            completed: 'default',
            pending: 'secondary',
            processing: 'outline',
            failed: 'destructive',
        };

        return (
            <Badge variant={variants[status] || 'outline'}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'completed':
                return <CheckCircle className="w-4 h-4 text-green-500" />;
            case 'pending':
                return <Clock className="w-4 h-4 text-yellow-500" />;
            case 'processing':
                return <RefreshCw className="w-4 h-4 text-blue-500" />;
            case 'failed':
                return <XCircle className="w-4 h-4 text-red-500" />;
            default:
                return <Clock className="w-4 h-4 text-gray-500" />;
        }
    };

    const filteredTransactions = transactions.filter(transaction => {
        const matchesSearch =
            transaction.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            transaction.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
            transaction.reference.toLowerCase().includes(searchTerm.toLowerCase()) ||
            transaction.description.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesFilter = filterType === 'all' || transaction.type === filterType;

        return matchesSearch && matchesFilter;
    });

    return (
        <AdminLayout title="Transactions">
            <Head title="Transactions - Admin" />

            <div className="p-6">
                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Volume</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(transactionStats.total_volume)}</div>
                            <p className="text-xs text-muted-foreground">
                                {transactionStats.total_transactions} transactions
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Deposits</CardTitle>
                            <ArrowDownLeft className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-green-600">{formatCurrency(transactionStats.deposits)}</div>
                            <p className="text-xs text-muted-foreground">
                                Total deposits
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Withdrawals</CardTitle>
                            <ArrowUpRight className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-red-600">{formatCurrency(transactionStats.withdrawals)}</div>
                            <p className="text-xs text-muted-foreground">
                                Total withdrawals
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pending</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{transactionStats.pending_count}</div>
                            <p className="text-xs text-muted-foreground">
                                Awaiting action
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Transactions Table */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle>All Transactions</CardTitle>
                                <CardDescription>Complete transaction history across all types</CardDescription>
                            </div>
                            <div className="flex items-center space-x-2">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                    <Input
                                        placeholder="Search transactions..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10 w-64"
                                    />
                                </div>
                                <select
                                    value={filterType}
                                    onChange={(e) => setFilterType(e.target.value)}
                                    className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                                >
                                    <option value="all">All Types</option>
                                    <option value="deposit">Deposits</option>
                                    <option value="withdrawal">Withdrawals</option>
                                    <option value="investment">Investments</option>
                                    <option value="return">Returns</option>
                                    <option value="gift_code">Gift Codes</option>
                                    <option value="referral">Referrals</option>
                                </select>
                                <Button variant="outline" size="sm">
                                    <Filter className="w-4 h-4 mr-2" />
                                    Filter
                                </Button>
                                <Button size="sm">
                                    <Download className="w-4 h-4 mr-2" />
                                    Export
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {filteredTransactions.length > 0 ? (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>User</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead>Amount</TableHead>
                                        <TableHead>Description</TableHead>
                                        <TableHead>Reference</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Date</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredTransactions.map((transaction) => (
                                        <TableRow key={transaction.id}>
                                            <TableCell>
                                                <div className="flex items-center space-x-3">
                                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
                                                        {getStatusIcon(transaction.status)}
                                                    </div>
                                                    <div>
                                                        <p className="font-medium">{transaction.user.name}</p>
                                                        <p className="text-sm text-muted-foreground">{transaction.user.email}</p>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex items-center space-x-2">
                                                    {getTransactionIcon(transaction.type)}
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTransactionTypeColor(transaction.type)}`}>
                                                        {transaction.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                                    </span>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <span className={`font-semibold ${
                                                    transaction.type === 'withdrawal' ? 'text-red-600' :
                                                    transaction.type === 'deposit' ? 'text-green-600' :
                                                    'text-blue-600'
                                                }`}>
                                                    {transaction.type === 'withdrawal' ? '-' : '+'}
                                                    {formatCurrency(transaction.amount)}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <p className="text-sm max-w-xs truncate" title={transaction.description}>
                                                    {transaction.description}
                                                </p>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-mono text-sm">{transaction.reference}</span>
                                            </TableCell>
                                            <TableCell>
                                                {getStatusBadge(transaction.status)}
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm">
                                                    <p>{new Date(transaction.created_at).toLocaleDateString()}</p>
                                                    <p className="text-muted-foreground">{new Date(transaction.created_at).toLocaleTimeString()}</p>
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <Button variant="outline" size="sm">
                                                    <Eye className="h-4 w-4" />
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        ) : (
                            <div className="py-8 text-center">
                                <DollarSign className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                <p className="text-muted-foreground">No transactions found</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
