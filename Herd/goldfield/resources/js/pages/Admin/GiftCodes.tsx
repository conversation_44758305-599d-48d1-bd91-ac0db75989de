import { Head } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button-new';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { formatCurrency } from '@/lib/currency';
import {
    Gift,
    Plus,
    Copy,
    Eye,
    Filter,
    Calendar,
    Users,
    DollarSign,
    CheckCircle,
    XCircle,
    Clock,
    Trash2
} from 'lucide-react';
import { useState } from 'react';

export default function AdminGiftCodes() {
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [newGiftCode, setNewGiftCode] = useState({
        code: '',
        amount: '',
        max_uses: '',
        expires_at: ''
    });

    // Mock data for demonstration
    const giftCodeStats = {
        total_codes: 45,
        active_codes: 12,
        total_redeemed: 8750000, // ₦8.75M
        total_uses: 156,
        codes_today: 3
    };

    const giftCodes = [
        {
            id: 1,
            code: 'WELCOME2025',
            amount: 50000, // ₦50,000
            max_uses: 100,
            current_uses: 45,
            status: 'active',
            created_at: '2025-01-20 09:00:00',
            expires_at: '2025-02-20 23:59:59',
            created_by: 'Admin User',
            type: 'welcome'
        },
        {
            id: 2,
            code: 'NEWYEAR50K',
            amount: 50000, // ₦50,000
            max_uses: 50,
            current_uses: 50,
            status: 'exhausted',
            created_at: '2025-01-01 00:00:00',
            expires_at: '2025-01-31 23:59:59',
            created_by: 'Admin User',
            type: 'promotional'
        },
        {
            id: 3,
            code: 'BONUS100K',
            amount: 100000, // ₦100,000
            max_uses: 20,
            current_uses: 8,
            status: 'active',
            created_at: '2025-01-25 14:30:00',
            expires_at: '2025-03-01 23:59:59',
            created_by: 'Admin User',
            type: 'bonus'
        },
        {
            id: 4,
            code: 'EXPIRED2024',
            amount: 25000, // ₦25,000
            max_uses: 30,
            current_uses: 12,
            status: 'expired',
            created_at: '2024-12-01 10:00:00',
            expires_at: '2024-12-31 23:59:59',
            created_by: 'Admin User',
            type: 'seasonal'
        }
    ];

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            active: 'default',
            exhausted: 'secondary',
            expired: 'destructive',
            disabled: 'outline',
        };

        return (
            <Badge variant={variants[status] || 'outline'}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'active':
                return <CheckCircle className="w-4 h-4 text-green-500" />;
            case 'exhausted':
                return <Users className="w-4 h-4 text-blue-500" />;
            case 'expired':
                return <XCircle className="w-4 h-4 text-red-500" />;
            case 'disabled':
                return <Clock className="w-4 h-4 text-gray-500" />;
            default:
                return <Gift className="w-4 h-4 text-gray-500" />;
        }
    };

    const getTypeColor = (type: string) => {
        const colors: Record<string, string> = {
            welcome: 'bg-blue-100 text-blue-800',
            promotional: 'bg-purple-100 text-purple-800',
            bonus: 'bg-green-100 text-green-800',
            seasonal: 'bg-orange-100 text-orange-800',
        };
        return colors[type] || 'bg-gray-100 text-gray-800';
    };

    const copyToClipboard = (code: string) => {
        navigator.clipboard.writeText(code);
        // You could add a toast notification here
    };

    const generateRandomCode = () => {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 8; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        setNewGiftCode(prev => ({ ...prev, code: result }));
    };

    return (
        <AdminLayout title="Gift Codes">
            <Head title="Gift Codes - Admin" />

            <div className="p-6">
                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Codes</CardTitle>
                            <Gift className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{giftCodeStats.total_codes}</div>
                            <p className="text-xs text-muted-foreground">
                                All gift codes
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Codes</CardTitle>
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{giftCodeStats.active_codes}</div>
                            <p className="text-xs text-muted-foreground">
                                Currently active
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Redeemed</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(giftCodeStats.total_redeemed)}</div>
                            <p className="text-xs text-muted-foreground">
                                Total value redeemed
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Uses</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{giftCodeStats.total_uses}</div>
                            <p className="text-xs text-muted-foreground">
                                Times redeemed
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Created Today</CardTitle>
                            <Calendar className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{giftCodeStats.codes_today}</div>
                            <p className="text-xs text-muted-foreground">
                                New codes today
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Create Gift Code Form */}
                {showCreateForm && (
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Create New Gift Code</CardTitle>
                            <CardDescription>Generate a new gift code for users to redeem</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="code">Gift Code</Label>
                                    <div className="flex space-x-2">
                                        <Input
                                            id="code"
                                            value={newGiftCode.code}
                                            onChange={(e) => setNewGiftCode(prev => ({ ...prev, code: e.target.value }))}
                                            placeholder="Enter code"
                                        />
                                        <Button variant="outline" size="sm" onClick={generateRandomCode}>
                                            Generate
                                        </Button>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="amount">Amount (₦)</Label>
                                    <Input
                                        id="amount"
                                        type="number"
                                        value={newGiftCode.amount}
                                        onChange={(e) => setNewGiftCode(prev => ({ ...prev, amount: e.target.value }))}
                                        placeholder="50000"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="max_uses">Max Uses</Label>
                                    <Input
                                        id="max_uses"
                                        type="number"
                                        value={newGiftCode.max_uses}
                                        onChange={(e) => setNewGiftCode(prev => ({ ...prev, max_uses: e.target.value }))}
                                        placeholder="100"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="expires_at">Expires At</Label>
                                    <Input
                                        id="expires_at"
                                        type="datetime-local"
                                        value={newGiftCode.expires_at}
                                        onChange={(e) => setNewGiftCode(prev => ({ ...prev, expires_at: e.target.value }))}
                                    />
                                </div>
                            </div>
                            <div className="flex justify-end space-x-2 mt-4">
                                <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                                    Cancel
                                </Button>
                                <Button>
                                    Create Gift Code
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Gift Codes Table */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle>Gift Codes</CardTitle>
                                <CardDescription>Manage all gift codes and their usage</CardDescription>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button variant="outline" size="sm">
                                    <Filter className="w-4 h-4 mr-2" />
                                    Filter
                                </Button>
                                <Button size="sm" onClick={() => setShowCreateForm(!showCreateForm)}>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Create Code
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {giftCodes.length > 0 ? (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Code</TableHead>
                                        <TableHead>Amount</TableHead>
                                        <TableHead>Usage</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead>Expires</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {giftCodes.map((giftCode) => (
                                        <TableRow key={giftCode.id}>
                                            <TableCell>
                                                <div className="flex items-center space-x-3">
                                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-100">
                                                        {getStatusIcon(giftCode.status)}
                                                    </div>
                                                    <div className="flex items-center space-x-2">
                                                        <span className="font-mono font-medium">{giftCode.code}</span>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => copyToClipboard(giftCode.code)}
                                                            className="h-6 w-6 p-0"
                                                        >
                                                            <Copy className="h-3 w-3" />
                                                        </Button>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-semibold">{formatCurrency(giftCode.amount)}</span>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm">
                                                    <span className="font-medium">{giftCode.current_uses}</span>
                                                    <span className="text-muted-foreground"> / {giftCode.max_uses}</span>
                                                    <div className="w-full bg-gray-200 rounded-full h-1.5 mt-1">
                                                        <div
                                                            className="bg-blue-600 h-1.5 rounded-full"
                                                            style={{ width: `${(giftCode.current_uses / giftCode.max_uses) * 100}%` }}
                                                        ></div>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(giftCode.type)}`}>
                                                    {giftCode.type}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <span className="text-sm">
                                                    {new Date(giftCode.created_at).toLocaleDateString()}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <span className="text-sm">
                                                    {new Date(giftCode.expires_at).toLocaleDateString()}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                {getStatusBadge(giftCode.status)}
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <div className="flex items-center justify-end space-x-1">
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                    <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        ) : (
                            <div className="py-8 text-center">
                                <Gift className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                <p className="text-muted-foreground">No gift codes found</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
