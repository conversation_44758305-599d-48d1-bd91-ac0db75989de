import { Head } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button-new';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { formatCurrency } from '@/lib/currency';
import {
    Package,
    Plus,
    Edit,
    Trash2,
    Eye,
    Users,
    DollarSign,
    Calendar,
    TrendingUp,
    Star,
    Crown,
    Gem,
    Award,
    Shield
} from 'lucide-react';
import { useState } from 'react';

export default function AdminPlans() {
    const [showCreateForm, setShowCreateForm] = useState(false);
    const [newPlan, setNewPlan] = useState({
        name: '',
        min_amount: '',
        max_amount: '',
        duration: '',
        daily_return: '',
        description: ''
    });

    // Mock data for investment plans
    const planStats = {
        total_plans: 5,
        active_plans: 5,
        total_investors: 342,
        total_invested: 89750000 // ₦89.75M
    };

    const plans = [
        {
            id: 1,
            name: 'Bronze Plan',
            description: 'Perfect for beginners starting their investment journey',
            min_amount: 50000, // ₦50K
            max_amount: 500000, // ₦500K
            duration: 7, // days
            daily_return: 2.0, // %
            total_return: 14.0, // %
            investors_count: 89,
            total_invested: 12500000, // ₦12.5M
            status: 'active',
            icon: 'bronze',
            color: 'orange',
            created_at: '2024-12-01 10:00:00'
        },
        {
            id: 2,
            name: 'Silver Plan',
            description: 'Ideal for moderate investors seeking steady returns',
            min_amount: 100000, // ₦100K
            max_amount: 1000000, // ₦1M
            duration: 15, // days
            daily_return: 3.0, // %
            total_return: 45.0, // %
            investors_count: 76,
            total_invested: 18750000, // ₦18.75M
            status: 'active',
            icon: 'silver',
            color: 'gray',
            created_at: '2024-12-01 10:00:00'
        },
        {
            id: 3,
            name: 'Gold Plan',
            description: 'Premium plan for serious investors with higher returns',
            min_amount: 500000, // ₦500K
            max_amount: 5000000, // ₦5M
            duration: 30, // days
            daily_return: 5.0, // %
            total_return: 150.0, // %
            investors_count: 124,
            total_invested: 35000000, // ₦35M
            status: 'active',
            icon: 'gold',
            color: 'yellow',
            created_at: '2024-12-01 10:00:00'
        },
        {
            id: 4,
            name: 'Platinum Plan',
            description: 'Elite plan for high-net-worth individuals',
            min_amount: 2000000, // ₦2M
            max_amount: 10000000, // ₦10M
            duration: 60, // days
            daily_return: 7.0, // %
            total_return: 420.0, // %
            investors_count: 43,
            total_invested: 18500000, // ₦18.5M
            status: 'active',
            icon: 'platinum',
            color: 'purple',
            created_at: '2024-12-01 10:00:00'
        },
        {
            id: 5,
            name: 'Diamond Plan',
            description: 'Ultimate investment plan with maximum returns',
            min_amount: 5000000, // ₦5M
            max_amount: 20000000, // ₦20M
            duration: 90, // days
            daily_return: 10.0, // %
            total_return: 900.0, // %
            investors_count: 10,
            total_invested: 5000000, // ₦5M
            status: 'active',
            icon: 'diamond',
            color: 'blue',
            created_at: '2024-12-01 10:00:00'
        }
    ];

    const getPlanIcon = (icon: string) => {
        switch (icon) {
            case 'bronze':
                return <Award className="w-6 h-6 text-orange-600" />;
            case 'silver':
                return <Shield className="w-6 h-6 text-gray-600" />;
            case 'gold':
                return <Star className="w-6 h-6 text-yellow-600" />;
            case 'platinum':
                return <Crown className="w-6 h-6 text-purple-600" />;
            case 'diamond':
                return <Gem className="w-6 h-6 text-blue-600" />;
            default:
                return <Package className="w-6 h-6 text-gray-600" />;
        }
    };

    const getPlanColorClasses = (color: string) => {
        const colors: Record<string, string> = {
            orange: 'border-orange-200 bg-orange-50',
            gray: 'border-gray-200 bg-gray-50',
            yellow: 'border-yellow-200 bg-yellow-50',
            purple: 'border-purple-200 bg-purple-50',
            blue: 'border-blue-200 bg-blue-50',
        };
        return colors[color] || 'border-gray-200 bg-gray-50';
    };

    const getStatusBadge = (status: string) => {
        return status === 'active' ? (
            <Badge variant="default">Active</Badge>
        ) : (
            <Badge variant="destructive">Inactive</Badge>
        );
    };

    const handleCreatePlan = () => {
        // Handle create plan logic here
        console.log('Creating plan:', newPlan);
        setShowCreateForm(false);
        setNewPlan({
            name: '',
            min_amount: '',
            max_amount: '',
            duration: '',
            daily_return: '',
            description: ''
        });
    };

    return (
        <AdminLayout title="Investment Plans">
            <Head title="Investment Plans - Admin" />

            <div className="p-6">
                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Plans</CardTitle>
                            <Package className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{planStats.total_plans}</div>
                            <p className="text-xs text-muted-foreground">
                                Investment plans
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Plans</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{planStats.active_plans}</div>
                            <p className="text-xs text-muted-foreground">
                                Currently active
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Investors</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{planStats.total_investors}</div>
                            <p className="text-xs text-muted-foreground">
                                Active investors
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Invested</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(planStats.total_invested)}</div>
                            <p className="text-xs text-muted-foreground">
                                Across all plans
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Create Plan Form */}
                {showCreateForm && (
                    <Card className="mb-8">
                        <CardHeader>
                            <CardTitle>Create New Investment Plan</CardTitle>
                            <CardDescription>Add a new investment plan for users</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="plan_name">Plan Name</Label>
                                    <Input
                                        id="plan_name"
                                        value={newPlan.name}
                                        onChange={(e) => setNewPlan(prev => ({ ...prev, name: e.target.value }))}
                                        placeholder="e.g., Emerald Plan"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="min_amount">Minimum Amount (₦)</Label>
                                    <Input
                                        id="min_amount"
                                        type="number"
                                        value={newPlan.min_amount}
                                        onChange={(e) => setNewPlan(prev => ({ ...prev, min_amount: e.target.value }))}
                                        placeholder="100000"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="max_amount">Maximum Amount (₦)</Label>
                                    <Input
                                        id="max_amount"
                                        type="number"
                                        value={newPlan.max_amount}
                                        onChange={(e) => setNewPlan(prev => ({ ...prev, max_amount: e.target.value }))}
                                        placeholder="1000000"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="duration">Duration (Days)</Label>
                                    <Input
                                        id="duration"
                                        type="number"
                                        value={newPlan.duration}
                                        onChange={(e) => setNewPlan(prev => ({ ...prev, duration: e.target.value }))}
                                        placeholder="30"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="daily_return">Daily Return (%)</Label>
                                    <Input
                                        id="daily_return"
                                        type="number"
                                        step="0.1"
                                        value={newPlan.daily_return}
                                        onChange={(e) => setNewPlan(prev => ({ ...prev, daily_return: e.target.value }))}
                                        placeholder="5.0"
                                    />
                                </div>
                                <div className="space-y-2 md:col-span-2 lg:col-span-1">
                                    <Label htmlFor="description">Description</Label>
                                    <Input
                                        id="description"
                                        value={newPlan.description}
                                        onChange={(e) => setNewPlan(prev => ({ ...prev, description: e.target.value }))}
                                        placeholder="Plan description"
                                    />
                                </div>
                            </div>
                            <div className="flex justify-end space-x-2 mt-4">
                                <Button variant="outline" onClick={() => setShowCreateForm(false)}>
                                    Cancel
                                </Button>
                                <Button onClick={handleCreatePlan}>
                                    Create Plan
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* Plans Grid */}
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h2 className="text-2xl font-bold">Investment Plans</h2>
                        <p className="text-muted-foreground">Manage all investment plans and their configurations</p>
                    </div>
                    <Button onClick={() => setShowCreateForm(!showCreateForm)}>
                        <Plus className="w-4 h-4 mr-2" />
                        Create Plan
                    </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {plans.map((plan) => (
                        <Card key={plan.id} className={`relative ${getPlanColorClasses(plan.color)}`}>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                        {getPlanIcon(plan.icon)}
                                        <div>
                                            <CardTitle className="text-lg">{plan.name}</CardTitle>
                                            {getStatusBadge(plan.status)}
                                        </div>
                                    </div>
                                    <div className="flex items-center space-x-1">
                                        <Button variant="ghost" size="sm">
                                            <Eye className="h-4 w-4" />
                                        </Button>
                                        <Button variant="ghost" size="sm">
                                            <Edit className="h-4 w-4" />
                                        </Button>
                                        <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-700">
                                            <Trash2 className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                                <CardDescription className="mt-2">
                                    {plan.description}
                                </CardDescription>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-4">
                                    {/* Investment Range */}
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Investment Range</p>
                                        <p className="text-lg font-semibold">
                                            {formatCurrency(plan.min_amount)} - {formatCurrency(plan.max_amount)}
                                        </p>
                                    </div>

                                    {/* Duration & Returns */}
                                    <div className="grid grid-cols-2 gap-4">
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Duration</p>
                                            <p className="text-lg font-semibold flex items-center">
                                                <Calendar className="w-4 h-4 mr-1" />
                                                {plan.duration} days
                                            </p>
                                        </div>
                                        <div>
                                            <p className="text-sm font-medium text-muted-foreground">Daily Return</p>
                                            <p className="text-lg font-semibold text-green-600">
                                                {plan.daily_return}%
                                            </p>
                                        </div>
                                    </div>

                                    {/* Total Return */}
                                    <div>
                                        <p className="text-sm font-medium text-muted-foreground">Total Return</p>
                                        <p className="text-xl font-bold text-green-600">
                                            {plan.total_return}%
                                        </p>
                                    </div>

                                    {/* Statistics */}
                                    <div className="pt-4 border-t">
                                        <div className="grid grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <p className="text-muted-foreground">Investors</p>
                                                <p className="font-semibold">{plan.investors_count}</p>
                                            </div>
                                            <div>
                                                <p className="text-muted-foreground">Total Invested</p>
                                                <p className="font-semibold">{formatCurrency(plan.total_invested)}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        </AdminLayout>
    );
}
