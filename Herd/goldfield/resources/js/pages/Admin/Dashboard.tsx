import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import AdminLayout from '@/layouts/AdminLayout';
import { formatCurrency } from '@/lib/currency';
import {
    Users,
    TrendingUp,
    Activity,
    Clock
} from 'lucide-react';

interface DashboardStats {
    total_users: number;
    total_investments: number;
    total_transactions: number;
    active_investments: number;
    users_today: number;
    investments_today: number;
    pending_withdrawals: number;
}

interface User {
    id: number;
    name: string;
    email: string;
    balance: number;
    total_earnings: number;
    created_at: string;
}

interface Transaction {
    id: number;
    user: { name: string; email: string } | null;
    amount: number;
    type: string;
    status: string;
    description: string;
    created_at: string;
}

interface Investment {
    id: number;
    user: { name: string; email: string } | null;
    package: { name: string } | null;
    amount: number;
    status: string;
    daily_return: number;
    total_earned: number;
    created_at: string;
}

interface Props {
    stats: DashboardStats;
    recentUsers: User[];
    recentTransactions: Transaction[];
    recentInvestments: Investment[];
}

export default function AdminDashboard({ stats, recentUsers, recentTransactions, recentInvestments }: Props) {

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            active: 'default',
            completed: 'default',
            pending: 'secondary',
            failed: 'destructive',
            cancelled: 'outline',
        };

        return (
            <Badge variant={variants[status] || 'outline'}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    return (
        <AdminLayout title="Dashboard">
            <Head title="Admin Dashboard" />

            <div className="p-6">
                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.total_users.toLocaleString()}</div>
                            <p className="text-xs text-muted-foreground">
                                +{stats.users_today} today
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Investments</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(stats.total_investments)}</div>
                            <p className="text-xs text-muted-foreground">
                                +{formatCurrency(stats.investments_today)} today
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Investments</CardTitle>
                            <Activity className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.active_investments}</div>
                            <p className="text-xs text-muted-foreground">
                                Currently active
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pending Withdrawals</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats.pending_withdrawals}</div>
                            <p className="text-xs text-muted-foreground">
                                Awaiting approval
                            </p>
                        </CardContent>
                    </Card>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    {/* Recent Users */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Users</CardTitle>
                            <CardDescription>Latest user registrations</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentUsers.map((user) => (
                                    <div key={user.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                                        <div>
                                            <p className="font-medium">{user.name}</p>
                                            <p className="text-sm text-muted-foreground">{user.email}</p>
                                            <p className="text-xs text-muted-foreground">Joined {user.created_at}</p>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-medium">{formatCurrency(user.balance)}</p>
                                            <p className="text-sm text-muted-foreground">Balance</p>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Recent Transactions */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Transactions</CardTitle>
                            <CardDescription>Latest transaction activity</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {recentTransactions.map((transaction) => (
                                    <div key={transaction.id} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                                        <div>
                                            <p className="font-medium">{transaction.user?.name || 'Unknown User'}</p>
                                            <p className="text-sm text-muted-foreground">{transaction.description}</p>
                                            <p className="text-xs text-muted-foreground">{transaction.created_at}</p>
                                        </div>
                                        <div className="text-right space-y-1">
                                            <p className="font-medium">{formatCurrency(transaction.amount)}</p>
                                            {getStatusBadge(transaction.status)}
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Investments */}
                <Card className="mt-8">
                    <CardHeader>
                        <CardTitle>Recent Investments</CardTitle>
                        <CardDescription>Latest investment activity</CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-4">
                            {recentInvestments.map((investment) => (
                                <div key={investment.id} className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                                    <div className="flex-1">
                                        <p className="font-medium">{investment.user?.name || 'Unknown User'}</p>
                                        <p className="text-sm text-muted-foreground">{investment.package?.name || 'Unknown Package'}</p>
                                        <p className="text-xs text-muted-foreground">Invested on {investment.created_at}</p>
                                    </div>
                                    <div className="text-center">
                                        <p className="font-medium">{formatCurrency(investment.amount)}</p>
                                        <p className="text-sm text-muted-foreground">Investment</p>
                                    </div>
                                    <div className="text-center">
                                        <p className="font-medium">{formatCurrency(investment.total_earned)}</p>
                                        <p className="text-sm text-muted-foreground">Earned</p>
                                    </div>
                                    <div className="text-right">
                                        {getStatusBadge(investment.status)}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
