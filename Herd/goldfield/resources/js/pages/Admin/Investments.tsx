import { Head } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button-new';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { formatCurrency } from '@/lib/currency';
import {
    TrendingUp,
    DollarSign,
    Users,
    Calendar,
    Search,
    Filter,
    Eye,
    Edit,
    Play,
    Pause,
    StopCircle,
    CheckCircle,
    Clock,
    AlertTriangle,
    Package
} from 'lucide-react';
import { useState } from 'react';

export default function AdminInvestments() {
    const [searchTerm, setSearchTerm] = useState('');

    // Mock data for demonstration
    const investmentStats = {
        total_investments: 125750000, // ₦125.75M
        active_investments: 89,
        completed_investments: 234,
        total_returns_paid: 45200000, // ₦45.2M
        investments_today: 8,
        average_investment: 1412000 // ₦1.412M
    };

    const investments = [
        {
            id: 1,
            user: {
                name: 'Adebayo Ogundimu',
                email: '<EMAIL>',
                referral_code: 'GF12AB34CD'
            },
            package: {
                name: 'Gold Package',
                duration: 30,
                daily_return_rate: 5.0,
                min_amount: 500000,
                max_amount: 5000000
            },
            amount: 2500000, // ₦2.5M
            daily_return: 125000, // ₦125K
            total_earned: 1875000, // ₦1.875M (15 days)
            days_remaining: 15,
            status: 'active',
            created_at: '2025-01-12 10:30:00',
            last_payout: '2025-01-27 00:00:00'
        },
        {
            id: 2,
            user: {
                name: 'Fatima Abdullahi',
                email: '<EMAIL>',
                referral_code: 'GF56EF78GH'
            },
            package: {
                name: 'Silver Package',
                duration: 15,
                daily_return_rate: 3.0,
                min_amount: 100000,
                max_amount: 1000000
            },
            amount: 750000, // ₦750K
            daily_return: 22500, // ₦22.5K
            total_earned: 337500, // ₦337.5K (15 days - completed)
            days_remaining: 0,
            status: 'completed',
            created_at: '2025-01-01 14:15:00',
            last_payout: '2025-01-15 00:00:00'
        },
        {
            id: 3,
            user: {
                name: 'Chinedu Okoro',
                email: '<EMAIL>',
                referral_code: 'GF90IJ12KL'
            },
            package: {
                name: 'Platinum Package',
                duration: 60,
                daily_return_rate: 7.0,
                min_amount: 2000000,
                max_amount: 10000000
            },
            amount: 5000000, // ₦5M
            daily_return: 350000, // ₦350K
            total_earned: 2800000, // ₦2.8M (8 days)
            days_remaining: 52,
            status: 'active',
            created_at: '2025-01-19 09:45:00',
            last_payout: '2025-01-27 00:00:00'
        },
        {
            id: 4,
            user: {
                name: 'Aisha Mohammed',
                email: '<EMAIL>',
                referral_code: 'GF34MN56OP'
            },
            package: {
                name: 'Bronze Package',
                duration: 7,
                daily_return_rate: 2.0,
                min_amount: 50000,
                max_amount: 500000
            },
            amount: 300000, // ₦300K
            daily_return: 6000, // ₦6K
            total_earned: 18000, // ₦18K (3 days)
            days_remaining: 4,
            status: 'paused',
            created_at: '2025-01-24 16:20:00',
            last_payout: '2025-01-26 00:00:00'
        },
        {
            id: 5,
            user: {
                name: 'Ibrahim Yusuf',
                email: '<EMAIL>',
                referral_code: 'GF78QR90ST'
            },
            package: {
                name: 'Diamond Package',
                duration: 90,
                daily_return_rate: 10.0,
                min_amount: 5000000,
                max_amount: 20000000
            },
            amount: 8000000, // ₦8M
            daily_return: 800000, // ₦800K
            total_earned: 4000000, // ₦4M (5 days)
            days_remaining: 85,
            status: 'active',
            created_at: '2025-01-22 11:00:00',
            last_payout: '2025-01-27 00:00:00'
        }
    ];

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            active: 'default',
            completed: 'secondary',
            paused: 'outline',
            cancelled: 'destructive',
        };

        return (
            <Badge variant={variants[status] || 'outline'}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'active':
                return <Play className="w-4 h-4 text-green-500" />;
            case 'completed':
                return <CheckCircle className="w-4 h-4 text-blue-500" />;
            case 'paused':
                return <Pause className="w-4 h-4 text-yellow-500" />;
            case 'cancelled':
                return <StopCircle className="w-4 h-4 text-red-500" />;
            default:
                return <Clock className="w-4 h-4 text-gray-500" />;
        }
    };

    const getPackageColor = (packageName: string) => {
        const colors: Record<string, string> = {
            'Bronze Package': 'bg-orange-100 text-orange-800',
            'Silver Package': 'bg-gray-100 text-gray-800',
            'Gold Package': 'bg-yellow-100 text-yellow-800',
            'Platinum Package': 'bg-purple-100 text-purple-800',
            'Diamond Package': 'bg-blue-100 text-blue-800',
        };
        return colors[packageName] || 'bg-gray-100 text-gray-800';
    };

    const getProgressPercentage = (totalDays: number, daysRemaining: number) => {
        return ((totalDays - daysRemaining) / totalDays) * 100;
    };

    const filteredInvestments = investments.filter(investment =>
        investment.user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        investment.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        investment.package.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        investment.user.referral_code.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <AdminLayout title="Investments">
            <Head title="Investments - Admin" />

            <div className="p-6">
                {/* Investments Table */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle>Investment Management</CardTitle>
                                <CardDescription>Monitor and manage all user investments</CardDescription>
                            </div>
                            <div className="flex items-center space-x-2">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                    <Input
                                        placeholder="Search investments..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10 w-64"
                                    />
                                </div>
                                <Button variant="outline" size="sm">
                                    <Filter className="w-4 h-4 mr-2" />
                                    Filter
                                </Button>
                                <Button size="sm">
                                    Export
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {filteredInvestments.length > 0 ? (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Investor</TableHead>
                                        <TableHead>Package</TableHead>
                                        <TableHead>Amount</TableHead>
                                        <TableHead>Daily Return</TableHead>
                                        <TableHead>Progress</TableHead>
                                        <TableHead>Total Earned</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredInvestments.map((investment) => (
                                        <TableRow key={investment.id}>
                                            <TableCell>
                                                <div className="flex items-center space-x-3">
                                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
                                                        {getStatusIcon(investment.status)}
                                                    </div>
                                                    <div>
                                                        <p className="font-medium">{investment.user.name}</p>
                                                        <p className="text-sm text-muted-foreground">{investment.user.email}</p>
                                                        <p className="text-xs text-muted-foreground font-mono">{investment.user.referral_code}</p>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div>
                                                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPackageColor(investment.package.name)}`}>
                                                        {investment.package.name}
                                                    </span>
                                                    <p className="text-xs text-muted-foreground mt-1">
                                                        {investment.package.duration} days • {investment.package.daily_return_rate}% daily
                                                    </p>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-semibold">{formatCurrency(investment.amount)}</span>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-medium text-green-600">{formatCurrency(investment.daily_return)}</span>
                                            </TableCell>
                                            <TableCell>
                                                <div className="space-y-1">
                                                    <div className="flex justify-between text-sm">
                                                        <span>{investment.package.duration - investment.days_remaining}/{investment.package.duration} days</span>
                                                        <span>{Math.round(getProgressPercentage(investment.package.duration, investment.days_remaining))}%</span>
                                                    </div>
                                                    <div className="w-full bg-gray-200 rounded-full h-2">
                                                        <div
                                                            className="bg-blue-600 h-2 rounded-full"
                                                            style={{ width: `${getProgressPercentage(investment.package.duration, investment.days_remaining)}%` }}
                                                        ></div>
                                                    </div>
                                                    <p className="text-xs text-muted-foreground">
                                                        {investment.days_remaining} days remaining
                                                    </p>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-semibold text-blue-600">{formatCurrency(investment.total_earned)}</span>
                                            </TableCell>
                                            <TableCell>
                                                {getStatusBadge(investment.status)}
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <div className="flex items-center justify-end space-x-1">
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                    <Button variant="outline" size="sm">
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                    {investment.status === 'active' && (
                                                        <Button variant="outline" size="sm" className="text-yellow-600 hover:text-yellow-700">
                                                            <Pause className="h-4 w-4" />
                                                        </Button>
                                                    )}
                                                    {investment.status === 'paused' && (
                                                        <Button variant="outline" size="sm" className="text-green-600 hover:text-green-700">
                                                            <Play className="h-4 w-4" />
                                                        </Button>
                                                    )}
                                                    <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                                                        <StopCircle className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        ) : (
                            <div className="py-8 text-center">
                                <TrendingUp className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                <p className="text-muted-foreground">No investments found</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
