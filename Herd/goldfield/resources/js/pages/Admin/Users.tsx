import { Head } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button-new';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { formatCurrency } from '@/lib/currency';
import {
    Users,
    UserPlus,
    Search,
    Filter,
    Eye,
    Edit,
    Shield,
    Ban,
    CheckCircle,
    XCircle,
    Clock,
    Mail,
    Phone,
} from 'lucide-react';
import { useState } from 'react';

export default function AdminUsers() {
    const [searchTerm, setSearchTerm] = useState('');

    // Mock data for demonstration
    const userStats = {
        total_users: 1247,
        active_users: 1156,
        verified_users: 892,
        admin_users: 3,
        new_today: 12,
        blocked_users: 8
    };

    const users = [
        {
            id: 1,
            name: '<PERSON><PERSON><PERSON><PERSON>',
            email: '<EMAIL>',
            phone: '+234 ************',
            balance: 2450000, // ₦2.45M
            total_earnings: 3200000, // ₦3.2M
            total_investments: 5000000, // ₦5M
            referral_code: 'GF12AB34CD',
            referrals_count: 8,
            status: 'active',
            email_verified: true,
            is_admin: false,
            last_login: '2025-01-27 10:30:00',
            created_at: '2024-12-15 09:20:00'
        },
        {
            id: 2,
            name: 'Fatima Abdullahi',
            email: '<EMAIL>',
            phone: '+234 ************',
            balance: 850000, // ₦850K
            total_earnings: 1200000, // ₦1.2M
            total_investments: 2000000, // ₦2M
            referral_code: 'GF56EF78GH',
            referrals_count: 3,
            status: 'active',
            email_verified: true,
            is_admin: false,
            last_login: '2025-01-27 08:45:00',
            created_at: '2025-01-10 14:15:00'
        },
        {
            id: 3,
            name: 'Chinedu Okoro',
            email: '<EMAIL>',
            phone: '+234 ************',
            balance: 125000, // ₦125K
            total_earnings: 450000, // ₦450K
            total_investments: 500000, // ₦500K
            referral_code: 'GF90IJ12KL',
            referrals_count: 1,
            status: 'blocked',
            email_verified: false,
            is_admin: false,
            last_login: '2025-01-25 16:20:00',
            created_at: '2025-01-20 11:30:00'
        },
        {
            id: 4,
            name: 'Admin User',
            email: '<EMAIL>',
            phone: '+234 ************',
            balance: 0,
            total_earnings: 0,
            total_investments: 0,
            referral_code: 'GFADMIN001',
            referrals_count: 0,
            status: 'active',
            email_verified: true,
            is_admin: true,
            last_login: '2025-01-27 12:00:00',
            created_at: '2024-01-01 00:00:00'
        },
        {
            id: 5,
            name: 'Aisha Mohammed',
            email: '<EMAIL>',
            phone: '+234 ************',
            balance: 1750000, // ₦1.75M
            total_earnings: 2100000, // ₦2.1M
            total_investments: 3500000, // ₦3.5M
            referral_code: 'GF34MN56OP',
            referrals_count: 12,
            status: 'active',
            email_verified: true,
            is_admin: false,
            last_login: '2025-01-26 19:45:00',
            created_at: '2024-11-28 13:10:00'
        }
    ];

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            active: 'default',
            blocked: 'destructive',
            pending: 'secondary',
            suspended: 'outline',
        };

        return (
            <Badge variant={variants[status] || 'outline'}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'active':
                return <CheckCircle className="w-4 h-4 text-green-500" />;
            case 'blocked':
                return <Ban className="w-4 h-4 text-red-500" />;
            case 'pending':
                return <Clock className="w-4 h-4 text-yellow-500" />;
            case 'suspended':
                return <XCircle className="w-4 h-4 text-orange-500" />;
            default:
                return <Users className="w-4 h-4 text-gray-500" />;
        }
    };

    const filteredUsers = users.filter(user =>
        user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.referral_code.toLowerCase().includes(searchTerm.toLowerCase())
    );

    return (
        <AdminLayout title="Users">
            <Head title="Users - Admin" />

            <div className="p-6">
                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-6 mb-8">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                            <Users className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{userStats.total_users.toLocaleString()}</div>
                            <p className="text-xs text-muted-foreground">
                                All registered users
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Active Users</CardTitle>
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{userStats.active_users.toLocaleString()}</div>
                            <p className="text-xs text-muted-foreground">
                                Currently active
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Verified</CardTitle>
                            <Mail className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{userStats.verified_users.toLocaleString()}</div>
                            <p className="text-xs text-muted-foreground">
                                Email verified
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Admins</CardTitle>
                            <Shield className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{userStats.admin_users}</div>
                            <p className="text-xs text-muted-foreground">
                                Admin users
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">New Today</CardTitle>
                            <UserPlus className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{userStats.new_today}</div>
                            <p className="text-xs text-muted-foreground">
                                Registered today
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Blocked</CardTitle>
                            <Ban className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{userStats.blocked_users}</div>
                            <p className="text-xs text-muted-foreground">
                                Blocked users
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Users Table */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle>User Management</CardTitle>
                                <CardDescription>Manage all users and their accounts</CardDescription>
                            </div>
                            <div className="flex items-center space-x-2">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                    <Input
                                        placeholder="Search users..."
                                        value={searchTerm}
                                        onChange={(e) => setSearchTerm(e.target.value)}
                                        className="pl-10 w-64"
                                    />
                                </div>
                                <Button variant="outline" size="sm">
                                    <Filter className="w-4 h-4 mr-2" />
                                    Filter
                                </Button>
                                <Button size="sm">
                                    <UserPlus className="w-4 h-4 mr-2" />
                                    Add User
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {filteredUsers.length > 0 ? (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>User</TableHead>
                                        <TableHead>Contact</TableHead>
                                        <TableHead>Balance</TableHead>
                                        <TableHead>Investments</TableHead>
                                        <TableHead>Referrals</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Last Login</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {filteredUsers.map((user) => (
                                        <TableRow key={user.id}>
                                            <TableCell>
                                                <div className="flex items-center space-x-3">
                                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                                                        {user.is_admin ? (
                                                            <Shield className="w-4 h-4 text-blue-600" />
                                                        ) : (
                                                            getStatusIcon(user.status)
                                                        )}
                                                    </div>
                                                    <div>
                                                        <p className="font-medium flex items-center space-x-2">
                                                            <span>{user.name}</span>
                                                            {user.is_admin && (
                                                                <Badge variant="outline" className="text-xs">Admin</Badge>
                                                            )}
                                                            {user.email_verified && (
                                                                <CheckCircle className="w-3 h-3 text-green-500" />
                                                            )}
                                                        </p>
                                                        <p className="text-sm text-muted-foreground font-mono">{user.referral_code}</p>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm">
                                                    <p className="flex items-center space-x-1">
                                                        <Mail className="w-3 h-3" />
                                                        <span>{user.email}</span>
                                                    </p>
                                                    <p className="flex items-center space-x-1 text-muted-foreground">
                                                        <Phone className="w-3 h-3" />
                                                        <span>{user.phone}</span>
                                                    </p>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm">
                                                    <p className="font-semibold">{formatCurrency(user.balance)}</p>
                                                    <p className="text-muted-foreground">Earned: {formatCurrency(user.total_earnings)}</p>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-medium">{formatCurrency(user.total_investments)}</span>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-medium">{user.referrals_count}</span>
                                            </TableCell>
                                            <TableCell>
                                                {getStatusBadge(user.status)}
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm">
                                                    <p>{new Date(user.last_login).toLocaleDateString()}</p>
                                                    <p className="text-muted-foreground">{new Date(user.last_login).toLocaleTimeString()}</p>
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <div className="flex items-center justify-end space-x-1">
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                    <Button variant="outline" size="sm">
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                    {!user.is_admin && (
                                                        <>
                                                            <Button variant="outline" size="sm" className="text-blue-600 hover:text-blue-700">
                                                                <Shield className="h-4 w-4" />
                                                            </Button>
                                                            <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                                                                <Ban className="h-4 w-4" />
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        ) : (
                            <div className="py-8 text-center">
                                <Users className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                <p className="text-muted-foreground">No users found</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
