import { Head } from '@inertiajs/react';
import AdminLayout from '@/layouts/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button-new';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { formatCurrency } from '@/lib/currency';
import {
    CreditCard,
    TrendingDown,
    Clock,
    CheckCircle,
    XCircle,
    Eye,
    Filter,
    AlertTriangle,
    DollarSign
} from 'lucide-react';

export default function AdminWithdrawals() {
    // Mock data for demonstration
    const withdrawalStats = {
        total_withdrawals: ********, // ₦28.75M
        pending_withdrawals: 8,
        approved_today: 6,
        rejected_today: 2,
        total_today: 3200000, // ₦3.2M
    };

    const recentWithdrawals = [
        {
            id: 1,
            user: { name: '<PERSON><PERSON><PERSON><PERSON>', email: '<EMAIL>' },
            amount: 850000, // ₦850,000
            bank_details: {
                bank_name: 'First Bank of Nigeria',
                account_number: '**********',
                account_name: 'Adebayo Ogundimu'
            },
            status: 'pending',
            created_at: '2025-01-27 11:30:00',
            reference: 'WTH001',
            reason: null
        },
        {
            id: 2,
            user: { name: 'Fatima Abdullahi', email: '<EMAIL>' },
            amount: 1200000, // ₦1.2M
            bank_details: {
                bank_name: 'GTBank',
                account_number: '**********',
                account_name: 'Fatima Abdullahi'
            },
            status: 'approved',
            created_at: '2025-01-27 09:45:00',
            reference: 'WTH002',
            reason: null
        },
        {
            id: 3,
            user: { name: 'Chinedu Okoro', email: '<EMAIL>' },
            amount: 450000, // ₦450,000
            bank_details: {
                bank_name: 'Access Bank',
                account_number: '**********',
                account_name: 'Chinedu Okoro'
            },
            status: 'rejected',
            created_at: '2025-01-27 08:15:00',
            reference: 'WTH003',
            reason: 'Insufficient balance verification'
        },
        {
            id: 4,
            user: { name: 'Aisha Mohammed', email: '<EMAIL>' },
            amount: 675000, // ₦675,000
            bank_details: {
                bank_name: 'Zenith Bank',
                account_number: '**********',
                account_name: 'Aisha Mohammed'
            },
            status: 'processing',
            created_at: '2025-01-27 10:20:00',
            reference: 'WTH004',
            reason: null
        }
    ];

    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            approved: 'default',
            pending: 'secondary',
            rejected: 'destructive',
            processing: 'outline',
        };

        return (
            <Badge variant={variants[status] || 'outline'}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
            </Badge>
        );
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'approved':
                return <CheckCircle className="w-4 h-4 text-green-500" />;
            case 'pending':
                return <Clock className="w-4 h-4 text-yellow-500" />;
            case 'rejected':
                return <XCircle className="w-4 h-4 text-red-500" />;
            case 'processing':
                return <AlertTriangle className="w-4 h-4 text-blue-500" />;
            default:
                return <Clock className="w-4 h-4 text-gray-500" />;
        }
    };

    return (
        <AdminLayout title="Withdrawals">
            <Head title="Withdrawals - Admin" />

            <div className="p-6">
                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Withdrawals</CardTitle>
                            <CreditCard className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(withdrawalStats.total_withdrawals)}</div>
                            <p className="text-xs text-muted-foreground">
                                All time withdrawals
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pending</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{withdrawalStats.pending_withdrawals}</div>
                            <p className="text-xs text-muted-foreground">
                                Awaiting approval
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Approved Today</CardTitle>
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{withdrawalStats.approved_today}</div>
                            <p className="text-xs text-muted-foreground">
                                Processed today
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Rejected Today</CardTitle>
                            <XCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{withdrawalStats.rejected_today}</div>
                            <p className="text-xs text-muted-foreground">
                                Rejected today
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Today's Volume</CardTitle>
                            <TrendingDown className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(withdrawalStats.total_today)}</div>
                            <p className="text-xs text-muted-foreground">
                                Total withdrawn today
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Withdrawals Table */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle>Withdrawal Requests</CardTitle>
                                <CardDescription>Manage and review withdrawal requests</CardDescription>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button variant="outline" size="sm">
                                    <Filter className="w-4 h-4 mr-2" />
                                    Filter
                                </Button>
                                <Button size="sm">
                                    Export
                                </Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {recentWithdrawals.length > 0 ? (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>User</TableHead>
                                        <TableHead>Amount</TableHead>
                                        <TableHead>Bank Details</TableHead>
                                        <TableHead>Reference</TableHead>
                                        <TableHead>Date</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {recentWithdrawals.map((withdrawal) => (
                                        <TableRow key={withdrawal.id}>
                                            <TableCell>
                                                <div className="flex items-center space-x-3">
                                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
                                                        {getStatusIcon(withdrawal.status)}
                                                    </div>
                                                    <div>
                                                        <p className="font-medium">{withdrawal.user.name}</p>
                                                        <p className="text-sm text-muted-foreground">{withdrawal.user.email}</p>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-semibold text-red-600">{formatCurrency(withdrawal.amount)}</span>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm">
                                                    <p className="font-medium">{withdrawal.bank_details.bank_name}</p>
                                                    <p className="text-muted-foreground">{withdrawal.bank_details.account_number}</p>
                                                    <p className="text-muted-foreground">{withdrawal.bank_details.account_name}</p>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-mono text-sm">{withdrawal.reference}</span>
                                            </TableCell>
                                            <TableCell>
                                                <span className="text-sm">
                                                    {new Date(withdrawal.created_at).toLocaleDateString()}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <div className="space-y-1">
                                                    {getStatusBadge(withdrawal.status)}
                                                    {withdrawal.reason && (
                                                        <p className="text-xs text-red-600">{withdrawal.reason}</p>
                                                    )}
                                                </div>
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <div className="flex items-center justify-end space-x-1">
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="h-4 w-4" />
                                                    </Button>

                                                    {withdrawal.status === 'pending' && (
                                                        <>
                                                            <Button variant="outline" size="sm" className="text-green-600 hover:text-green-700">
                                                                <CheckCircle className="h-4 w-4" />
                                                            </Button>
                                                            <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                                                                <XCircle className="h-4 w-4" />
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        ) : (
                            <div className="py-8 text-center">
                                <CreditCard className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                <p className="text-muted-foreground">No withdrawal requests found</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
