import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button-new';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AdminLayout from '@/layouts/AdminLayout';
import { formatCurrency } from '@/lib/currency';
import { Head } from '@inertiajs/react';
import {
    // Mail,
    Bell,
    // Globe,
    Database,
    DollarSign,
    Edit,
    Eye,
    EyeOff,
    // Key,
    // Users,
    Package,
    Plus,
    RefreshCw,
    Save,
    Settings,
    Shield,
    Trash2,
} from 'lucide-react';
import { useState } from 'react';

export default function AdminSettings() {
    const [showApiKey, setShowApiKey] = useState(false);
    const [activeTab, setActiveTab] = useState('general');

    // Mock settings data
    const [settings, setSettings] = useState({
        // General Settings
        site_name: 'Goldfield Investment',
        site_description: 'Premium Investment Platform for Nigerians',
        site_url: 'https://goldfield.com',
        admin_email: '<EMAIL>',
        support_email: '<EMAIL>',

        // Investment Settings
        min_investment: 50000, // ₦50K
        max_investment: ********, // ₦20M
        referral_bonus_level1: 10, // 10%
        referral_bonus_level2: 5, // 5%
        welcome_bonus: 50000, // ₦50K

        // Security Settings
        api_key: 'gf_live_sk_1234567890abcdef',
        session_timeout: 30, // minutes
        max_login_attempts: 5,
        password_min_length: 8,

        // Notification Settings
        email_notifications: true,
        sms_notifications: false,
        push_notifications: true,

        // Payment Settings
        bank_name: 'First Bank of Nigeria',
        account_number: '**********',
        account_name: 'Goldfield Investment Ltd',

        // System Settings
        maintenance_mode: false,
        registration_enabled: true,
        auto_approve_deposits: false,
        auto_approve_withdrawals: false,
    });

    const investmentPackages = [
        {
            id: 1,
            name: 'Bronze Package',
            min_amount: 50000,
            max_amount: 500000,
            duration: 7,
            daily_return: 2.0,
            status: 'active',
        },
        {
            id: 2,
            name: 'Silver Package',
            min_amount: 100000,
            max_amount: 1000000,
            duration: 15,
            daily_return: 3.0,
            status: 'active',
        },
        {
            id: 3,
            name: 'Gold Package',
            min_amount: 500000,
            max_amount: 5000000,
            duration: 30,
            daily_return: 5.0,
            status: 'active',
        },
        {
            id: 4,
            name: 'Platinum Package',
            min_amount: 2000000,
            max_amount: 10000000,
            duration: 60,
            daily_return: 7.0,
            status: 'active',
        },
        {
            id: 5,
            name: 'Diamond Package',
            min_amount: 5000000,
            max_amount: ********,
            duration: 90,
            daily_return: 10.0,
            status: 'active',
        },
    ];

    const handleInputChange = (key: string, value: string | number | boolean) => {
        setSettings((prev) => ({
            ...prev,
            [key]: value,
        }));
    };

    const handleSaveSettings = () => {
        // Handle save settings logic here
        console.log('Saving settings:', settings);
    };

    const getStatusBadge = (status: string) => {
        return status === 'active' ? <Badge variant="default">Active</Badge> : <Badge variant="destructive">Inactive</Badge>;
    };

    const tabs = [
        { id: 'general', name: 'General', icon: Settings },
        { id: 'investment', name: 'Investment', icon: DollarSign },
        { id: 'packages', name: 'Packages', icon: Package },
        { id: 'security', name: 'Security', icon: Shield },
        { id: 'notifications', name: 'Notifications', icon: Bell },
        { id: 'system', name: 'System', icon: Database },
    ];

    return (
        <AdminLayout title="Settings">
            <Head title="Settings - Admin" />

            <div className="p-6">
                {/* Settings Navigation */}
                <div className="mb-8 flex space-x-1 rounded-lg bg-gray-100 p-1">
                    {tabs.map((tab) => (
                        <button
                            key={tab.id}
                            onClick={() => setActiveTab(tab.id)}
                            className={`flex items-center space-x-2 rounded-md px-4 py-2 text-sm font-medium transition-colors ${
                                activeTab === tab.id ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            <tab.icon className="h-4 w-4" />
                            <span>{tab.name}</span>
                        </button>
                    ))}
                </div>

                {/* General Settings */}
                {activeTab === 'general' && (
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Site Information</CardTitle>
                                <CardDescription>Basic site configuration</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="site_name">Site Name</Label>
                                    <Input
                                        id="site_name"
                                        value={settings.site_name}
                                        onChange={(e) => handleInputChange('site_name', e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="site_description">Site Description</Label>
                                    <Input
                                        id="site_description"
                                        value={settings.site_description}
                                        onChange={(e) => handleInputChange('site_description', e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="site_url">Site URL</Label>
                                    <Input id="site_url" value={settings.site_url} onChange={(e) => handleInputChange('site_url', e.target.value)} />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Contact Information</CardTitle>
                                <CardDescription>Admin and support contact details</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="admin_email">Admin Email</Label>
                                    <Input
                                        id="admin_email"
                                        type="email"
                                        value={settings.admin_email}
                                        onChange={(e) => handleInputChange('admin_email', e.target.value)}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="support_email">Support Email</Label>
                                    <Input
                                        id="support_email"
                                        type="email"
                                        value={settings.support_email}
                                        onChange={(e) => handleInputChange('support_email', e.target.value)}
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Investment Settings */}
                {activeTab === 'investment' && (
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>Investment Limits</CardTitle>
                                <CardDescription>Set minimum and maximum investment amounts</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="min_investment">Minimum Investment (₦)</Label>
                                    <Input
                                        id="min_investment"
                                        type="number"
                                        value={settings.min_investment}
                                        onChange={(e) => handleInputChange('min_investment', parseInt(e.target.value))}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="max_investment">Maximum Investment (₦)</Label>
                                    <Input
                                        id="max_investment"
                                        type="number"
                                        value={settings.max_investment}
                                        onChange={(e) => handleInputChange('max_investment', parseInt(e.target.value))}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="welcome_bonus">Welcome Bonus (₦)</Label>
                                    <Input
                                        id="welcome_bonus"
                                        type="number"
                                        value={settings.welcome_bonus}
                                        onChange={(e) => handleInputChange('welcome_bonus', parseInt(e.target.value))}
                                    />
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Referral Settings</CardTitle>
                                <CardDescription>Configure referral bonus percentages</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="referral_bonus_level1">Level 1 Referral Bonus (%)</Label>
                                    <Input
                                        id="referral_bonus_level1"
                                        type="number"
                                        value={settings.referral_bonus_level1}
                                        onChange={(e) => handleInputChange('referral_bonus_level1', parseFloat(e.target.value))}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="referral_bonus_level2">Level 2 Referral Bonus (%)</Label>
                                    <Input
                                        id="referral_bonus_level2"
                                        type="number"
                                        value={settings.referral_bonus_level2}
                                        onChange={(e) => handleInputChange('referral_bonus_level2', parseFloat(e.target.value))}
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Investment Packages */}
                {activeTab === 'packages' && (
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle>Investment Packages</CardTitle>
                                    <CardDescription>Manage investment packages and their returns</CardDescription>
                                </div>
                                <Button size="sm">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add Package
                                </Button>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Package Name</TableHead>
                                        <TableHead>Amount Range</TableHead>
                                        <TableHead>Duration</TableHead>
                                        <TableHead>Daily Return</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {investmentPackages.map((pkg) => (
                                        <TableRow key={pkg.id}>
                                            <TableCell className="font-medium">{pkg.name}</TableCell>
                                            <TableCell>
                                                {formatCurrency(pkg.min_amount)} - {formatCurrency(pkg.max_amount)}
                                            </TableCell>
                                            <TableCell>{pkg.duration} days</TableCell>
                                            <TableCell>{pkg.daily_return}%</TableCell>
                                            <TableCell>{getStatusBadge(pkg.status)}</TableCell>
                                            <TableCell className="text-right">
                                                <div className="flex items-center justify-end space-x-1">
                                                    <Button variant="outline" size="sm">
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                    <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                )}

                {/* Security Settings */}
                {activeTab === 'security' && (
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>API Configuration</CardTitle>
                                <CardDescription>Manage API keys and access</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="api_key">API Key</Label>
                                    <div className="flex space-x-2">
                                        <Input
                                            id="api_key"
                                            type={showApiKey ? 'text' : 'password'}
                                            value={settings.api_key}
                                            onChange={(e) => handleInputChange('api_key', e.target.value)}
                                        />
                                        <Button variant="outline" size="sm" onClick={() => setShowApiKey(!showApiKey)}>
                                            {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                        </Button>
                                    </div>
                                </div>
                                <Button variant="outline" size="sm">
                                    <RefreshCw className="mr-2 h-4 w-4" />
                                    Regenerate API Key
                                </Button>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Security Policies</CardTitle>
                                <CardDescription>Configure security settings</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="session_timeout">Session Timeout (minutes)</Label>
                                    <Input
                                        id="session_timeout"
                                        type="number"
                                        value={settings.session_timeout}
                                        onChange={(e) => handleInputChange('session_timeout', parseInt(e.target.value))}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="max_login_attempts">Max Login Attempts</Label>
                                    <Input
                                        id="max_login_attempts"
                                        type="number"
                                        value={settings.max_login_attempts}
                                        onChange={(e) => handleInputChange('max_login_attempts', parseInt(e.target.value))}
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="password_min_length">Minimum Password Length</Label>
                                    <Input
                                        id="password_min_length"
                                        type="number"
                                        value={settings.password_min_length}
                                        onChange={(e) => handleInputChange('password_min_length', parseInt(e.target.value))}
                                    />
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Notification Settings */}
                {activeTab === 'notifications' && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Notification Preferences</CardTitle>
                            <CardDescription>Configure system notifications</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h4 className="font-medium">Email Notifications</h4>
                                    <p className="text-sm text-muted-foreground">Send notifications via email</p>
                                </div>
                                <Button
                                    variant={settings.email_notifications ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handleInputChange('email_notifications', !settings.email_notifications)}
                                >
                                    {settings.email_notifications ? 'Enabled' : 'Disabled'}
                                </Button>
                            </div>
                            <div className="flex items-center justify-between">
                                <div>
                                    <h4 className="font-medium">SMS Notifications</h4>
                                    <p className="text-sm text-muted-foreground">Send notifications via SMS</p>
                                </div>
                                <Button
                                    variant={settings.sms_notifications ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handleInputChange('sms_notifications', !settings.sms_notifications)}
                                >
                                    {settings.sms_notifications ? 'Enabled' : 'Disabled'}
                                </Button>
                            </div>
                            <div className="flex items-center justify-between">
                                <div>
                                    <h4 className="font-medium">Push Notifications</h4>
                                    <p className="text-sm text-muted-foreground">Send push notifications to mobile apps</p>
                                </div>
                                <Button
                                    variant={settings.push_notifications ? 'default' : 'outline'}
                                    size="sm"
                                    onClick={() => handleInputChange('push_notifications', !settings.push_notifications)}
                                >
                                    {settings.push_notifications ? 'Enabled' : 'Disabled'}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}

                {/* System Settings */}
                {activeTab === 'system' && (
                    <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                        <Card>
                            <CardHeader>
                                <CardTitle>System Controls</CardTitle>
                                <CardDescription>Manage system-wide settings</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h4 className="font-medium">Maintenance Mode</h4>
                                        <p className="text-sm text-muted-foreground">Put site in maintenance mode</p>
                                    </div>
                                    <Button
                                        variant={settings.maintenance_mode ? 'destructive' : 'outline'}
                                        size="sm"
                                        onClick={() => handleInputChange('maintenance_mode', !settings.maintenance_mode)}
                                    >
                                        {settings.maintenance_mode ? 'Active' : 'Inactive'}
                                    </Button>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h4 className="font-medium">User Registration</h4>
                                        <p className="text-sm text-muted-foreground">Allow new user registrations</p>
                                    </div>
                                    <Button
                                        variant={settings.registration_enabled ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleInputChange('registration_enabled', !settings.registration_enabled)}
                                    >
                                        {settings.registration_enabled ? 'Enabled' : 'Disabled'}
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Auto-Approval Settings</CardTitle>
                                <CardDescription>Configure automatic approval settings</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-6">
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h4 className="font-medium">Auto-Approve Deposits</h4>
                                        <p className="text-sm text-muted-foreground">Automatically approve deposit requests</p>
                                    </div>
                                    <Button
                                        variant={settings.auto_approve_deposits ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleInputChange('auto_approve_deposits', !settings.auto_approve_deposits)}
                                    >
                                        {settings.auto_approve_deposits ? 'Enabled' : 'Disabled'}
                                    </Button>
                                </div>
                                <div className="flex items-center justify-between">
                                    <div>
                                        <h4 className="font-medium">Auto-Approve Withdrawals</h4>
                                        <p className="text-sm text-muted-foreground">Automatically approve withdrawal requests</p>
                                    </div>
                                    <Button
                                        variant={settings.auto_approve_withdrawals ? 'default' : 'outline'}
                                        size="sm"
                                        onClick={() => handleInputChange('auto_approve_withdrawals', !settings.auto_approve_withdrawals)}
                                    >
                                        {settings.auto_approve_withdrawals ? 'Enabled' : 'Disabled'}
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                )}

                {/* Save Button */}
                <div className="mt-8 flex justify-end">
                    <Button onClick={handleSaveSettings} className="flex items-center space-x-2">
                        <Save className="h-4 w-4" />
                        <span>Save Settings</span>
                    </Button>
                </div>
            </div>
        </AdminLayout>
    );
}
