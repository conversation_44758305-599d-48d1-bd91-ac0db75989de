import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, router, usePage } from '@inertiajs/react';
import { CheckCircle, Clock, AlertTriangle, RefreshCw, Eye, DollarSign } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import { Button } from '@/components/ui/button-new';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Admin Dashboard',
        href: '/admin/dashboard',
    },
    {
        title: 'Pending Deposits',
        href: '#',
    },
];

interface PendingDeposit {
    id: number;
    user: {
        id: number;
        name: string;
        email: string;
    };
    amount: number;
    reference: string;
    status: string;
    created_at: string;
    virtual_account: {
        account_number: string;
        account_name: string;
        bank_name: string;
    };
    pending_duration: number; // in seconds
}

interface PendingDepositsProps {
    auth: {
        user: {
            id: number;
            email: string;
            name: string;
            role: string;
        };
    };
    pendingDeposits: PendingDeposit[];
}

export default function PendingDeposits() {
    const { auth, pendingDeposits: initialDeposits } = usePage().props as unknown as PendingDepositsProps;
    const { toast } = useToast();
    const [deposits, setDeposits] = useState<PendingDeposit[]>(initialDeposits);
    const [isLoading, setIsLoading] = useState(false);
    const [approvingIds, setApprovingIds] = useState<Set<number>>(new Set());

    const formatAmount = (amount: number) => {
        return new Intl.NumberFormat('en-NG', {
            style: 'currency',
            currency: 'NGN',
        }).format(amount / 100);
    };

    const formatDuration = (seconds: number) => {
        if (seconds < 60) return `${seconds}s`;
        const minutes = Math.floor(seconds / 60);
        if (minutes < 60) return `${minutes}m ${seconds % 60}s`;
        const hours = Math.floor(minutes / 60);
        return `${hours}h ${minutes % 60}m`;
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString('en-NG', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const refreshDeposits = async () => {
        setIsLoading(true);
        try {
            router.reload({ only: ['pendingDeposits'] });
        } catch (error) {
            toast({
                title: 'Error',
                description: 'Failed to refresh deposits',
                variant: 'destructive',
            });
        } finally {
            setIsLoading(false);
        }
    };

    const approveDeposit = async (depositId: number) => {
        setApprovingIds(prev => new Set(prev).add(depositId));

        try {
            const response = await fetch(`/admin/deposits/${depositId}/approve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                credentials: 'include',
            });

            const data = await response.json();

            if (response.ok && data.status === 'success') {
                toast({
                    title: 'Deposit Approved! ✅',
                    description: `${data.data.user_name}'s deposit of ${formatAmount(data.data.amount)} has been approved`,
                    variant: 'default',
                });

                // Remove the approved deposit from the list
                setDeposits(prev => prev.filter(d => d.id !== depositId));
            } else {
                throw new Error(data.message || 'Failed to approve deposit');
            }
        } catch (error) {
            toast({
                title: 'Approval Failed',
                description: error instanceof Error ? error.message : 'Failed to approve deposit',
                variant: 'destructive',
            });
        } finally {
            setApprovingIds(prev => {
                const newSet = new Set(prev);
                newSet.delete(depositId);
                return newSet;
            });
        }
    };

    const viewPaymentPage = (depositId: number, virtualAccountId: number) => {
        const url = `/payment/virtual-account/${depositId}/${virtualAccountId}`;
        window.open(url, '_blank');
    };

    // Auto-refresh every 30 seconds
    useEffect(() => {
        const interval = setInterval(refreshDeposits, 30000);
        return () => clearInterval(interval);
    }, []);

    const eligibleForApproval = deposits.filter(d => d.pending_duration > 30);
    const recentDeposits = deposits.filter(d => d.pending_duration <= 30);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Pending Deposits - Admin" />

            <div className="space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Pending Deposits</h1>
                        <p className="text-gray-600">Manage and approve pending deposit transactions</p>
                    </div>
                    <Button
                        onClick={refreshDeposits}
                        disabled={isLoading}
                        variant="outline"
                        size="sm"
                    >
                        <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                        Refresh
                    </Button>
                </div>

                {/* Summary Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Pending</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{deposits.length}</div>
                            <p className="text-xs text-muted-foreground">
                                {deposits.length === 1 ? 'deposit' : 'deposits'} awaiting processing
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Eligible for Approval</CardTitle>
                            <AlertTriangle className="h-4 w-4 text-orange-500" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold text-orange-600">{eligibleForApproval.length}</div>
                            <p className="text-xs text-muted-foreground">
                                Pending for more than 30 seconds
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
                            <DollarSign className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {formatAmount(deposits.reduce((sum, d) => sum + d.amount, 0))}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Total pending amount
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Eligible for Approval Section */}
                {eligibleForApproval.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <AlertTriangle className="w-5 h-5 text-orange-500" />
                                Deposits Eligible for Manual Approval
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>User</TableHead>
                                        <TableHead>Amount</TableHead>
                                        <TableHead>Virtual Account</TableHead>
                                        <TableHead>Pending Duration</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {eligibleForApproval.map((deposit) => (
                                        <TableRow key={deposit.id}>
                                            <TableCell>
                                                <div>
                                                    <div className="font-medium">{deposit.user.name}</div>
                                                    <div className="text-sm text-gray-500">{deposit.user.email}</div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="font-semibold text-green-600">
                                                    {formatAmount(deposit.amount)}
                                                </div>
                                                <div className="text-xs text-gray-500">{deposit.reference}</div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm">
                                                    <div>{deposit.virtual_account.account_number}</div>
                                                    <div className="text-gray-500">{deposit.virtual_account.bank_name}</div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant="outline" className="text-orange-600 border-orange-200">
                                                    {formatDuration(deposit.pending_duration)}
                                                </Badge>
                                            </TableCell>
                                            <TableCell className="text-sm text-gray-500">
                                                {formatDate(deposit.created_at)}
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex gap-2">
                                                    <Button
                                                        size="sm"
                                                        onClick={() => viewPaymentPage(deposit.id, deposit.virtual_account.id)}
                                                        variant="outline"
                                                    >
                                                        <Eye className="w-4 h-4 mr-1" />
                                                        View
                                                    </Button>
                                                    <Button
                                                        size="sm"
                                                        onClick={() => approveDeposit(deposit.id)}
                                                        disabled={approvingIds.has(deposit.id)}
                                                        className="bg-green-600 hover:bg-green-700"
                                                    >
                                                        {approvingIds.has(deposit.id) ? (
                                                            <RefreshCw className="w-4 h-4 mr-1 animate-spin" />
                                                        ) : (
                                                            <CheckCircle className="w-4 h-4 mr-1" />
                                                        )}
                                                        Approve
                                                    </Button>
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                )}

                {/* Recent Deposits Section */}
                {recentDeposits.length > 0 && (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Clock className="w-5 h-5 text-blue-500" />
                                Recent Deposits (Under 30 seconds)
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>User</TableHead>
                                        <TableHead>Amount</TableHead>
                                        <TableHead>Virtual Account</TableHead>
                                        <TableHead>Pending Duration</TableHead>
                                        <TableHead>Created</TableHead>
                                        <TableHead>Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {recentDeposits.map((deposit) => (
                                        <TableRow key={deposit.id}>
                                            <TableCell>
                                                <div>
                                                    <div className="font-medium">{deposit.user.name}</div>
                                                    <div className="text-sm text-gray-500">{deposit.user.email}</div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="font-semibold text-green-600">
                                                    {formatAmount(deposit.amount)}
                                                </div>
                                                <div className="text-xs text-gray-500">{deposit.reference}</div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="text-sm">
                                                    <div>{deposit.virtual_account.account_number}</div>
                                                    <div className="text-gray-500">{deposit.virtual_account.bank_name}</div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant="outline" className="text-blue-600 border-blue-200">
                                                    {formatDuration(deposit.pending_duration)}
                                                </Badge>
                                            </TableCell>
                                            <TableCell className="text-sm text-gray-500">
                                                {formatDate(deposit.created_at)}
                                            </TableCell>
                                            <TableCell>
                                                <Button
                                                    size="sm"
                                                    onClick={() => viewPaymentPage(deposit.id, deposit.virtual_account.id)}
                                                    variant="outline"
                                                >
                                                    <Eye className="w-4 h-4 mr-1" />
                                                    View
                                                </Button>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </CardContent>
                    </Card>
                )}

                {/* Empty State */}
                {deposits.length === 0 && (
                    <Card>
                        <CardContent className="text-center py-12">
                            <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-4" />
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Pending Deposits</h3>
                            <p className="text-gray-600">All deposits have been processed successfully.</p>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
