import AdminLayout from '@/layouts/AdminLayout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PlanForm from './PlanForm';

interface Plan {
    id: number;
    name: string;
    description: string | null;
    min_deposit: number;
    max_deposit: number | null;
    type: 'fixed' | 'flexible';
    return: number | null;
    return_type: 'fixed' | 'percent' | null;
    return_periods: Record<string, number> | null;
    duration: number;
    features: string[];
    is_active: boolean;
    referral_bonus: number;
}

interface Props {
    plan: Plan;
}

export default function EditPlan({ plan }: Props) {
    return (
        <AdminLayout>
            <Head title={`Edit Plan - ${plan.name}`} />
            <div className="flex-1 space-y-4 p-8 pt-6">
                <div className="flex items-center justify-between space-y-2">
                    <div>
                        <h2 className="text-3xl font-bold tracking-tight">Edit Plan</h2>
                        <p className="text-muted-foreground">
                            Update the details of this investment plan
                        </p>
                    </div>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Plan Details</CardTitle>
                        <CardDescription>
                            Update the details below to modify this investment plan
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <PlanForm plan={plan} isEdit />
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
