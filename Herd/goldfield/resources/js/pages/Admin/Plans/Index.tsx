import AdminLayout from '@/layouts/AdminLayout';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Pencil, Plus, Trash2 } from 'lucide-react';
import { formatCurrency } from '@/lib/format';
import { toast } from 'sonner';
import { useState, useEffect } from 'react';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';
import Pagination from '@/components/Pagination';

interface Plan {
    id: number;
    name: string;
    description: string | null;
    min_deposit: number;
    max_deposit: number | null;
    type: 'fixed' | 'flexible';
    return: number | null;
    return_type: 'fixed' | 'percent' | null;
    return_periods: Record<string, number> | null;
    duration: number;
    features: string[] | null;
    is_active: boolean;
    referral_bonus: number;
    created_at: string;
    updated_at: string;
}

interface PaginationLink {
    url: string | null;
    label: string;
    active: boolean;
}

interface PlansData {
    data: Plan[];
    links: PaginationLink[];
}

export default function PlansIndex({ plans }: { plans: PlansData }) {
    // Debug: Log the plans data
    useEffect(() => {
        console.log('Plans data received:', plans);
    }, [plans]);

    const [deletingId, setDeletingId] = useState<number | null>(null);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const [planToDelete, setPlanToDelete] = useState<number | null>(null);
    const { flash } = usePage<{ 
        flash?: {
            message?: {
                type: 'success' | 'error' | 'warning' | 'info';
                text: string;
            };
            success?: string | (() => string);
            error?: string | (() => string);
        } 
    }>().props;
    
    const data = plans.data;

    // Show message from flash
    useEffect(() => {
        if (flash) {
            // Handle new format: { message: { type: 'success', text: '...' } }
            if (flash.message) {
                const { type, text } = flash.message;
                if (type === 'success') {
                    toast.success(text, {
                        className: 'pl-14', // Add padding for the check icon
                    });
                } else if (type === 'error') {
                    toast.error(text);
                }
            } 
            // Handle old format: { success: '...', error: '...' }
            else {
                if (typeof flash.success === 'function' ? flash.success() : flash.success) {
                    const message = typeof flash.success === 'function' ? flash.success() : flash.success;
                    toast.success(message, {
                        className: 'pl-14', // Add padding for the check icon
                    });
                }
                if (typeof flash.error === 'function' ? flash.error() : flash.error) {
                    const message = typeof flash.error === 'function' ? flash.error() : flash.error;
                    toast.error(message);
                }
            }
        }
    }, [flash]);

    const handleDeleteClick = (id: number) => {
        setPlanToDelete(id);
        setShowDeleteDialog(true);
    };

    const handleDelete = () => {
        if (!planToDelete) return;
        
        setDeletingId(planToDelete);
        
        router.delete(route('admin.plans.destroy', planToDelete), {
            preserveScroll: true,
            onSuccess: () => {
                toast.success('Plan deleted successfully');
            },
            onError: () => {
                toast.error('Failed to delete plan. Please try again.');
            },
            onFinish: () => {
                setDeletingId(null);
                setShowDeleteDialog(false);
                setPlanToDelete(null);
            },
        });
    };

    const getReturnText = (plan: Plan) => {
        if (plan.type === 'fixed') {
            return plan.return_type === 'percent' 
                ? `${plan.return}%` 
                : formatCurrency(plan.return || 0);
        } else {
            return 'Flexible';
        }
    };

    return (
        <>
            {/* Delete Confirmation Dialog */}
            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the plan and all its associated data.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={deletingId !== null}>Cancel</AlertDialogCancel>
                        <AlertDialogAction 
                            asChild
                            onClick={handleDelete}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            <button 
                                type="button"
                                className="inline-flex items-center justify-center rounded-md bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 border border-gray-300"
                                disabled={deletingId !== null}
                                onClick={handleDelete}
                            >
                                {deletingId === planToDelete ? (
                                    <>
                                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                        Deleting...
                                    </>
                                ) : 'Delete Plan'}
                            </button>
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            <AdminLayout>
            <Head title="Manage Plans" />
            <div className="flex-1 space-y-4 p-8 pt-6">
                <div className="flex items-center justify-between space-y-2">
                    <div>
                        <h2 className="text-3xl font-bold tracking-tight">Manage Plans</h2>
                        <p className="text-muted-foreground">
                            Create and manage investment plans for your users
                        </p>
                    </div>
                    <Link 
                        href={route('admin.plans.create')}
                        className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-sm hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:opacity-50"
                    >
                        <Plus className="mr-2 h-4 w-4" />
                        Add New Plan
                    </Link>
                </div>

                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle>Plans</CardTitle>
                                <CardDescription>
                                    Manage all available investment plans
                                </CardDescription>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Name</TableHead>
                                        <TableHead>Type</TableHead>
                                        <TableHead>Min Deposit</TableHead>
                                        <TableHead>Max Deposit</TableHead>
                                        <TableHead>Return</TableHead>
                                        <TableHead>Duration</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {data.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                                                No plans found. Create your first plan to get started.
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        data.map((plan: Plan) => (
                                            <TableRow key={plan.id}>
                                                <TableCell className="font-medium">
                                                    <div className="flex items-center space-x-2">
                                                        <span>{plan.name}</span>
                                                        {plan.is_active ? (
                                                            <Badge variant="outline" className="border-green-500 text-green-700">
                                                                Active
                                                            </Badge>
                                                        ) : (
                                                            <Badge variant="outline" className="border-gray-400 text-gray-500">
                                                                Inactive
                                                            </Badge>
                                                        )}
                                                    </div>
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant={plan.type === 'fixed' ? 'default' : 'secondary'}>
                                                        {plan.type.charAt(0).toUpperCase() + plan.type.slice(1)}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>{formatCurrency(plan.min_deposit)}</TableCell>
                                                <TableCell>{plan.max_deposit ? formatCurrency(plan.max_deposit) : 'No limit'}</TableCell>
                                                <TableCell>{getReturnText(plan)}</TableCell>
                                                <TableCell>
                                                    {plan.duration === 0 ? 'Lifetime' : `${plan.duration} days`}
                                                </TableCell>
                                                <TableCell>
                                                    <Badge variant={plan.is_active ? 'default' : 'outline'}>
                                                        {plan.is_active ? 'Active' : 'Inactive'}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell className="text-right">
                                                    <div className="flex items-center justify-end space-x-2">
                                                        <Link 
                                                            href={route('admin.plans.edit', plan.id)}
                                                            className="inline-flex items-center justify-center p-2 rounded-md hover:bg-accent"
                                                        >
                                                            <Pencil className="h-4 w-4" />
                                                            <span className="sr-only">Edit</span>
                                                        </Link>
                                                        <button
                                                            type="button"
                                                            className="inline-flex items-center justify-center p-2 rounded-md text-destructive hover:text-destructive hover:bg-accent disabled:opacity-50"
                                                            onClick={() => handleDeleteClick(plan.id)}
                                                            disabled={deletingId === plan.id}
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                            <span className="sr-only">Delete</span>
                                                        </button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    </CardContent>
                    {plans.links && plans.links.length > 3 && (
                        <div className="border-t border-gray-200 px-6 py-4">
                            <Pagination links={plans.links} />
                        </div>
                    )}
                </Card>
            </div>
            </AdminLayout>
        </>
    );
}
