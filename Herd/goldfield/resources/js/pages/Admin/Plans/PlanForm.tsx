import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { router, usePage } from '@inertiajs/react';
import { Plus, Trash2 } from 'lucide-react';
import { useState, useEffect } from 'react';
import { Plan } from '@/types/plan';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';

// Axios request config type
interface AxiosRequestConfig {
    headers?: Record<string, string>;
    params?: Record<string, string | number | boolean>;
    [key: string]: unknown;
}

// Extend the Window interface to include axios
declare global {
    interface Window {
        axios: {
            get: <T = unknown>(url: string, config?: AxiosRequestConfig) => Promise<{ data: T }>;
            post: <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig) => Promise<{ data: T }>;
            put: <T = unknown>(url: string, data?: unknown, config?: AxiosRequestConfig) => Promise<{ data: T }>;
            delete: <T = unknown>(url: string, config?: AxiosRequestConfig) => Promise<{ data: T }>;
        };
    }
}

// Type for return periods
type ReturnPeriods = Record<string, number>;

interface PlanFormData {
    name: string;
    description: string;
    min_deposit: string | number;
    max_deposit: string | number | null;
    type: 'fixed' | 'flexible';
    return: string | number | null;
    return_type: 'fixed' | 'percent' | null;
    return_periods: ReturnPeriods;
    duration: string | number;
    features: string[];
    is_active: boolean;
    referral_bonus: string | number;
}

interface PlanFormErrors {
    [key: string]: string | undefined;
    name?: string;
    description?: string;
    min_deposit?: string;
    max_deposit?: string;
    type?: string;
    return?: string;
    return_type?: string;
    return_periods?: string;
    duration?: string;
    features?: string;
    is_active?: string;
    referral_bonus?: string;
}

interface PlanFormProps {
    plan?: Partial<Plan>;
    isEdit?: boolean;
    errors?: PlanFormErrors;
}

// Custom Textarea component since we're not using the UI library's Textarea
interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
    id?: string;
    name: string;
    value: string | number | readonly string[] | undefined;
    onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
    placeholder?: string;
    rows?: number;
    className?: string;
}

const Textarea: React.FC<TextareaProps> = ({
    id,
    name,
    value,
    onChange,
    placeholder = '',
    rows = 3,
    className = '',
    ...props
}) => (
    <textarea
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        rows={rows}
        className={`flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
        {...props}
    />
);

export default function PlanForm({ plan, isEdit = false, errors: propErrors = {}, success }: PlanFormProps & { success?: string }) {
    const [formData, setFormData] = useState<PlanFormData>({
        name: plan?.name || '',
        description: plan?.description || '',
        min_deposit: plan?.min_deposit || '',
        max_deposit: plan?.max_deposit || '',
        type: plan?.type || 'fixed',
        return: plan?.return || '',
        return_type: plan?.return_type || 'fixed',
        return_periods: plan?.return_periods || {},
        duration: plan?.duration || 30,
        features: Array.isArray(plan?.features) ? plan.features : [],
        is_active: plan?.is_active !== undefined ? plan.is_active : true,
        referral_bonus: plan?.referral_bonus || 0,
    });

    const [newFeature, setNewFeature] = useState('');
    const [newPeriodDays, setNewPeriodDays] = useState('');
    const [newPeriodReturn, setNewPeriodReturn] = useState('');

    const [isProcessing, setIsProcessing] = useState(false);
    const [formErrors, setFormErrors] = useState<PlanFormErrors>(propErrors || {});
    const [showSuccess, setShowSuccess] = useState(false);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const { flash } = usePage().props as { flash?: { success?: string } };

    // Show success message if it exists in flash or props
    useEffect(() => {
        if (flash?.success || success) {
            setShowSuccess(true);
            const timer = setTimeout(() => setShowSuccess(false), 5000);
            return () => clearTimeout(timer);
        }
    }, [flash?.success, success]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value, type } = e.target;
        
        setFormData(prev => ({
            ...prev,
            [name]: type === 'number' ? (value === '' ? '' : parseFloat(value)) : value
        }));
    };

    const handleSwitchChange = (name: string, checked: boolean) => {
        setFormData(prev => ({
            ...prev,
            [name]: checked
        }));
    };

    const validateForm = (): boolean => {
        const errors: PlanFormErrors = {};
        
        if (!formData.name?.trim()) {
            errors.name = 'Plan name is required';
        }
        
        if (formData.min_deposit === '' || formData.min_deposit === null || formData.min_deposit === undefined) {
            errors.min_deposit = 'Minimum deposit is required';
        } else if (Number(formData.min_deposit) < 0) {
            errors.min_deposit = 'Minimum deposit cannot be negative';
        }
        
        if (formData.max_deposit !== null && formData.max_deposit !== '' && 
            Number(formData.max_deposit) < Number(formData.min_deposit)) {
            errors.max_deposit = 'Maximum deposit must be greater than minimum deposit';
        }
        
        if (formData.type === 'fixed' && (formData.return === '' || formData.return === null)) {
            errors.return = 'Return value is required for fixed plans';
        }
        
        if (formData.type === 'flexible' && Object.keys(formData.return_periods).length === 0) {
            errors.return_periods = 'At least one return period is required for flexible plans';
        }
        
        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        const isValid = validateForm();
        if (!isValid) {
            return;
        }
        
        setIsProcessing(true);
        setFormErrors({});
        
        // Prepare form data
        const formDataToSend = {
            ...formData,
            is_active: formData.is_active ? 1 : 0,
            max_deposit: formData.max_deposit || null,
        };
        
        try {
            if (isEdit && plan?.id) {
                await router.put(route('admin.plans.update', plan.id), formDataToSend, {
                    preserveScroll: true,
                    onError: (errors: Record<string, string>) => {
                        setFormErrors(errors);
                        setIsProcessing(false);
                    },
                    onFinish: () => {
                        setIsProcessing(false);
                    },
                });
            } else {
                await router.post(route('admin.plans.store'), formDataToSend, {
                    preserveScroll: true,
                    onError: (errors: Record<string, string>) => {
                        setFormErrors(errors);
                        setIsProcessing(false);
                    },
                    onFinish: () => {
                        setIsProcessing(false);
                    },
                });
            }
        } catch (error) {
            console.error('Error submitting form:', error);
            const errorMessage = error instanceof Error 
                ? error.message 
                : 'An error occurred while saving the plan. Please try again.';
                
            setFormErrors({
                ...formErrors,
                _general: errorMessage
            });
            
            alert(errorMessage);
        } finally {
            setIsProcessing(false);
        }
    };

    const addFeature = () => {
        if (newFeature.trim()) {
            const updatedFeatures = [...(formData.features || []), newFeature.trim()];
            setFormData(prev => ({
                ...prev,
                features: updatedFeatures
            }));
            setNewFeature('');
        }
    };

    const removeFeature = (index: number) => {
        const newFeatures = [...(formData.features || [])];
        newFeatures.splice(index, 1);
        setFormData(prev => ({
            ...prev,
            features: newFeatures
        }));
    };

    const addReturnPeriod = () => {
        if (newPeriodDays && newPeriodReturn) {
            setFormData(prev => ({
                ...prev,
                return_periods: {
                    ...prev.return_periods,
                    [newPeriodDays]: parseFloat(newPeriodReturn)
                }
            }));
            setNewPeriodDays('');
            setNewPeriodReturn('');
        }
    };

    const removeReturnPeriod = (days: string) => {
        const newReturnPeriods = { ...formData.return_periods };
        delete newReturnPeriods[days];
        setFormData(prev => ({
            ...prev,
            return_periods: newReturnPeriods
        }));
    };

    const handleDelete = async () => {
        if (!plan?.id) return;
        
        try {
            setIsProcessing(true);
            const response = await fetch(route('admin.plans.destroy', plan.id), {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                    'X-Requested-With': 'XMLHttpRequest',
                },
            });

            if (response.ok) {
                window.location.href = route('admin.plans.index');
            } else {
                const data = await response.json();
                throw new Error(data.message || 'Failed to delete plan');
            }
        } catch (error) {
            console.error('Error deleting plan:', error);
            const errorMessage = error instanceof Error ? error.message : 'An error occurred while deleting the plan.';
            setFormErrors({
                ...formErrors,
                _general: errorMessage
            });
        } finally {
            setIsProcessing(false);
            setShowDeleteDialog(false);
        }
    };

    return (
        <div className="space-y-6">
            {/* Success Alert */}
            {showSuccess && (
                <Alert variant="default" className="mb-6">
                    <AlertTitle>Success!</AlertTitle>
                    <AlertDescription>
                        {flash?.success || success || 'Operation completed successfully.'}
                    </AlertDescription>
                </Alert>
            )}

            {/* Delete Confirmation Dialog */}
            <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This action cannot be undone. This will permanently delete the plan and all its associated data.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={isProcessing}>Cancel</AlertDialogCancel>
                        <AlertDialogAction 
                            onClick={handleDelete}
                            disabled={isProcessing}
                            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                        >
                            {isProcessing ? 'Deleting...' : 'Delete'}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                    <CardHeader>
                        <CardTitle>Basic Information</CardTitle>
                        <CardDescription>
                            Basic details about the investment plan
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="name">Plan Name *</Label>
                            <Input
                                id="name"
                                name="name"
                                value={formData.name as string}
                                onChange={handleChange}
                                placeholder="e.g., Premium Plan"
                                required
                            />
                            {formErrors.name && <p className="text-sm text-red-500">{formErrors.name}</p>}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="description">Description</Label>
                            <Textarea
                                id="description"
                                name="description"
                                value={formData.description as string}
                                onChange={handleChange}
                                placeholder="Describe the plan features and benefits"
                                rows={3}
                                className="min-h-[80px]"
                            />
                            {formErrors.description && <p className="text-sm text-red-500">{formErrors.description}</p>}
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="min_deposit">Minimum Deposit *</Label>
                                <Input
                                    id="min_deposit"
                                    type="number"
                                    min="0"
                                    step="0.01"
                                    name="min_deposit"
                                    value={formData.min_deposit as string | number}
                                    onChange={handleChange}
                                    placeholder="0.00"
                                    required
                                />
                                {formErrors.min_deposit && <p className="text-sm text-red-500">{formErrors.min_deposit}</p>}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="max_deposit">Maximum Deposit</Label>
                                <Input
                                    id="max_deposit"
                                    type="number"
                                    min={formData.min_deposit || 0}
                                    step="0.01"
                                    name="max_deposit"
                                    value={formData.max_deposit as string | number | undefined || ''}
                                    onChange={handleChange}
                                    placeholder="Leave empty for no limit"
                                />
                                {formErrors.max_deposit && <p className="text-sm text-red-500">{formErrors.max_deposit}</p>}
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="duration">Duration (days) *</Label>
                            <Input
                                id="duration"
                                type="number"
                                min="0"
                                name="duration"
                                value={formData.duration as string | number}
                                onChange={handleChange}
                                placeholder="0 for lifetime"
                                required
                            />
                            {formErrors.duration && <p className="text-sm text-red-500">{formErrors.duration}</p>}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="referral_bonus">Referral Bonus (%)</Label>
                            <Input
                                id="referral_bonus"
                                type="number"
                                min="0"
                                max="100"
                                step="0.01"
                                name="referral_bonus"
                                value={formData.referral_bonus as string | number}
                                onChange={handleChange}
                                placeholder="0.00"
                            />
                            {formErrors.referral_bonus && <p className="text-sm text-red-500">{formErrors.referral_bonus}</p>}
                        </div>

                        <div className="flex items-center space-x-2">
                            <Switch
                                id="is_active"
                                checked={formData.is_active as boolean}
                                onCheckedChange={(checked) => handleSwitchChange('is_active', checked as boolean)}
                            />
                            <Label htmlFor="is_active">Active Plan</Label>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle>Return Settings</CardTitle>
                        <CardDescription>
                            Configure how returns are calculated for this plan
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                            <Label htmlFor="type">Plan Type *</Label>
                            <Select
                                value={formData.type as 'fixed' | 'flexible'}
                                onValueChange={(value: 'fixed' | 'flexible') => setFormData(prev => ({
                                    ...prev,
                                    type: value
                                }))}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select plan type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="fixed">Fixed Return</SelectItem>
                                    <SelectItem value="flexible">Flexible Return</SelectItem>
                                </SelectContent>
                            </Select>
                            {formErrors.type && <p className="text-sm text-red-500">{formErrors.type}</p>}
                        </div>

                        {formData.type === 'fixed' ? (
                            <>
                                <div className="space-y-2">
                                    <Label htmlFor="return">Return *</Label>
                                    <div className="flex space-x-2">
                                        <Input
                                            id="return"
                                            type="number"
                                            min="0"
                                            step="0.01"
                                            name="return"
                                            value={formData.return as string | number | undefined || ''}
                                            onChange={handleChange}
                                            placeholder="e.g., 10"
                                            className="flex-1"
                                            required
                                        />
                                        <Select
                                            value={formData.return_type as 'fixed' | 'percent' | undefined || 'percent'}
                                            onValueChange={(value: 'fixed' | 'percent') => setFormData(prev => ({
                                                ...prev,
                                                return_type: value
                                            }))}
                                        >
                                            <SelectTrigger className="w-[120px]">
                                                <SelectValue placeholder="Select type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="percent">%</SelectItem>
                                                <SelectItem value="fixed">Fixed</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        {formData.return_type === 'percent' 
                                            ? 'Percentage of the deposit amount' 
                                            : 'Fixed return amount'}
                                    </p>
                                    {formErrors.return && <p className="text-sm text-red-500">{formErrors.return}</p>}
                                    {formErrors.return_type && <p className="text-sm text-red-500">{formErrors.return_type}</p>}
                                </div>
                            </>
                        ) : (
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label>Return Periods</Label>
                                    <div className="space-y-2">
                                        {Object.entries(formData.return_periods).map(([days, returnValue]) => (
                                            <div key={days} className="flex items-center justify-between space-x-2">
                                                <div className="flex-1 bg-muted/50 p-2 rounded">
                                                    {days} days: {returnValue}%
                                                </div>
                                                <button
                                                    type="button"
                                                    className="inline-flex items-center justify-center rounded-md p-2 text-sm font-medium text-destructive hover:bg-destructive/10 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
                                                    onClick={() => removeReturnPeriod(days)}
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                    <span className="sr-only">Remove</span>
                                                </button>
                                            </div>
                                        ))}
                                    </div>
                                    <div className="flex space-x-2 mt-2">
                                        <Input
                                            type="number"
                                            min="1"
                                            placeholder="Days"
                                            value={newPeriodDays}
                                            onChange={(e) => setNewPeriodDays(e.target.value)}
                                        />
                                        <Input
                                            type="number"
                                            min="0.01"
                                            step="0.01"
                                            placeholder="Return %"
                                            value={newPeriodReturn}
                                            onChange={(e) => setNewPeriodReturn(e.target.value)}
                                        />
                                        <button
                                            type="button"
                                            className="inline-flex items-center justify-center rounded-md border border-input bg-background p-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
                                            onClick={addReturnPeriod}
                                            disabled={!newPeriodDays || !newPeriodReturn}
                                        >
                                            <Plus className="h-4 w-4" />
                                            <span className="sr-only">Add</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>

                <Card className="md:col-span-2">
                    <CardHeader>
                        <CardTitle>Plan Features</CardTitle>
                        <CardDescription>
                            List the key features of this plan that will be shown to users
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        <div className="space-y-2">
                            {(formData.features || []).map((feature: string, index: number) => (
                                <div key={index} className="flex items-center space-x-2">
                                    <div className="flex-1 bg-muted/50 p-2 rounded">
                                        {feature}
                                    </div>
                                    <button
                                        type="button"
                                        className="inline-flex items-center justify-center rounded-md p-2 text-sm font-medium text-destructive hover:bg-destructive/10 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
                                        onClick={() => removeFeature(index)}
                                    >
                                        <Trash2 className="h-4 w-4" />
                                        <span className="sr-only">Remove</span>
                                    </button>
                                </div>
                            ))}
                            <div className="flex space-x-2 mt-4">
                                <Input
                                    type="text"
                                    placeholder="Add a new feature"
                                    value={newFeature}
                                    onChange={(e) => setNewFeature(e.target.value)}
                                    onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                                    className="flex-1"
                                    disabled={isProcessing}
                                />
                                <button
                                    type="button"
                                    className="inline-flex items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
                                    onClick={addFeature}
                                    disabled={!newFeature.trim() || isProcessing}
                                >
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add
                                </button>
                            </div>
                            {formErrors.features && <p className="text-sm text-red-500">{formErrors.features}</p>}
                        </div>
                    </CardContent>
                </Card>
            </div>

            <div className="flex justify-end">
                <div className="space-x-4">
                    <button
                        type="button"
                        className="inline-flex items-center justify-center rounded-md border border-input bg-background px-4 py-2 text-sm font-medium shadow-sm transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
                        onClick={() => window.history.back()}
                        disabled={isProcessing}
                    >
                        Cancel
                    </button>
                    <button 
                        type="submit" 
                        className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50"
                        disabled={isProcessing}
                    >
                        {isProcessing ? 'Saving...' : isEdit ? 'Update Plan' : 'Create Plan'}
                    </button>
                </div>
            </div>
            </form>
        </div>
    );
}
