import AdminLayout from '@/layouts/AdminLayout';
import { Head } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PlanForm from './PlanForm';

export default function CreatePlan() {
    return (
        <AdminLayout>
            <Head title="Create New Plan" />
            <div className="flex-1 space-y-4 p-8 pt-6">
                <div className="flex items-center justify-between space-y-2">
                    <div>
                        <h2 className="text-3xl font-bold tracking-tight">Create New Plan</h2>
                        <p className="text-muted-foreground">
                            Set up a new investment plan for your users
                        </p>
                    </div>
                </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Plan Details</CardTitle>
                        <CardDescription>
                            Fill in the details below to create a new investment plan
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <PlanForm />
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
