import AdminLayout from '@/layouts/AdminLayout';
import { Head, useForm } from '@inertiajs/react';
import { CustomButton as Button } from '@/components/ui/custom-button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Save, X } from 'lucide-react';
import { useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';

interface PaymentGateway {
    id: number;
    gateway_name: string;
    gateway_type: string;
    is_active: boolean;
    credentials: {
        api_key?: string;
        secret_key?: string;
        contract_code?: string;
    };
}

export default function PaymentGatewayEdit({ paymentGateway }: { paymentGateway: PaymentGateway }) {
    const { toast } = useToast();
    const { data, setData, put, processing, errors, wasSuccessful } = useForm({
        gateway_name: paymentGateway.gateway_name,
        gateway_type: paymentGateway.gateway_type,
        is_active: paymentGateway.is_active,
        api_key: paymentGateway.credentials?.api_key || '',
        secret_key: paymentGateway.credentials?.secret_key || '',
        contract_code: paymentGateway.credentials?.contract_code || '',
    });

    useEffect(() => {
        if (wasSuccessful) {
            toast({
                title: 'Success',
                description: 'Payment gateway updated successfully',
                variant: 'default',
            });
        }
    }, [wasSuccessful, toast]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('admin.payment-gateways.update', paymentGateway.id));
    };

    return (
        <AdminLayout>
            <Head title={`Edit ${paymentGateway.gateway_name}`} />
            <div className="flex-1 space-y-4 p-8 pt-6">
                <div className="flex items-center justify-between space-y-2">
                    <h2 className="text-3xl font-bold tracking-tight">
                        Configure {paymentGateway.gateway_name}
                    </h2>
                </div>

                <form onSubmit={handleSubmit}>
                    <Card>
                        <CardHeader>
                            <CardTitle>Gateway Settings</CardTitle>
                            <CardDescription>
                                Update the configuration for {paymentGateway.gateway_name}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="space-y-2">
                                <Label htmlFor="gateway_name">Display Name</Label>
                                <Input
                                    id="gateway_name"
                                    value={data.gateway_name}
                                    onChange={(e) => setData('gateway_name', e.target.value)}
                                    className="mt-1 block w-full"
                                    placeholder="e.g., Monnify"
                                />
                                {errors.gateway_name && (
                                    <p className="text-sm text-red-500">{errors.gateway_name}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="api_key">API Key</Label>
                                <Input
                                    id="api_key"
                                    type="password"
                                    value={data.api_key}
                                    onChange={(e) => setData('api_key', e.target.value)}
                                    className="mt-1 block w-full"
                                    placeholder="Enter API Key"
                                />
                                {errors.api_key && (
                                    <p className="text-sm text-red-500">{errors.api_key}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="secret_key">Secret Key</Label>
                                <Input
                                    id="secret_key"
                                    type="password"
                                    value={data.secret_key}
                                    onChange={(e) => setData('secret_key', e.target.value)}
                                    className="mt-1 block w-full"
                                    placeholder="Enter secret key"
                                />
                                {errors.secret_key && (
                                    <p className="text-sm text-red-500">{errors.secret_key}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="contract_code">Contract Code</Label>
                                <Input
                                    id="contract_code"
                                    value={data.contract_code}
                                    onChange={(e) => setData('contract_code', e.target.value)}
                                    className="mt-1 block w-full"
                                    placeholder="Enter Contract Code"
                                />
                                {errors.contract_code && (
                                    <p className="text-sm text-red-500">{errors.contract_code}</p>
                                )}
                            </div>

                            <div className="flex items-center justify-between pt-4">
                                <div className="space-y-0.5">
                                    <Label className="text-base">Status</Label>
                                    <p className="text-sm text-muted-foreground">
                                        {data.is_active ? 'Active' : 'Inactive'} - This gateway is currently{' '}
                                        {data.is_active ? 'enabled' : 'disabled'} for transactions
                                    </p>
                                </div>
                                <Switch
                                    checked={data.is_active}
                                    onCheckedChange={(checked: boolean) => setData('is_active', checked)}
                                />
                            </div>

                            <div className="flex justify-end space-x-2 pt-6">
                                <Button type="button" variant="outline" asChild>
                                    <a href={route('admin.payment-gateways.index')}>
                                        <X className="mr-2 h-4 w-4" />
                                        Cancel
                                    </a>
                                </Button>
                                <Button type="submit" disabled={processing}>
                                    <Save className="mr-2 h-4 w-4" />
                                    {processing ? 'Saving...' : 'Save Changes'}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </form>
            </div>
        </AdminLayout>
    );
}
