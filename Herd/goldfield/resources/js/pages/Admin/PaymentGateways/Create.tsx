import AdminLayout from '@/layouts/AdminLayout';
import { Head, useForm } from '@inertiajs/react';
import { CustomButton as Button } from '@/components/ui/custom-button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Save, X } from 'lucide-react';

type FormData = {
    gateway_name: string;
    gateway_type: string;
    is_active: boolean;
    api_key: string;
    secret_key: string;
    contract_code: string;
};

export default function CreatePaymentGateway() {
    const { data, setData, post, processing, errors } = useForm<FormData>({
        gateway_name: '',
        gateway_type: 'monnify',
        is_active: false,
        api_key: '',
        secret_key: '',
        contract_code: '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('admin.payment-gateways.store'), {
            onSuccess: () => {
                // Handle success if needed
            },
            onError: (errors) => {
                console.error('Error creating payment gateway:', errors);
            },
            preserveScroll: true,
        });
    };

    const handleChange = (field: keyof FormData) => (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        const value = e.target.type === 'checkbox' 
            ? (e.target as HTMLInputElement).checked 
            : e.target.value;
            
        setData(field, value as never);
    };

    return (
        <AdminLayout>
            <Head title="Add Payment Gateway" />
            <div className="flex-1 space-y-4 p-8 pt-6">
                <div className="flex items-center justify-between space-y-2">
                    <h2 className="text-3xl font-bold tracking-tight">Add Payment Gateway</h2>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Gateway Information</CardTitle>
                            <CardDescription>
                                Enter the details of the payment gateway you want to add.
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="gateway_name">Gateway Name *</Label>
                                    <Input
                                        id="gateway_name"
                                        name="gateway_name"
                                        value={data.gateway_name}
                                        onChange={handleChange('gateway_name')}
                                        placeholder="e.g. Monnify Live"
                                        required
                                    />
                                    {errors.gateway_name && (
                                        <p className="text-sm text-red-500">{errors.gateway_name}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="gateway_type">Gateway Type *</Label>
                                    <Select
                                        value={data.gateway_type}
                                        onValueChange={(value) => setData('gateway_type', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select gateway type" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="monnify">Monnify</SelectItem>
                                            <SelectItem value="paystack">Paystack</SelectItem>
                                            <SelectItem value="flutterwave">Flutterwave</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    {errors.gateway_type && (
                                        <p className="text-sm text-red-500">{errors.gateway_type}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="api_key">API Key *</Label>
                                    <Input
                                        id="api_key"
                                        name="api_key"
                                        type="password"
                                        value={data.api_key}
                                        onChange={handleChange('api_key')}
                                        required={data.is_active}
                                        placeholder="Enter API key"
                                    />
                                    {errors.api_key && (
                                        <p className="text-sm text-red-500">{errors.api_key}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="secret_key">Secret Key *</Label>
                                    <Input
                                        id="secret_key"
                                        name="secret_key"
                                        type="password"
                                        value={data.secret_key}
                                        onChange={handleChange('secret_key')}
                                        required={data.is_active}
                                        placeholder="Enter secret key"
                                    />
                                    {errors.secret_key && (
                                        <p className="text-sm text-red-500">{errors.secret_key}</p>
                                    )}
                                </div>

                                {data.gateway_type === 'monnify' && (
                                    <div className="space-y-2">
                                        <Label htmlFor="contract_code">Contract Code *</Label>
                                        <Input
                                            id="contract_code"
                                            name="contract_code"
                                            value={data.contract_code}
                                            onChange={handleChange('contract_code')}
                                            required={data.gateway_type === 'monnify'}
                                            placeholder="Enter contract code"
                                        />
                                        {errors.contract_code && (
                                            <p className="text-sm text-red-500">{errors.contract_code}</p>
                                        )}
                                    </div>
                                )}

                                <div className="flex items-center space-x-2">
                                    <input
                                        type="checkbox"
                                        id="is_active"
                                        name="is_active"
                                        checked={data.is_active}
                                        onChange={(e) => setData('is_active', e.target.checked)}
                                        className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                                    />
                                    <Label htmlFor="is_active">Activate this gateway</Label>
                                </div>
                                {errors.is_active && (
                                    <p className="text-sm text-red-500">{errors.is_active}</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    <div className="flex justify-end space-x-4">
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => window.history.back()}
                            disabled={processing}
                        >
                            <X className="mr-2 h-4 w-4" />
                            Cancel
                        </Button>
                        <Button type="submit" disabled={processing}>
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? 'Saving...' : 'Save Gateway'}
                        </Button>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
