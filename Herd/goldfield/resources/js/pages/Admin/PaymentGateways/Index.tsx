import AdminLayout from '@/layouts/AdminLayout';
import { Head, usePage } from '@inertiajs/react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { CreditCard, Edit, Plus, Trash2 } from 'lucide-react';
import { useEffect, useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import { Link } from '@inertiajs/react';
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';

interface PaymentGateway {
    id: number;
    gateway_name: string;
    gateway_type: string;
    is_active: boolean;
    credentials: {
        api_key?: string;
        secret_key?: string;
        contract_code?: string;
    };
}

interface PageProps {
    paymentGateways?: PaymentGateway[];
    flash?: {
        success?: string | (() => string);
        error?: string | (() => string);
    };
}

export default function PaymentGatewaysIndex({ paymentGateways: initialPaymentGateways = [] }: PageProps) {
  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>(initialPaymentGateways);
  const { toast } = useToast();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [gatewayToDelete, setGatewayToDelete] = useState<PaymentGateway | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const { flash } = usePage<{ 
    flash?: {
      success?: string | (() => string);
      error?: string | (() => string);
    } 
  }>().props;

  useEffect(() => {
    if (flash) {
      if (typeof flash.success === 'function' ? flash.success() : flash.success) {
        const message = typeof flash.success === 'function' ? flash.success() : flash.success;
        toast({
          title: 'Success',
          description: message,
          variant: 'default',
          className: 'pl-14', // Add padding for the check icon
        });
      }
      if (typeof flash.error === 'function' ? flash.error() : flash.error) {
        const message = typeof flash.error === 'function' ? flash.error() : flash.error;
        toast({
          title: 'Error',
          description: message,
          variant: 'destructive',
        });
      }
    }
  }, [flash, toast]);

  const handleDeleteClick = (gateway: PaymentGateway) => {
    if (isDeleting) return;
    setGatewayToDelete(gateway);
    setIsDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!gatewayToDelete || isDeleting) return;
    
    try {
      setIsDeleting(true);
      const response = await fetch(route('admin.payment-gateways.destroy', gatewayToDelete.id), {
        method: 'DELETE',
        headers: {
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
        },
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || 'Failed to delete payment gateway');
      }

      // Update the local state to remove the deleted gateway
      setPaymentGateways(prevGateways => 
        prevGateways.filter(gateway => gateway.id !== gatewayToDelete.id)
      );

      toast({
        title: 'Success',
        description: data.message || 'Payment gateway deleted successfully.',
        variant: 'default',
        className: 'pl-14',
      });
    } catch (error) {
      console.error('Delete error:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to delete payment gateway. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDeleting(false);
      setIsDeleteDialogOpen(false);
      setGatewayToDelete(null);
    }
  };
  return (
    <AdminLayout>
      <Head title="Payment Gateways" />
      <div className="flex-1 space-y-4 p-8 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Payment Gateways</h2>
          <div className="flex items-center space-x-2">
            <a 
              href={route('admin.payment-gateways.create')}
              className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground ring-offset-background transition-colors hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              <Plus className="mr-2 h-4 w-4" />
              Add Gateway
            </a>
          </div>
        </div>

                <Card>
                    <CardHeader>
                        <CardTitle>Available Gateways</CardTitle>
                        <CardDescription>
                            Manage your payment gateways and their settings
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Type</TableHead>
                                    <TableHead>Status</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {paymentGateways.map((gateway) => (
                                    <TableRow key={gateway.id}>
                                        <TableCell className="font-medium">
                                            <div className="flex items-center space-x-2">
                                                <CreditCard className="h-5 w-5 text-muted-foreground" />
                                                <span>{gateway.gateway_name}</span>
                                            </div>
                                        </TableCell>
                                        <TableCell className="capitalize">{gateway.gateway_type}</TableCell>
                                        <TableCell>
                                            <Badge variant={gateway.is_active ? 'default' : 'secondary'}>
                                                {gateway.is_active ? 'Active' : 'Inactive'}
                                            </Badge>
                                        </TableCell>
                                        <TableCell className="text-right space-x-2">
                                            <Link 
                                                href={route('admin.payment-gateways.edit', gateway.id)}
                                                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-9 px-3"
                                            >
                                                <Edit className="h-4 w-4 mr-2" />
                                                Configure
                                            </Link>
                                            <button 
                                                onClick={() => handleDeleteClick(gateway)}
                                                className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-red-200 bg-background hover:bg-red-50 text-red-600 hover:text-red-700 h-9 px-3"
                                            >
                                                <Trash2 className="h-4 w-4 mr-2" />
                                                Delete
                                            </button>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>
            </div>

            <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                            This will permanently delete the payment gateway "{gatewayToDelete?.gateway_name}".
                            This action cannot be undone.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
                        <button
                            type="button"
                            onClick={handleDeleteConfirm}
                            disabled={isDeleting}
                            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-red-600 text-white hover:bg-red-700 h-10 px-4 py-2"
                        >
                            {isDeleting ? 'Deleting...' : 'Delete'}
                        </button>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AdminLayout>
    );
}
