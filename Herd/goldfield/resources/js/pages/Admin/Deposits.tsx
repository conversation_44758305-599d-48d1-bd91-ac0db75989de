import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button-new';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import AdminLayout from '@/layouts/AdminLayout';
import { formatCurrency } from '@/lib/currency';
import { Head } from '@inertiajs/react';
import { CheckCircle, Clock, Eye, Filter, TrendingUp, Wallet, XCircle } from 'lucide-react';

export default function AdminDeposits() {
    // Mock data for demonstration
    const depositStats = {
        total_deposits: ********, // ₦52.5M
        pending_deposits: 5,
        approved_today: 12,
        total_today: 6300000, // ₦6.3M
    };

    const recentDeposits = [
        {
            id: 1,
            user: { name: '<PERSON>', email: '<EMAIL>' },
            amount: 420000, // ₦420,000
            method: 'Bank Transfer',
            status: 'pending',
            created_at: '2025-01-27 10:30:00',
            reference: 'DEP001',
        },
        {
            id: 2,
            user: { name: '<PERSON>', email: '<EMAIL>' },
            amount: 1050000, // ₦1,050,000
            method: 'Bank Transfer',
            status: 'approved',
            created_at: '2025-01-27 09:15:00',
            reference: 'DEP002',
        },
        {
            id: 3,
            user: { name: 'Mike Johnson', email: '<EMAIL>' },
            amount: 210000, // ₦210,000
            method: 'Bank Transfer',
            status: 'rejected',
            created_at: '2025-01-27 08:45:00',
            reference: 'DEP003',
        },
    ];



    const getStatusBadge = (status: string) => {
        const variants: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
            approved: 'default',
            pending: 'secondary',
            rejected: 'destructive',
            processing: 'outline',
        };

        return <Badge variant={variants[status] || 'outline'}>{status.charAt(0).toUpperCase() + status.slice(1)}</Badge>;
    };

    const getStatusIcon = (status: string) => {
        switch (status) {
            case 'approved':
                return <CheckCircle className="h-4 w-4 text-green-500" />;
            case 'pending':
                return <Clock className="h-4 w-4 text-yellow-500" />;
            case 'rejected':
                return <XCircle className="h-4 w-4 text-red-500" />;
            default:
                return <Clock className="h-4 w-4 text-gray-500" />;
        }
    };

    return (
        <AdminLayout title="Deposits">
            <Head title="Deposits - Admin" />

            <div className="p-6">
                {/* Stats Grid */}
                <div className="mb-8 grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Total Deposits</CardTitle>
                            <Wallet className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(depositStats.total_deposits)}</div>
                            <p className="text-xs text-muted-foreground">All time deposits</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pending Deposits</CardTitle>
                            <Clock className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{depositStats.pending_deposits}</div>
                            <p className="text-xs text-muted-foreground">Awaiting approval</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Approved Today</CardTitle>
                            <CheckCircle className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{depositStats.approved_today}</div>
                            <p className="text-xs text-muted-foreground">Processed today</p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Today's Volume</CardTitle>
                            <TrendingUp className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{formatCurrency(depositStats.total_today)}</div>
                            <p className="text-xs text-muted-foreground">Total deposited today</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Deposits Table */}
                <Card>
                    <CardHeader>
                        <div className="flex items-center justify-between">
                            <div>
                                <CardTitle>Recent Deposits</CardTitle>
                                <CardDescription>Manage and review deposit requests</CardDescription>
                            </div>
                            <div className="flex items-center space-x-2">
                                <Button variant="outline" size="sm">
                                    <Filter className="mr-2 h-4 w-4" />
                                    Filter
                                </Button>
                                <Button size="sm">Export</Button>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        {recentDeposits.length > 0 ? (
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>User</TableHead>
                                        <TableHead>Amount</TableHead>
                                        <TableHead>Method</TableHead>
                                        <TableHead>Reference</TableHead>
                                        <TableHead>Date</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {recentDeposits.map((deposit) => (
                                        <TableRow key={deposit.id}>
                                            <TableCell>
                                                <div className="flex items-center space-x-3">
                                                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                                                        {getStatusIcon(deposit.status)}
                                                    </div>
                                                    <div>
                                                        <p className="font-medium">{deposit.user.name}</p>
                                                        <p className="text-sm text-muted-foreground">{deposit.user.email}</p>
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-semibold">{formatCurrency(deposit.amount)}</span>
                                            </TableCell>
                                            <TableCell>
                                                <span className="text-sm">{deposit.method}</span>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-mono text-sm">{deposit.reference}</span>
                                            </TableCell>
                                            <TableCell>
                                                <span className="text-sm">
                                                    {new Date(deposit.created_at).toLocaleDateString()}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                {getStatusBadge(deposit.status)}
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <div className="flex items-center justify-end space-x-1">
                                                    <Button variant="outline" size="sm">
                                                        <Eye className="h-4 w-4" />
                                                    </Button>

                                                    {deposit.status === 'pending' && (
                                                        <>
                                                            <Button variant="outline" size="sm" className="text-green-600 hover:text-green-700">
                                                                <CheckCircle className="h-4 w-4" />
                                                            </Button>
                                                            <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                                                                <XCircle className="h-4 w-4" />
                                                            </Button>
                                                        </>
                                                    )}
                                                </div>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        ) : (
                            <div className="py-8 text-center">
                                <Wallet className="mx-auto mb-4 h-12 w-12 text-muted-foreground" />
                                <p className="text-muted-foreground">No deposits found</p>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
