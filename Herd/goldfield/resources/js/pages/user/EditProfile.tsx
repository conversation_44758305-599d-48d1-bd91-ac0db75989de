import { Head, Link, useForm, usePage } from '@inertiajs/react';
import React, { useRef } from 'react';
import AppLayout from '@/layouts/app-layout';
import { ArrowLeft, Camera, Check, Mail, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

type User = {
    id: number;
    name: string;
    email: string;
    profile_photo_url: string | null;
};


const EditProfile = () => {
    const { user } = usePage<{ user: User }>().props;
    const fileInput = useRef<HTMLInputElement>(null);
    
    const { data, setData, post, processing, errors } = useForm({
        _method: 'put', // This will be used to spoof the HTTP method
        name: user.name,
        email: user.email,
        profile_photo: null as File | null,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        // Create form data and append all fields
        const formData = new FormData();
        formData.append('_method', 'put');
        formData.append('name', data.name);
        formData.append('email', data.email);
        
        if (data.profile_photo) {
            formData.append('profile_photo', data.profile_photo);
            console.log('Including profile photo in form data');
        }
        
        // Log form data for debugging
        console.log('Form data to be sent:');
        for (const pair of formData.entries()) {
            console.log(pair[0] + ': ', pair[1]);
        }
        
        console.log('Sending request to:', route('user.profile.update'));
        
        // Type assertion for the post function to include the options parameter
        (post as unknown as (url: string, data: Record<string, unknown>, options: { forceFormData: boolean, onSuccess: () => void, onError: (errors: Record<string, string>) => void }) => void)(
            route('user.profile.update'),
            {
                _method: 'put',
                name: data.name,
                email: data.email,
                profile_photo: data.profile_photo,
            },
            {
                forceFormData: true,
                onSuccess: () => {
                    console.log('Profile update successful');
                    // Optionally add a success message
                    // toast.success('Profile updated successfully!');
                },
                onError: (errors: Record<string, string>) => {
                    console.error('Error updating profile:', errors);
                    alert('Failed to update profile. Please check the form for errors.');
                },
            }
        );
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
            setData('profile_photo', e.target.files[0]);
        }
    };

    const removeProfilePhoto = () => {
        setData('profile_photo', null);
        if (fileInput.current) {
            fileInput.current.value = '';
        }
    };

    return (
        <AppLayout>
            <Head title="Edit Profile" />
            <div className="py-8">
                <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="mb-6">
                        <Link 
                            href={route('user.profile')} 
                            className="inline-flex items-center text-sm text-orange-600 hover:text-orange-700 dark:text-orange-400 dark:hover:text-orange-300"
                        >
                            <ArrowLeft className="h-4 w-4 mr-1" />
                            Back to Profile
                        </Link>
                        <h1 className="text-2xl font-bold text-gray-900 dark:text-white mt-2">Edit Profile</h1>
                    </div>

                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
                        <div className="p-6">
                            <form onSubmit={handleSubmit}>
                                {/* Profile Photo */}
                                <div className="mb-8">
                                    <Label htmlFor="profile_photo">Profile Photo</Label>
                                    <div className="mt-2 flex items-center">
                                        <div className="relative">
                                            <div className="h-24 w-24 rounded-full border-2 border-gray-200 dark:border-gray-700 overflow-hidden">
                                                {data.profile_photo ? (
                                                    <img 
                                                        src={URL.createObjectURL(data.profile_photo)} 
                                                        alt="New profile"
                                                        className="h-full w-full object-cover"
                                                    />
                                                ) : (
                                                    <img 
                                                        src={user.profile_photo_url || `https://ui-avatars.com/api/?name=${encodeURIComponent(user.name)}&color=7F9CF5&background=EBF4FF`} 
                                                        alt={user.name}
                                                        className="h-full w-full object-cover"
                                                    />
                                                )}
                                            </div>
                                            <button
                                                type="button"
                                                onClick={() => fileInput.current?.click()}
                                                className="absolute -bottom-2 -right-2 bg-orange-600 hover:bg-orange-700 text-white p-1.5 rounded-full shadow-sm"
                                            >
                                                <Camera className="h-4 w-4" />
                                            </button>
                                            <input
                                                id="profile_photo"
                                                ref={fileInput}
                                                type="file"
                                                className="hidden"
                                                onChange={handleFileChange}
                                                accept="image/*"
                                            />
                                        </div>
                                        {data.profile_photo && (
                                            <button
                                                type="button"
                                                onClick={removeProfilePhoto}
                                                className="ml-4 text-sm text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                                            >
                                                Remove photo
                                            </button>
                                        )}
                                    </div>
                                    {errors.profile_photo && (
                                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                                            {errors.profile_photo}
                                        </p>
                                    )}
                                </div>

                                {/* Name */}
                                <div className="mb-6">
                                    <Label htmlFor="name">Name</Label>
                                    <div className="mt-1 relative rounded-md shadow-sm">
                                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <User className="h-5 w-5 text-gray-400" />
                                        </div>
                                        <Input
                                            id="name"
                                            type="text"
                                            value={data.name}
                                            onChange={(e) => setData('name', e.target.value)}
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                    {errors.name && (
                                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                                            {errors.name}
                                        </p>
                                    )}
                                </div>

                                {/* Email */}
                                <div className="mb-8">
                                    <Label htmlFor="email">Email</Label>
                                    <div className="mt-1 relative rounded-md shadow-sm">
                                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                            <Mail className="h-5 w-5 text-gray-400" />
                                        </div>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={data.email}
                                            onChange={(e) => setData('email', e.target.value)}
                                            className="pl-10"
                                            required
                                        />
                                    </div>
                                    {errors.email && (
                                        <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                                            {errors.email}
                                        </p>
                                    )}
                                </div>

                                {/* Buttons */}
                                <div className="flex justify-end space-x-3">
                                    <Link
                                        href={route('user.profile')}
                                        className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                                    >
                                        Cancel
                                    </Link>
                                    <Button
                                        type="submit"
                                        disabled={processing}
                                        className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                                    >
                                        <Check className="h-4 w-4 mr-2" />
                                        {processing ? 'Saving...' : 'Save Changes'}
                                    </Button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
};

export default EditProfile;
