import { Head, <PERSON> } from '@inertiajs/react';
import React from 'react';
import AppLayout from '@/layouts/app-layout';
import { Mail, Calendar, Shield, Check, X, Edit } from 'lucide-react';

type ProfileProps = {
    user: {
        id: number;
        name: string;
        email: string;
        profile_photo_url: string | null;
        created_at: string;
        email_verified_at: string | null;
        last_login_at: string;
        account_status: {
            email_verified: boolean;
            kyc_verified: boolean;
        };
    };
};

const UserProfile = ({ user }: ProfileProps) => {
    const getInitials = (name: string) => {
        return name.split(' ').map(n => n[0]).join('').toUpperCase();
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    return (
        <AppLayout>
            <Head title={`${user.name}'s Profile`} />
            <div className="py-8">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    {/* Profile Header */}
                    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm overflow-hidden">
                        {/* Profile Banner */}
                        <div className="h-24 sm:h-32 bg-gradient-to-r from-orange-500 to-amber-600"></div>
                        
                        {/* Profile Info */}
                        <div className="px-6 pb-6 -mt-16 relative">
                            <div className="flex flex-col sm:flex-row sm:items-end sm:justify-between">
                                <div className="flex flex-col sm:flex-row sm:items-end">
                                    <div className="h-24 w-24 sm:h-32 sm:w-32 rounded-full border-4 border-white dark:border-gray-800 bg-white dark:bg-gray-700 overflow-hidden flex-shrink-0">
                                        {user.profile_photo_url ? (
                                            <img 
                                                src={user.profile_photo_url} 
                                                alt={user.name}
                                                className="h-full w-full object-cover"
                                            />
                                        ) : (
                                            <div className="h-full w-full bg-gray-200 dark:bg-gray-600 flex items-center justify-center text-2xl sm:text-4xl font-bold text-gray-600 dark:text-gray-300">
                                                {getInitials(user.name)}
                                            </div>
                                        )}
                                    </div>
                                    <div className="mt-3 sm:mt-0 sm:ml-6 mb-2">
                                        <h1 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">{user.name}</h1>
                                        <div className="flex flex-wrap items-center text-gray-600 dark:text-gray-300 mt-1">
                                            <Mail className="h-4 w-4 mr-2" />
                                            <span>{user.email}</span>
                                            {user.account_status.email_verified ? (
                                                <span className="ml-2 px-2 py-0.5 text-xs bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 rounded-full flex items-center">
                                                    <Check className="h-3 w-3 mr-1" />
                                                    Verified
                                                </span>
                                            ) : (
                                                <span className="ml-2 px-2 py-0.5 text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400 rounded-full flex items-center">
                                                    <X className="h-3 w-3 mr-1" />
                                                    Unverified
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <div className="mt-4 sm:mt-0 sm:ml-4">
                                    <Link 
                                        href={route('user.profile.edit')}
                                        className="inline-flex items-center px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white text-sm font-medium rounded-md transition-colors"
                                    >
                                        <Edit className="h-4 w-4 mr-2" />
                                        Edit Profile
                                    </Link>
                                </div>
                            </div>
                        </div>

                        {/* Profile Details */}
                        <div className="border-t border-gray-200 dark:border-gray-700 px-4 sm:px-6 py-6">
                            <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Account Information</h2>
                            
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Member Since</h3>
                                    <div className="mt-1 flex items-center text-gray-900 dark:text-gray-100">
                                        <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                                        <p>{formatDate(user.created_at)}</p>
                                    </div>
                                </div>
                                
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Last Login</h3>
                                    <div className="mt-1 flex items-center text-gray-900 dark:text-gray-100">
                                        <Calendar className="h-5 w-5 text-gray-400 mr-2" />
                                        <p>{formatDate(user.last_login_at)}</p>
                                    </div>
                                </div>
                                
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Email Verification</h3>
                                    <div className="mt-1 flex items-center">
                                        {user.account_status.email_verified ? (
                                            <span className="inline-flex items-center text-green-600 dark:text-green-400">
                                                <Check className="h-5 w-5 mr-1" />
                                                Verified
                                            </span>
                                        ) : (
                                            <span className="inline-flex items-center text-yellow-600 dark:text-yellow-400">
                                                <X className="h-5 w-5 mr-1" />
                                                Not Verified
                                            </span>
                                        )}
                                    </div>
                                </div>
                                
                                <div>
                                    <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">KYC Status</h3>
                                    <div className="mt-1 flex items-center">
                                        {user.account_status.kyc_verified ? (
                                            <span className="inline-flex items-center text-green-600 dark:text-green-400">
                                                <Check className="h-5 w-5 mr-1" />
                                                Verified
                                            </span>
                                        ) : (
                                            <Link 
                                                href="/kyc" 
                                                className="text-orange-600 dark:text-orange-400 hover:underline text-sm font-medium inline-flex items-center"
                                            >
                                                <Shield className="h-4 w-4 mr-1" />
                                                Complete KYC
                                            </Link>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    {/* Additional Sections */}
                    <div className="mt-6 sm:mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                        {/* Security Card */}
                        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 sm:p-6">
                            <div className="flex items-center justify-between mb-4">
                                <h2 className="text-lg font-medium text-gray-900 dark:text-white">Security</h2>
                                <button className="text-sm text-orange-600 dark:text-orange-400 hover:underline">
                                    View All
                                </button>
                            </div>
                            <ul className="space-y-3">
                                <li className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600 dark:text-gray-300">Two-Factor Authentication</span>
                                    <button className="text-sm text-orange-600 dark:text-orange-400 hover:underline">
                                        Enable
                                    </button>
                                </li>
                                <li className="flex items-center justify-between">
                                    <span className="text-sm text-gray-600 dark:text-gray-300">Change Password</span>
                                    <button className="text-sm text-orange-600 dark:text-orange-400 hover:underline">
                                        Change
                                    </button>
                                </li>
                            </ul>
                        </div>
                        
                        {/* Activity Card */}
                        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 sm:p-6">
                            <div className="flex items-center justify-between mb-4">
                                <h2 className="text-lg font-medium text-gray-900 dark:text-white">Recent Activity</h2>
                                <button className="text-sm text-orange-600 dark:text-orange-400 hover:underline">
                                    View All
                                </button>
                            </div>
                            <div className="space-y-4">
                                <div className="flex items-start">
                                    <div className="h-10 w-10 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center flex-shrink-0">
                                        <Mail className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                                    </div>
                                    <div className="ml-3">
                                        <p className="text-sm font-medium text-gray-900 dark:text-white">Email verified</p>
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            {user.account_status.email_verified ? 'Your email was verified' : 'Please verify your email'}
                                        </p>
                                    </div>
                                </div>
                                <div className="flex items-start">
                                    <div className="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center flex-shrink-0">
                                        <Calendar className="h-5 w-5 text-green-600 dark:text-green-400" />
                                    </div>
                                    <div className="ml-3">
                                        <p className="text-sm font-medium text-gray-900 dark:text-white">Account created</p>
                                        <p className="text-xs text-gray-500 dark:text-gray-400">
                                            Joined on {formatDate(user.created_at).split(' at ')[0]}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
};

export default UserProfile;
