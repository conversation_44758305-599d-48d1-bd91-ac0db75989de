import { ActionsBar } from '@/components/dashboard/actions-bar';
import { WelcomeCard } from '@/components/dashboard/welcome-card';
import { InvestmentPlans } from '@/components/dashboard/investment-plans';
import { QuickTransactions } from '@/components/dashboard/quick-transactions';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem, type PageProps, type User } from '@/types';
import { Head } from '@inertiajs/react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/user/dashboard',
    },
];

interface DashboardPageProps extends PageProps {
    user: User;
    investment_packages?: Array<{
        id: number;
        name: string;
        price: number;
        daily_income: number;
        total_return: number;
        duration_days: number;
        roi_percentage: number;
        icon: string;
        color: string;
    }>;
}

export default function Dashboard({ user, investment_packages = [] }: DashboardPageProps) {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />
            <div className="flex h-full flex-1 flex-col gap-6 p-4">
                <div className="space-y-6">
                    <WelcomeCard userName={user.name} membershipStatus="Premium Member" />
                    <ActionsBar />
                    <QuickTransactions />
                </div>
                <div className="mt-6">
                    <InvestmentPlans plans={investment_packages} />
                </div>
            </div>
        </AppLayout>
    );
}


