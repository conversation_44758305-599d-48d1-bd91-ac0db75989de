import { useState, useEffect } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, usePage } from '@inertiajs/react';
import { ArrowLeft, Copy, CheckCircle, Clock, CreditCard, Building } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import { Button } from '@/components/ui/button-new';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';
import { Badge } from '@/components/ui/badge';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/user/dashboard',
    },
    {
        title: 'Deposit',
        href: '/user/deposit',
    },
    {
        title: 'Virtual Account Payment',
        href: '#',
    },
];

interface VirtualAccountPaymentProps {
    auth: {
        user: {
            id: number;
            email: string;
            name: string;
            balance: number;
        };
    };
    virtualAccount: {
        id: number;
        account_number: string;
        account_name: string;
        bank_name: string;
        provider: string;
        is_active: boolean;
        created_at: string;
    };
    payment: {
        id: number;
        reference: string;
        amount: number;
        status: string;
        created_at: string;
    };
    amount: number;
}

export default function VirtualAccountPayment() {
    const { auth, virtualAccount, payment, amount } = usePage().props as unknown as VirtualAccountPaymentProps;
    const { toast } = useToast();
    const [copied, setCopied] = useState<string | null>(null);
    const [paymentStatus, setPaymentStatus] = useState(payment.status);
    const [isCheckingStatus, setIsCheckingStatus] = useState(false);

    const formatAmount = (amount: number) => {
        return new Intl.NumberFormat('en-NG', {
            style: 'currency',
            currency: 'NGN',
        }).format(amount / 100); // Convert from kobo to naira
    };

    const copyToClipboard = async (text: string, label: string) => {
        try {
            await navigator.clipboard.writeText(text);
            setCopied(label);
            toast({
                title: 'Copied!',
                description: `${label} copied to clipboard`,
                variant: 'default',
            });
            setTimeout(() => setCopied(null), 2000);
        } catch (err) {
            toast({
                title: 'Error',
                description: 'Failed to copy to clipboard',
                variant: 'destructive',
            });
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleString('en-NG', {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    };

    const cleanAccountName = (accountName: string) => {
        // Remove "[Voozoo Halibut Enterprise]" but keep "OT-PAY"
        return accountName
            .replace(/\s*-\s*\[.*?\]\((.*?)\)\s*$/i, ' - $1')
            .replace(/\s*\[.*?\]\s*$/i, '')
            .trim();
    };

    // Check payment status periodically
    useEffect(() => {
        if (paymentStatus === 'completed') {
            return; // Don't poll if already completed
        }

        const checkPaymentStatus = async () => {
            try {
                setIsCheckingStatus(true);
                const response = await fetch(`/api/payment/${payment.id}/status`, {
                    credentials: 'include',
                });

                if (response.ok) {
                    const data = await response.json();

                    if (data.status === 'completed' && paymentStatus !== 'completed') {
                        setPaymentStatus('completed');

                        // Show success notification
                        toast({
                            title: 'Payment Received! 🎉',
                            description: `Your wallet has been credited with ${formatAmount(amount)}`,
                            variant: 'default',
                        });

                        // Redirect to dashboard after 3 seconds
                        setTimeout(() => {
                            window.location.href = '/user/dashboard';
                        }, 3000);
                    }
                }
            } catch (error) {
                console.error('Error checking payment status:', error);
            } finally {
                setIsCheckingStatus(false);
            }
        };

        // Check immediately, then every 30 seconds
        checkPaymentStatus();
        const interval = setInterval(checkPaymentStatus, 30000);

        // Cleanup interval on unmount
        return () => clearInterval(interval);
    }, [payment.id, paymentStatus, amount, toast]);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Virtual Account Payment" />

            <div className="space-y-6 max-w-4xl mx-auto px-5">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900 mt-5">Complete Your Deposit</h1>
                        <p className="text-gray-600 text-sm md:text-base">Transfer money to the virtual account below to fund your wallet</p>
                    </div>
                </div>

                {/* Payment Status */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            {paymentStatus === 'completed' ? (
                                <>
                                    <CheckCircle className="w-5 h-5 text-green-500" />
                                    Payment Completed
                                </>
                            ) : (
                                <>
                                    <Clock className="w-5 h-5 text-orange-500" />
                                    Payment Pending
                                </>
                            )}
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-gray-600">Amount to deposit</p>
                                <p className="text-2xl font-bold text-green-600">{formatAmount(amount)}</p>
                            </div>
                            {paymentStatus === 'completed' ? (
                                <Badge variant="outline" className="text-green-600 border-green-200">
                                    Payment Received
                                </Badge>
                            ) : (
                                <Badge variant="outline" className="text-orange-600 border-orange-200">
                                    {isCheckingStatus ? 'Checking...' : 'Awaiting Payment'}
                                </Badge>
                            )}
                        </div>
                        <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <p className="text-gray-600">Reference</p>
                                <p className="font-mono">{payment.reference}</p>
                            </div>
                            <div>
                                <p className="text-gray-600">Created</p>
                                <p>{formatDate(payment.created_at)}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Virtual Account Details */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Building className="w-5 h-5 text-blue-500" />
                            Virtual Account Details
                        </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                        {/* Account Number */}
                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Account Number</p>
                                <p className="text-xl font-bold text-gray-900">{virtualAccount.account_number}</p>
                            </div>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(virtualAccount.account_number, 'Account Number')}
                                className="flex items-center gap-2"
                            >
                                {copied === 'Account Number' ? (
                                    <CheckCircle className="w-4 h-4 text-green-500" />
                                ) : (
                                    <Copy className="w-4 h-4" />
                                )}
                                Copy
                            </Button>
                        </div>

                        {/* Account Name */}
                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Account Name</p>
                                <p className="text-lg font-semibold text-gray-900">{cleanAccountName(virtualAccount.account_name)}</p>
                            </div>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(cleanAccountName(virtualAccount.account_name), 'Account Name')}
                                className="flex items-center gap-2"
                            >
                                {copied === 'Account Name' ? (
                                    <CheckCircle className="w-4 h-4 text-green-500" />
                                ) : (
                                    <Copy className="w-4 h-4" />
                                )}
                                Copy
                            </Button>
                        </div>

                        {/* Bank Name */}
                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                            <div>
                                <p className="text-sm font-medium text-gray-600">Bank</p>
                                <p className="text-lg font-semibold text-gray-900">{virtualAccount.bank_name}</p>
                            </div>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(virtualAccount.bank_name, 'Bank Name')}
                                className="flex items-center gap-2"
                            >
                                {copied === 'Bank Name' ? (
                                    <CheckCircle className="w-4 h-4 text-green-500" />
                                ) : (
                                    <Copy className="w-4 h-4" />
                                )}
                                Copy
                            </Button>
                        </div>

                        {/* Amount to Transfer */}
                        <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
                            <div>
                                <p className="text-sm font-medium text-green-700">Amount to Transfer</p>
                                <p className="text-2xl font-bold text-green-800">{formatAmount(amount)}</p>
                            </div>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard((amount / 100).toString(), 'Amount')}
                                className="flex items-center gap-2 border-green-300 text-green-700 hover:bg-green-100"
                            >
                                {copied === 'Amount' ? (
                                    <CheckCircle className="w-4 h-4 text-green-500" />
                                ) : (
                                    <Copy className="w-4 h-4" />
                                )}
                                Copy
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                {/* Instructions */}
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <CreditCard className="w-5 h-5 text-purple-500" />
                            Payment Instructions
                        </CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="space-y-3">
                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">1</div>
                                <p className="text-gray-700">Open your banking app or visit your bank</p>
                            </div>
                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">2</div>
                                <p className="text-gray-700">Transfer the exact amount <strong>{formatAmount(amount)}</strong> to the virtual account above</p>
                            </div>
                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">3</div>
                                <p className="text-gray-700">Your wallet will be credited automatically within 5 minutes</p>
                            </div>
                            <div className="flex items-start gap-3">
                                <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-semibold">4</div>
                                <p className="text-gray-700">You'll receive a confirmation notification once payment is processed</p>
                            </div>
                        </div>

                        <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <p className="text-sm text-yellow-800">
                                <strong>Important:</strong> Please transfer the exact amount shown above.
                                Transfers of different amounts may not be processed automatically.
                            </p>
                        </div>
                    </CardContent>
                </Card>

                {/* Action Buttons */}
                <div className="flex gap-4">
                    <Link href="/user/dashboard" className="flex-1">
                        <Button variant="outline" className="w-full">
                            Go to Dashboard
                        </Button>
                    </Link>
                    <Link href="/user/transactions" className="flex-1">
                        <Button className="w-full">
                            View Transactions
                        </Button>
                    </Link>
                </div>
            </div>
        </AppLayout>
    );
}
