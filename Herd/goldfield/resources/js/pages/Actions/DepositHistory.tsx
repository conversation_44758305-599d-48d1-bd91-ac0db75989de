import { Head } from '@inertiajs/react';
import { useState } from 'react';
import { Button } from '@/components/ui/button-new';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/currency';
import {
    Search,
    Filter,
    Download,
    Eye,
    Calendar,
    Building2,
    CreditCard,
    Smartphone,
    CheckCircle,
    XCircle,
    Clock
} from 'lucide-react';

interface Transaction {
    id: number;
    reference: string;
    amount: number;
    status: 'pending' | 'completed' | 'failed';
    metadata: {
        payment_method: string;
        bank_name?: string;
        account_number?: string;
        transaction_reference?: string;
        proof_of_payment?: string;
    };
    created_at: string;
    processed_at?: string;
}

interface Props {
    deposits: {
        data: Transaction[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
    };
    filters: {
        search?: string;
        status?: string;
        date_from?: string;
        date_to?: string;
    };
}

export default function DepositHistory({ deposits, filters }: Props) {
    const [searchTerm, setSearchTerm] = useState(filters.search || '');
    const [statusFilter, setStatusFilter] = useState(filters.status || '');
    const [showFilters, setShowFilters] = useState(false);

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'completed':
                return (
                    <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Completed
                    </Badge>
                );
            case 'failed':
                return (
                    <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
                        <XCircle className="w-3 h-3 mr-1" />
                        Failed
                    </Badge>
                );
            default:
                return (
                    <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
                        <Clock className="w-3 h-3 mr-1" />
                        Pending
                    </Badge>
                );
        }
    };

    const getPaymentMethodIcon = (method: string) => {
        switch (method) {
            case 'bank_transfer':
                return <Building2 className="w-4 h-4" />;
            case 'card':
                return <CreditCard className="w-4 h-4" />;
            case 'ussd':
                return <Smartphone className="w-4 h-4" />;
            default:
                return <Building2 className="w-4 h-4" />;
        }
    };

    const getPaymentMethodName = (method: string) => {
        switch (method) {
            case 'bank_transfer':
                return 'Bank Transfer';
            case 'card':
                return 'Debit/Credit Card';
            case 'ussd':
                return 'USSD';
            default:
                return 'Bank Transfer';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-NG', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const handleSearch = () => {
        const params = new URLSearchParams();
        if (searchTerm) params.append('search', searchTerm);
        if (statusFilter) params.append('status', statusFilter);

        window.location.href = `${route('deposits.history')}?${params.toString()}`;
    };

    const handleExport = () => {
        const params = new URLSearchParams(window.location.search);
        params.append('export', 'true');
        window.location.href = `${route('deposits.history')}?${params.toString()}`;
    };

    return (
        <>
            <Head title="Deposit History" />

            <div className="min-h-screen bg-gray-50 py-8">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="mb-8">
                        <div className="flex justify-between items-center">
                            <div>
                                <h1 className="text-3xl font-bold text-gray-900">Deposit History</h1>
                                <p className="text-gray-600 mt-2">Track all your deposit transactions</p>
                            </div>
                            <Button
                                onClick={() => window.location.href = route('deposits.create')}
                                className="bg-blue-600 hover:bg-blue-700"
                            >
                                Make New Deposit
                            </Button>
                        </div>
                    </div>

                    {/* Filters */}
                    <Card className="mb-6">
                        <CardContent className="pt-6">
                            <div className="flex flex-col md:flex-row gap-4">
                                <div className="flex-1">
                                    <div className="relative">
                                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                        <Input
                                            placeholder="Search by reference or transaction ID..."
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                            className="pl-10"
                                            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                        />
                                    </div>
                                </div>
                                <div className="flex gap-2">
                                    <select
                                        value={statusFilter}
                                        onChange={(e) => setStatusFilter(e.target.value)}
                                        className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All Status</option>
                                        <option value="pending">Pending</option>
                                        <option value="completed">Completed</option>
                                        <option value="failed">Failed</option>
                                    </select>
                                    <Button onClick={handleSearch} variant="outline">
                                        <Filter className="w-4 h-4 mr-2" />
                                        Filter
                                    </Button>
                                    <Button onClick={handleExport} variant="outline">
                                        <Download className="w-4 h-4 mr-2" />
                                        Export
                                    </Button>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Deposits Table */}
                    <Card>
                        <CardHeader>
                            <CardTitle>
                                Deposits ({deposits.total} total)
                            </CardTitle>
                        </CardHeader>
                        <CardContent>
                            {deposits.data.length === 0 ? (
                                <div className="text-center py-12">
                                    <div className="text-gray-400 mb-4">
                                        <Building2 className="w-16 h-16 mx-auto" />
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No deposits found</h3>
                                    <p className="text-gray-600 mb-6">You haven't made any deposits yet.</p>
                                    <Button
                                        onClick={() => window.location.href = route('deposits.create')}
                                        className="bg-blue-600 hover:bg-blue-700"
                                    >
                                        Make Your First Deposit
                                    </Button>
                                </div>
                            ) : (
                                <div className="overflow-x-auto">
                                    <table className="min-w-full divide-y divide-gray-200">
                                        <thead className="bg-gray-50">
                                            <tr>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Reference
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Amount
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Payment Method
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Status
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Date
                                                </th>
                                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                    Actions
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white divide-y divide-gray-200">
                                            {deposits.data.map((deposit) => (
                                                <tr key={deposit.id} className="hover:bg-gray-50">
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div>
                                                            <div className="text-sm font-medium text-gray-900">
                                                                {deposit.reference}
                                                            </div>
                                                            {deposit.metadata.transaction_reference && (
                                                                <div className="text-sm text-gray-500">
                                                                    Ref: {deposit.metadata.transaction_reference}
                                                                </div>
                                                            )}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm font-medium text-gray-900">
                                                            {formatCurrency(deposit.amount)}
                                                        </div>
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="flex items-center">
                                                            {getPaymentMethodIcon(deposit.metadata.payment_method)}
                                                            <span className="ml-2 text-sm text-gray-900">
                                                                {getPaymentMethodName(deposit.metadata.payment_method)}
                                                            </span>
                                                        </div>
                                                        {deposit.metadata.bank_name && (
                                                            <div className="text-sm text-gray-500">
                                                                {deposit.metadata.bank_name}
                                                            </div>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        {getStatusBadge(deposit.status)}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap">
                                                        <div className="text-sm text-gray-900">
                                                            {formatDate(deposit.created_at)}
                                                        </div>
                                                        {deposit.processed_at && (
                                                            <div className="text-sm text-gray-500">
                                                                Processed: {formatDate(deposit.processed_at)}
                                                            </div>
                                                        )}
                                                    </td>
                                                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            onClick={() => {
                                                                // Show deposit details modal or navigate to details page
                                                                console.log('View deposit details:', deposit.id);
                                                            }}
                                                        >
                                                            <Eye className="w-4 h-4 mr-1" />
                                                            View
                                                        </Button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            )}

                            {/* Pagination */}
                            {deposits.last_page > 1 && (
                                <div className="flex items-center justify-between mt-6">
                                    <div className="text-sm text-gray-700">
                                        Showing {((deposits.current_page - 1) * deposits.per_page) + 1} to{' '}
                                        {Math.min(deposits.current_page * deposits.per_page, deposits.total)} of{' '}
                                        {deposits.total} results
                                    </div>
                                    <div className="flex space-x-2">
                                        {deposits.current_page > 1 && (
                                            <Button
                                                variant="outline"
                                                onClick={() => {
                                                    const params = new URLSearchParams(window.location.search);
                                                    params.set('page', (deposits.current_page - 1).toString());
                                                    window.location.href = `${route('deposits.history')}?${params.toString()}`;
                                                }}
                                            >
                                                Previous
                                            </Button>
                                        )}
                                        {deposits.current_page < deposits.last_page && (
                                            <Button
                                                variant="outline"
                                                onClick={() => {
                                                    const params = new URLSearchParams(window.location.search);
                                                    params.set('page', (deposits.current_page + 1).toString());
                                                    window.location.href = `${route('deposits.history')}?${params.toString()}`;
                                                }}
                                            >
                                                Next
                                            </Button>
                                        )}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>
            </div>
        </>
    );
}