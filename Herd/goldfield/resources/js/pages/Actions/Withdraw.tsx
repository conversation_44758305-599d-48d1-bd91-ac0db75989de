import AppLayout from '@/layouts/app-layout';
import { <PERSON>, Link } from '@inertiajs/react';
import { ArrowLeft, Wallet, CreditCard, Banknote, WalletCards } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import { Button } from '@/components/ui/button-new';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/user/dashboard',
    },
    {
        title: 'Withdraw',
        href: '/user/withdraw',
    },
];

export default function Withdraw() {
    // Mock data - replace with actual data from your backend
    const balance = 1250.50;
    const quickAmounts = [5000, 10000, 20000, 50000, 100000, 200000];
    const bankAccounts = [
        { id: 1, name: 'GTBank', number: '***4567', type: 'Savings' },
        { id: 2, name: 'Access Bank', number: '***8910', type: 'Current' },
    ];

    const handleWithdraw = (e: React.FormEvent) => {
        e.preventDefault();
        // Handle withdraw logic here
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        // Allow: backspace, delete, tab, escape, enter, and numbers
        if (
            [46, 8, 9, 27, 13].includes(e.keyCode) ||
            // Allow: Ctrl+A, Command+A
            (e.keyCode === 65 && (e.ctrlKey === true || e.metaKey === true)) ||
            // Allow: Ctrl+C, Command+C
            (e.keyCode === 67 && (e.ctrlKey === true || e.metaKey === true)) ||
            // Allow: Ctrl+X, Command+X
            (e.keyCode === 88 && (e.ctrlKey === true || e.metaKey === true)) ||
            // Allow: home, end, left, right
            (e.keyCode >= 35 && e.keyCode <= 39) ||
            // Allow numbers on top of keyboard
            (e.keyCode >= 48 && e.keyCode <= 57) ||
            // Allow numbers on numpad
            (e.keyCode >= 96 && e.keyCode <= 105)
        ) {
            return;
        }

        // Prevent the default action for all other keys
        e.preventDefault();
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Withdraw" />
            <div className="container py-6 px-4">
                <div className="flex items-center gap-4 mb-6">
                    <Link href={route('dashboard')} className="text-muted-foreground hover:text-foreground">
                        <ArrowLeft className="h-5 w-5" />
                    </Link>
                    <h1 className="text-2xl font-bold">Withdraw Funds</h1>
                </div>

                {/* Balance Card */}
                <Card className="mb-6 border-0 shadow-sm bg-gradient-to-r from-orange-50 to-amber-50">
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-muted-foreground">Available Balance</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center gap-2">
                            <Wallet className="h-6 w-6 text-orange-500" />
                            <span className="text-3xl font-bold">₦{balance.toLocaleString()}</span>
                        </div>
                    </CardContent>
                </Card>

                {/* Withdraw Form */}
                <Card className="border-0 shadow-sm mb-6">
                    <CardHeader>
                        <CardTitle className="text-lg font-semibold">Withdraw Amount</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleWithdraw}>
                            <div className="mb-6">
                                <div className="relative flex items-baseline">
                                    <span className="text-2xl md:text-3xl font-medium text-foreground mr-2">
                                        ₦
                                    </span>
                                    <Input
                                        type="number"
                                        placeholder="0.00"
                                        className="flex-1 text-2xl md:text-3xl h-14 md:h-16 border-0 border-b-2 border-gray-200 focus-visible:ring-0 focus-visible:border-orange-500 rounded-none p-0 font-medium"
                                        min="5000"
                                        step="100"
                                        inputMode="numeric"
                                        pattern="[0-9]*"
                                        onKeyDown={handleKeyDown}
                                        required
                                    />
                                </div>
                                <p className="text-xs text-muted-foreground mt-2">
                                    Minimum withdrawal: ₦5,000
                                </p>
                            </div>

                            {/* Quick Amounts */}
                            <div className="mb-8">
                                <p className="text-sm text-muted-foreground mb-3">Quick Amount</p>
                                <div className="grid grid-cols-3 gap-2 sm:gap-3">
                                    {quickAmounts.map((amount) => (
                                        <Button
                                            key={amount}
                                            type="button"
                                            variant="outline"
                                            className="h-10 sm:h-12 text-sm sm:text-base rounded-xl"
                                            onClick={() => {
                                                const input = document.querySelector('input[type="number"]') as HTMLInputElement;
                                                if (input) input.value = amount.toString();
                                            }}
                                        >
                                            ₦{amount.toLocaleString()}
                                        </Button>
                                    ))}
                                </div>
                            </div>

                            {/* Bank Account Selection */}
                            <div className="mb-8">
                                <p className="text-sm font-medium mb-3">Withdraw to</p>
                                <div className="space-y-3">
                                    {bankAccounts.map((account) => (
                                        <div
                                            key={account.id}
                                            className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50"
                                        >
                                            <div className="bg-orange-100 p-2 rounded-full mr-3">
                                                <Banknote className="h-5 w-5 text-orange-500" />
                                            </div>
                                            <div className="flex-1">
                                                <p className="font-medium">{account.name} ••••{account.number}</p>
                                                <p className="text-sm text-muted-foreground">{account.type} Account</p>
                                            </div>
                                            <input
                                                type="radio"
                                                name="bankAccount"
                                                defaultChecked={account.id === 1}
                                                className="h-5 w-5 text-orange-500 border-gray-300 focus:ring-orange-500"
                                            />
                                        </div>
                                    ))}
                                    <Button
                                        type="button"
                                        variant="outline"
                                        className="w-full mt-2"
                                        asChild
                                    >
                                        <Link href={route('bank.account.add')}>
                                            <CreditCard className="h-4 w-4 mr-2" />
                                            Add New Bank Account
                                        </Link>
                                    </Button>
                                </div>
                            </div>

                            <Button
                                type="submit"
                                className="w-full h-12 text-base bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600"
                            >
                                Withdraw Now
                            </Button>
                        </form>
                    </CardContent>
                </Card>

                {/* Withdrawal Information */}
                <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-start">
                        <WalletCards className="h-5 w-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
                        <div>
                            <h3 className="font-medium text-blue-800">Withdrawal Information</h3>
                            <ul className="text-sm text-blue-700 mt-1 space-y-1">
                                <li>• Minimum withdrawal: ₦5,000</li>
                                <li>• Processing time: 1-3 business days</li>
                                <li>• No withdrawal fees</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
