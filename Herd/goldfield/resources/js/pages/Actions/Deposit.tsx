import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Head, Link, usePage } from '@inertiajs/react';
import { ArrowLeft, Wallet, Loader2 } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';
import { Button } from '@/components/ui/button-new';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useToast } from '@/components/ui/use-toast';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/user/dashboard',
    },
    {
        title: 'Deposit',
        href: '/user/deposit',
    },
];
interface DepositFormData {
    amount: string;
}

interface DepositProps {
    auth: {
        user: {
            id: number;
            email: string;
            balance: number;
        };
    };
    flash: {
        success?: string;
        error?: string;
    };
}

export default function Deposit() {
    const { auth, flash } = usePage().props as unknown as DepositProps;
    const { toast } = useToast();
    const [formData, setFormData] = useState<DepositFormData>({ amount: '' });
    const [isLoading, setIsLoading] = useState(false);

    const balance = auth.user.balance || 0;
    const quickAmounts = [5000, 10000, 20000, 50000, 100000, 200000];
    const minAmount = 100; // Minimum deposit amount in kobo (₦1.00 = 100 kobo)

    const handleDeposit = async (e: React.FormEvent) => {
        e.preventDefault();

        const amountInKobo = Math.round(parseFloat(formData.amount) * 100); // Convert to kobo

        if (isNaN(amountInKobo) || amountInKobo < minAmount) {
            toast({
                title: 'Error',
                description: `Minimum deposit amount is ₦${(minAmount / 100).toLocaleString()}`,
                variant: 'destructive',
            });
            return;
        }

        setIsLoading(true);

        try {
            console.log('Initializing payment...');
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
            console.log('CSRF Token:', csrfToken ? 'Found' : 'Not found');

            // Get the current URL for the callback
            const currentUrl = window.location.href;
            const baseUrl = currentUrl.split('/').slice(0, 3).join('/');
            const callbackUrl = `${baseUrl}/payment/callback`;

            console.log('Using callback URL:', callbackUrl);

            const response = await fetch(route('payment.initialize'), {
                method: 'POST',
                credentials: 'include',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken || '',
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    amount: amountInKobo,
                    email: auth.user.email,
                    type: 'deposit',
                    callback_url: callbackUrl,
                    metadata: {
                        user_id: auth.user.id,
                        description: 'Account deposit',
                        source: 'web_deposit_form'
                    }
                }),
            });

            console.log('Response status:', response.status);
            const data = await response.json();
            console.log('Response data:', data);

            if (!response.ok) {
                throw new Error(data.message || `HTTP error! status: ${response.status}`);
            }

            if (data.status === 'success') {
                if (data.data?.authorization_url) {
                    // For investment payments - redirect to OTPay payment page
                    console.log('Redirecting to:', data.data.authorization_url);
                    window.location.href = data.data.authorization_url;
                } else if (data.data?.virtual_account) {
                    // For deposits - redirect to virtual account payment page
                    console.log('Virtual account created, redirecting to payment page');
                    if (data.data?.redirect_url) {
                        window.location.href = data.data.redirect_url;
                    } else {
                        // Fallback: show toast if no redirect URL
                        const account = data.data.virtual_account;
                        toast({
                            title: 'Virtual Account Ready',
                            description: `Transfer to: ${account.account_number} (${account.bank_name})`,
                            variant: 'default',
                        });
                        setFormData({ amount: '' });
                    }
                } else {
                    throw new Error(data.message || 'Unexpected response format');
                }
            } else {
                throw new Error(data.message || 'Failed to initialize payment');
            }
        } catch (error) {
            console.error('Payment error:', error);
            toast({
                title: 'Error',
                description: error instanceof Error ? error.message : 'Failed to process payment',
                variant: 'destructive',
            });
        } finally {
            setIsLoading(false);
        }
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        // Allow: backspace, delete, tab, escape, enter, decimal point, and numbers
        if (
            [46, 8, 9, 27, 13, 110, 190].includes(e.keyCode) ||
            // Allow: Ctrl+A, Command+A
            (e.keyCode === 65 && (e.ctrlKey === true || e.metaKey === true)) ||
            // Allow: Ctrl+C, Command+C
            (e.keyCode === 67 && (e.ctrlKey === true || e.metaKey === true)) ||
            // Allow: Ctrl+X, Command+X
            (e.keyCode === 88 && (e.ctrlKey === true || e.metaKey === true)) ||
            // Allow: home, end, left, right
            (e.keyCode >= 35 && e.keyCode <= 39) ||
            // Allow numbers on top of keyboard
            (e.keyCode >= 48 && e.keyCode <= 57) ||
            // Allow numbers on numpad
            (e.keyCode >= 96 && e.keyCode <= 105)
        ) {
            // Allow only one decimal point
            if ((e.key === '.' || e.key === ',') && e.currentTarget.value.includes('.')) {
                e.preventDefault();
            }
            return;
        }

        // Prevent the default action for all other keys
        e.preventDefault();
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Deposit" />
            <div className="container py-6 px-4">
                {/* Success/Error Messages */}
                {flash?.success && (
                    <div className="mb-6 p-4 bg-green-50 text-green-800 rounded-lg">
                        {flash.success}
                    </div>
                )}
                {flash?.error && (
                    <div className="mb-6 p-4 bg-red-50 text-red-800 rounded-lg">
                        {flash.error}
                    </div>
                )}
                <div className="flex items-center gap-4 mb-6">
                    <Link href={route('user.dashboard')} className="text-muted-foreground hover:text-foreground">
                        <ArrowLeft className="h-5 w-5" />
                    </Link>
                    <h1 className="text-2xl font-bold">Deposit</h1>
                </div>

                {/* Balance Card */}
                <Card className="mb-6 border-0 shadow-sm bg-gradient-to-r from-orange-50 to-amber-50">
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-muted-foreground">Available Balance</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex items-center gap-2">
                            <Wallet className="h-6 w-6 text-orange-500" />
                            <span className="text-3xl font-bold">₦{balance.toLocaleString()}</span>
                        </div>
                    </CardContent>
                </Card>

                {/* Deposit Form */}
                <Card className="border-0 shadow-sm">
                    <CardHeader>
                        <CardTitle className="text-lg font-semibold">Deposit Amount</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <form onSubmit={handleDeposit} noValidate>
                            <div className="mb-6">
                                <div className="relative flex items-baseline">
                                    <span className="text-2xl md:text-3xl font-medium text-foreground mr-2">
                                        ₦
                                    </span>
                                    <Input
                                        type="number"
                                        placeholder="0.00"
                                        className="flex-1 text-2xl md:text-3xl h-14 md:h-16 border-0 border-b-2 border-gray-200 focus-visible:ring-0 focus-visible:border-orange-500 rounded-none p-0 font-medium"
                                        min={minAmount / 100}
                                        step="100"
                                        inputMode="decimal"
                                        pattern="[0-9]*[.,]?[0-9]*"
                                        value={formData.amount}
                                        onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                                        onKeyDown={handleKeyDown}
                                        required
                                        disabled={isLoading}
                                    />
                                </div>
                            </div>

                            {/* Quick Amounts */}
                            <div className="mb-8">
                                <p className="text-sm text-muted-foreground mb-3">Quick Amount</p>
                                <div className="grid grid-cols-3 gap-2 sm:gap-3">
                                    {quickAmounts.map((amount) => (
                                        <Button
                                            key={amount}
                                            type="button"
                                            variant="outline"
                                            className="h-10 sm:h-12 text-sm sm:text-base rounded-xl"
                                            onClick={() => {
                                                setFormData({ ...formData, amount: amount.toString() });
                                            }}
                                            disabled={isLoading}
                                        >
                                            ₦{amount}
                                        </Button>
                                    ))}
                                </div>
                            </div>

                            <Button
                                type="submit"
                                className="w-full h-12 text-base bg-gradient-to-r from-orange-500 to-amber-500 hover:from-orange-600 hover:to-amber-600"
                                disabled={isLoading}
                            >
                                {isLoading ? (
                                    <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Processing...
                                    </>
                                ) : (
                                    'Deposit Now'
                                )}
                            </Button>
                        </form>
                    </CardContent>
                </Card>

                {/* Payment Methods */}
                <div className="mt-6 text-center">
                    <p className="text-sm text-muted-foreground">We accept all major payment methods</p>
                    <div className="flex justify-center gap-4 mt-2">
                        <span className="text-xs bg-gray-100 px-3 py-1.5 rounded-full">Visa</span>
                        <span className="text-xs bg-gray-100 px-3 py-1.5 rounded-full">Mastercard</span>
                        <span className="text-xs bg-gray-100 px-3 py-1.5 rounded-full">Bank Transfer</span>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
