import { Link } from '@inertiajs/react';
import { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface PaginationProps {
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

export default function Pagination({ links }: PaginationProps) {
    if (links.length <= 3) {
        return null;
    }

    // Current page is determined by the active link, but not currently used in the UI
    const prevUrl = links[0].url;
    const nextUrl = links[links.length - 1].url;
    const firstPageUrl = links[1]?.url;
    const lastPageUrl = links[links.length - 2]?.url;

    return (
        <div className="flex items-center justify-between px-2 py-4">
            <div className="flex-1 flex justify-between sm:hidden">
                {prevUrl && (
                    <Link
                        href={prevUrl}
                        className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                        Previous
                    </Link>
                )}
                {nextUrl && (
                    <Link
                        href={nextUrl}
                        className="relative inline-flex items-center px-4 py-2 ml-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                    >
                        Next
                    </Link>
                )}
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p className="text-sm text-gray-700">
                        Showing <span className="font-medium">{links[0].label}</span> to{' '}
                        <span className="font-medium">{links[links.length - 1].label}</span> of{' '}
                        <span className="font-medium">{links[links.length - 1].label}</span> results
                    </p>
                </div>
                <div>
                    <nav className="relative z-0 inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                        {firstPageUrl && (
                            <Link
                                href={firstPageUrl}
                                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                            >
                                <span className="sr-only">First</span>
                                <ChevronsLeft className="h-5 w-5" aria-hidden="true" />
                            </Link>
                        )}
                        {prevUrl && (
                            <Link
                                href={prevUrl}
                                className="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                            >
                                <span className="sr-only">Previous</span>
                                <ChevronLeft className="h-5 w-5" aria-hidden="true" />
                            </Link>
                        )}

                        {links.slice(1, -1).map((link, index) => (
                            <Link
                                key={index}
                                href={link.url || '#'}
                                className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                                    link.active
                                        ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'
                                        : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                                }`}
                                aria-current={link.active ? 'page' : undefined}
                            >
                                {link.label}
                            </Link>
                        ))}

                        {nextUrl && (
                            <Link
                                href={nextUrl}
                                className="relative inline-flex items-center px-2 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                            >
                                <span className="sr-only">Next</span>
                                <ChevronRight className="h-5 w-5" aria-hidden="true" />
                            </Link>
                        )}
                        {lastPageUrl && (
                            <Link
                                href={lastPageUrl}
                                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                            >
                                <span className="sr-only">Last</span>
                                <ChevronsRight className="h-5 w-5" aria-hidden="true" />
                            </Link>
                        )}
                    </nav>
                </div>
            </div>
        </div>
    );
}
