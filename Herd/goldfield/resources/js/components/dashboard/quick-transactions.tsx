import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button-new';
import { Link } from '@inertiajs/react';
import {
    Plus,
    Minus,
    History,
    ArrowUpRight,
    Wallet,
    TrendingUp
} from 'lucide-react';

export function QuickTransactions() {
    return (
        <Card className="rounded-xl shadow-sm">
            <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                    <Wallet className="w-5 h-5 text-orange-500" />
                    Quick Actions
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-3">
                    <Link href={route('deposits.create')}>
                        <Button
                            variant="outline"
                            className="w-full h-12 flex flex-col items-center gap-1 hover:bg-green-50 hover:border-green-200"
                        >
                            <Plus className="w-4 h-4 text-green-600" />
                            <span className="text-xs">Deposit</span>
                        </Button>
                    </Link>

                    <Link href={route('user.withdraw')}>
                        <Button
                            variant="outline"
                            className="w-full h-12 flex flex-col items-center gap-1 hover:bg-red-50 hover:border-red-200"
                        >
                            <Minus className="w-4 h-4 text-red-600" />
                            <span className="text-xs">Withdraw</span>
                        </Button>
                    </Link>
                </div>

                <div className="grid grid-cols-2 gap-3">
                    <Link href={route('deposits.history')}>
                        <Button
                            variant="ghost"
                            className="w-full h-10 flex items-center justify-center gap-2 text-gray-600 hover:bg-gray-50"
                        >
                            <History className="w-4 h-4" />
                            <span className="text-xs">Deposit History</span>
                        </Button>
                    </Link>

                    <Link href={route('transactions.index')}>
                        <Button
                            variant="ghost"
                            className="w-full h-10 flex items-center justify-center gap-2 text-gray-600 hover:bg-gray-50"
                        >
                            <TrendingUp className="w-4 h-4" />
                            <span className="text-xs">All Transactions</span>
                        </Button>
                    </Link>
                </div>

                <div className="pt-2 border-t">
                    <Link href={route('withdrawals.index')}>
                        <Button className="w-full bg-orange-500 hover:bg-orange-600 text-white">
                            <ArrowUpRight className="w-4 h-4 mr-2" />
                            View Withdrawals
                        </Button>
                    </Link>
                </div>
            </CardContent>
        </Card>
    );
}
