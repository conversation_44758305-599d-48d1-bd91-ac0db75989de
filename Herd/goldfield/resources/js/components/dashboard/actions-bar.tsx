import { Card } from '@/components/ui/card';
import { Link } from '@inertiajs/react';
import { CalendarCheck, Landmark, Share2, UserCircle, Users, Wallet } from 'lucide-react';

const actions = [
    { label: 'Deposit', icon: Wallet, route: 'deposits.create' },
    { label: 'Withdraw', icon: Landmark, route: 'user.withdraw' },
    { label: 'Team', icon: Users, route: 'team' },
    { label: 'Invite', icon: Share2, route: 'user.invite' },
    { label: 'Gift Code', icon: CalendarCheck, route: 'gift-codes.redeem' },
    { label: 'Profile', icon: UserCircle, route: 'user.profile' },
];

export function ActionsBar() {
    return (
        <Card className="p-4 rounded-xl shadow-sm">
            <div className="grid grid-cols-3 gap-x-4 gap-y-6">
                {actions.map((action, index) => (
                                        action.route ? (
                        <Link
                            key={index}
                            href={route(action.route)}
                            className="flex flex-col items-center gap-2"
                        >
                            <action.icon className="size-7 text-orange-500" />
                            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                                {action.label}
                            </span>
                        </Link>
                    ) : (
                        <div key={index} className="flex flex-col items-center gap-2">
                            <action.icon className="size-7 text-orange-500" />
                            <span className="text-xs font-medium text-gray-600 dark:text-gray-400">
                                {action.label}
                            </span>
                        </div>
                    )
                ))}
            </div>
        </Card>
    );
}
