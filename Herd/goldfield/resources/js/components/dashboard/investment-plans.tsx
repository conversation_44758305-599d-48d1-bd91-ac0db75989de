import { <PERSON>, CardContent, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button-new';
import { ArrowRight } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

export type Plan = {
    id: number;
    name: string;
    price: number;
    daily_income: number;
    duration_days: number;
    total_return: number;
    roi_percentage: number;
};

interface InvestmentPlansProps {
    plans: Plan[];
}

export function InvestmentPlans({ plans }: InvestmentPlansProps) {
    const [isProcessing, setIsProcessing] = useState(false);

    const handlePayment = async (planId: number, amount: number) => {
        try {
            setIsProcessing(true);
            const response = await fetch('/payment/initialize', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    plan_id: planId,
                    amount: amount,
                }),
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || 'Failed to initialize payment');
            }

            if (data.authorization_url) {
                window.location.href = data.authorization_url;
            }
        } catch (error: unknown) {
            console.error('Payment error:', error);
            const errorMessage = error instanceof Error ? error.message : 'Failed to process payment. Please try again.';
            toast.error(errorMessage);
        } finally {
            setIsProcessing(false);
        }
    };
    return (
        <div className="w-full">
            <div className="mb-6">
                <h2 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-white">Investment Plans</h2>
                <p className="text-gray-600 dark:text-gray-300">Choose the perfect plan for your investment goals</p>
            </div>
            
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {plans.map((plan) => (
                    <Card 
                        key={plan.id}
                        className="relative overflow-hidden border-none bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-700"
                    >
                        <CardHeader>
                            <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                                {plan.name}
                            </CardTitle>
                            <div className="text-3xl font-bold text-orange-500">
                                ₦{plan.price.toLocaleString()}
                            </div>
                            <CardDescription className="text-gray-500 dark:text-gray-400">
                                {plan.duration_days} Days Plan
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-500 dark:text-gray-400">Daily Income</span>
                                    <span className="font-medium">₦{plan.daily_income.toLocaleString()}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-500 dark:text-gray-400">Total Return</span>
                                    <span className="font-medium">₦{plan.total_return.toLocaleString()}</span>
                                </div>
                                <div className="flex items-center justify-between">
                                    <span className="text-sm text-gray-500 dark:text-gray-400">ROI</span>
                                    <span className="font-medium">{plan.roi_percentage.toFixed(2)}%</span>
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter>
                            <Button
                                variant="default"
                                className="w-full"
                                onClick={() => handlePayment(plan.id, plan.price)}
                                disabled={isProcessing}
                            >
                                {isProcessing ? 'Processing...' : 'Buy Now'}
                                {!isProcessing && <ArrowRight className="ml-2 h-4 w-4" />}
                            </Button>
                        </CardFooter>
                    </Card>
                ))}
            </div>
        </div>
    );
}
