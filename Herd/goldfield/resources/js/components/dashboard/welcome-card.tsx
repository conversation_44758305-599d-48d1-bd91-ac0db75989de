import { Card } from '@/components/ui/card';
import { Star } from 'lucide-react';

interface WelcomeCardProps {
    userName: string;
    membershipStatus: string;
}

export function WelcomeCard({ userName, membershipStatus }: WelcomeCardProps) {
    return (
        <Card className="rounded-xl bg-orange-500 p-6 text-white shadow-lg">
            <div className="flex flex-col gap-2">
                <p className="text-sm font-light">Welcome back</p>
                <p className="text-2xl font-bold tracking-wider">{userName}</p>
                <div className="flex items-center gap-2 text-sm font-semibold">
                    <Star className="size-4 fill-white" />
                    <span>{membershipStatus}</span>
                </div>
            </div>
        </Card>
    );
}
