import { NavFooter } from '@/components/nav-footer';
import { NavMain } from '@/components/nav-main';
import { NavUser } from '@/components/nav-user';
import { Sidebar, SidebarContent, SidebarFooter, SidebarHeader, SidebarMenu, SidebarMenuButton, SidebarMenuItem, useSidebar } from '@/components/ui/sidebar';
import { type NavItem } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { Gift, LayoutGrid, Users, User, Settings, LogOut } from 'lucide-react';
import AppLogo from './app-logo';

const mainNavItems: NavItem[] = [
    {
        title: 'Dashboard',
        href: '/user/dashboard',
        icon: LayoutGrid,
        exact: true,
    },
    {
        title: 'Gift Codes',
        href: '/gift-codes/redeem',
        icon: Gift,
    },
    {
        title: 'Team',
        href: '#', // Update with actual team route
        icon: Users,
    },
];

const footerNavItems: NavItem[] = [
    {
        title: 'Profile',
        href: '/user/profile',
        icon: User,
    },
    {
        title: 'Settings',
        href: '#', // Update with actual settings route
        icon: Settings,
    },
];

interface AppSidebarProps {
    className?: string;
}

export function AppSidebar({ className }: AppSidebarProps) {
    const { state } = useSidebar();
    const isCollapsed = state === 'collapsed';
    const { url } = usePage();

    // Helper to check if a nav item is active
    const isActive = (href: string, exact = false) => {
        return exact ? url === href : url.startsWith(href);
    };

    return (
        <Sidebar collapsible="icon" className={className}>
            <SidebarHeader>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <SidebarMenuButton asChild>
                            <Link href="/user/dashboard" prefetch className="flex items-center justify-start w-full">
                                <AppLogo collapsed={isCollapsed} />
                            </Link>
                        </SidebarMenuButton>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarHeader>

            <SidebarContent className="mt-4">
                <NavMain 
                    items={mainNavItems.map(item => ({
                        ...item,
                        isActive: isActive(item.href, item.exact),
                    }))} 
                />
            </SidebarContent>

            <SidebarFooter className="mt-auto">
                <NavFooter 
                    items={footerNavItems.map(item => ({
                        ...item,
                        isActive: isActive(item.href, item.exact),
                    }))} 
                    className="mb-2"
                />
                <NavUser />
                <div className="mt-4">
                    <Link 
                        href={route('logout')} 
                        method="post" 
                        as="button"
                        className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-800 rounded-md"
                    >
                        <LogOut className="h-4 w-4 mr-3" />
                        {!isCollapsed && 'Sign out'}
                    </Link>
                </div>
            </SidebarFooter>
        </Sidebar>
    );
}
