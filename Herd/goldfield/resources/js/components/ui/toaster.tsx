import * as React from 'react';
import { usePage } from '@inertiajs/react';

import { Toast, ToastProvider, ToastViewport } from '@/components/ui/toast';
import { useToast } from '@/components/ui/use-toast';

export function Toaster() {
  const { toasts } = useToast();
  const { url } = usePage();

  // Clear all toasts when the route changes
  React.useEffect(() => {
    const handleRouteChange = () => {
      toasts.forEach((toast) => {
        document.dispatchEvent(new CustomEvent('toast-dismiss', { detail: { id: toast.id } }));
      });
    };

    handleRouteChange();
  }, [url, toasts]);

  return (
    <ToastProvider>
      {toasts.map(({ id, title, description, action, ...props }) => (
        <Toast key={id} {...props}>
          <div className="grid gap-1">
            {title && <div className="font-medium">{title}</div>}
            {description && <div className="text-sm opacity-90">{description}</div>}
          </div>
          {action}
        </Toast>
      ))}
      <ToastViewport />
    </ToastProvider>
  );
}
