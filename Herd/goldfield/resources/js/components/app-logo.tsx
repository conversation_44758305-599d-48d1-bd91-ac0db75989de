import { useIsMobile } from '@/hooks/use-mobile';

export default function AppLogo({ collapsed = false }: { collapsed?: boolean }) {
    const isMobile = useIsMobile();
    const showFullLogo = !collapsed || isMobile;

    return (
        <div className="flex items-center w-full">
            {showFullLogo ? (
                <img 
                    src="/logo.png" 
                    alt="GOLDFIELD Logo" 
                    className="h-8 w-auto max-w-[180px] object-contain" // Full logo for mobile and expanded desktop
                />
            ) : (
                <div className="flex items-center justify-center w-full">
                    <img 
                        src="/icon.png" 
                        alt="GOLDFIELD Icon" 
                        className="h-12 w-12 object-contain" // Icon only for collapsed desktop
                        style={{ minWidth: '2rem' }}
                    />
                </div>
            )}
        </div>
    );
}
