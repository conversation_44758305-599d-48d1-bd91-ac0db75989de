import { Link, usePage } from '@inertiajs/react';
import { type NavItem } from '@/types';
import { cn } from '@/lib/utils';

export function NavLink({ item }: { item: NavItem }) {
    const { url } = usePage();
    const isActive = url.startsWith(item.href);

    return (
        <Link
            href={item.href}
            className={cn(
                'flex flex-col items-center gap-1 rounded-md p-2 text-sm font-medium text-muted-foreground transition-colors hover:bg-accent hover:text-accent-foreground',
                isActive && 'text-orange-500',
            )}
        >
            {item.icon && <item.icon className="size-5" />}
            <span>{item.title}</span>
        </Link>
    );
}
