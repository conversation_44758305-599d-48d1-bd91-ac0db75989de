import { NavLink } from '@/components/nav-link';
import { type NavItem } from '@/types';
import { Home, Network, Share2, User } from 'lucide-react';

const mainNavItems: NavItem[] = [
    {
        title: 'Home',
        href: '/user/dashboard',
        icon: Home,
    },
    {
        title: 'Invest',
        href: '/investments',
        icon: Network,
    },
    {
        title: 'Invite',
        href: '/referrals',
        icon: Share2,
    },
    {
        title: 'Account',
        href: '/profile',
        icon: User,
    },
];

export function AppBottomNav() {
    return (
        <nav className="fixed inset-x-0 bottom-0 z-50 border-t bg-background/80 backdrop-blur-lg md:hidden">
            <div className="flex h-16 items-center justify-around">
                {mainNavItems.map((item) => (
                    <NavLink key={item.href} item={item} />
                ))}
            </div>
        </nav>
    );
}
