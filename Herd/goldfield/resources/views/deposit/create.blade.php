<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Make a Deposit') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                
                @if(session('error'))
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                        <p>{{ session('error') }}</p>
                    </div>
                @endif

                <form action="{{ route('deposit.store') }}" method="POST" class="space-y-6">
                    @csrf
                    
                    <!-- Amount Input -->
                    <div>
                        <label for="amount" class="block text-sm font-medium text-gray-700">
                            Amount to Deposit (₦)
                            <span class="text-red-500">*</span>
                        </label>
                        <div class="mt-1 relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">₦</span>
                            </div>
                            <input type="number" name="amount" id="amount" 
                                class="focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-7 pr-12 sm:text-sm border-gray-300 rounded-md" 
                                placeholder="0.00" 
                                min="100" 
                                step="1"
                                required>
                        </div>
                        <p class="mt-1 text-sm text-gray-500">Minimum deposit: ₦100.00</p>
                        
                        <!-- Quick Amount Buttons -->
                        <div class="mt-3 flex flex-wrap gap-2">
                            @foreach($quickAmounts as $amount)
                                <button type="button" 
                                    onclick="document.getElementById('amount').value = '{{ $amount }}'" 
                                    class="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50">
                                    ₦{{ number_format($amount) }}
                                </button>
                            @endforeach
                        </div>
                    </div>
                    
                    <!-- Bank Selection -->
                    <div>
                        <label for="bank_code" class="block text-sm font-medium text-gray-700">
                            Select Bank
                            <span class="text-red-500">*</span>
                        </label>
                        <select id="bank_code" name="bank_code" 
                            class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
                            required>
                            <option value="">-- Select Bank --</option>
                            @foreach($banks as $bank)
                                <option value="{{ $bank['code'] }}">
                                    {{ $bank['name'] }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    
                    <div class="pt-4">
                        <button type="submit" 
                            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                            Generate Virtual Account
                        </button>
                    </div>
                </form>
                
                <div class="mt-8 border-t border-gray-200 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100">How to deposit money</h3>
                    <ol class="mt-4 space-y-3 list-decimal list-inside text-sm text-gray-700 dark:text-gray-300">
                        <li>Enter the amount you want to deposit</li>
                        <li>Select your preferred bank</li>
                        <li>Click "Generate Virtual Account"</li>
                        <li>Transfer the exact amount to the virtual account provided</li>
                        <li>Your account will be credited automatically</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
