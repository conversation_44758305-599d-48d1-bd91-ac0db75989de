<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
            {{ __('Deposit Instructions') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900 dark:text-gray-100">
                
                @if(session('error'))
                    <div class="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
                        <p>{{ session('error') }}</p>
                    </div>
                @endif

                <div class="bg-green-50 p-4 rounded-lg mb-6">
                    <h3 class="text-lg font-medium text-green-800">Payment Instructions</h3>
                    <p class="mt-2 text-green-700">
                        Please transfer <strong>₦{{ number_format($amount, 2) }}</strong> to the virtual account below.
                        Your account will be credited automatically once the payment is confirmed.
                    </p>
                </div>

                <div class="bg-gray-50 p-6 rounded-lg mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h4 class="text-sm font-medium text-gray-500">Bank Name</h4>
                            <p class="mt-1 text-lg font-medium text-gray-900">{{ $account->bank_name }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500">Account Number</h4>
                            <p class="mt-1 text-lg font-mono font-medium text-gray-900">{{ $account->account_number }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500">Account Name</h4>
                            <p class="mt-1 text-lg font-medium text-gray-900">{{ $account->account_name }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-gray-500">Amount to Pay</h4>
                            <p class="mt-1 text-2xl font-bold text-green-600">₦{{ number_format($amount, 2) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-blue-50 p-4 rounded-lg mb-6">
                    <h4 class="text-sm font-medium text-blue-800 mb-2">Important Notes:</h4>
                    <ul class="list-disc pl-5 space-y-1 text-sm text-blue-700">
                        <li>Transfers are processed automatically within minutes</li>
                        <li>Use the exact amount shown above for faster processing</li>
                        <li>Only transfers from accounts bearing your name will be processed</li>
                        <li>If you encounter any issues, please contact support</li>
                    </ul>
                </div>

                <div class="mt-8 flex items-center justify-between">
                    <a href="{{ route('home') }}" class="inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest hover:bg-gray-300 active:bg-gray-300 focus:outline-none focus:border-gray-300 focus:ring focus:ring-gray-200 disabled:opacity-25 transition">
                        Back to Dashboard
                    </a>
                    <div class="flex space-x-3">
                        <button onclick="copyToClipboard('{{ $account->account_number }}')" class="inline-flex items-center px-4 py-2 bg-blue-500 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-600 active:bg-blue-600 focus:outline-none focus:border-blue-600 focus:ring focus:ring-blue-200 disabled:opacity-25 transition">
                            Copy Account Number
                        </button>
                        <a href="#" class="inline-flex items-center px-4 py-2 bg-green-500 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-green-600 active:bg-green-600 focus:outline-none focus:border-green-600 focus:ring focus:ring-green-200 disabled:opacity-25 transition">
                            I've Made the Transfer
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('Account number copied to clipboard!');
            }, function() {
                // Fallback for older browsers
                const textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
                alert('Account number copied to clipboard!');
            });
        }
        
        // Auto-refresh the page every 60 seconds to check for payment status
        setTimeout(function() {
            window.location.reload();
        }, 60000);
    </script>
    @endpush
</x-app-layout>
