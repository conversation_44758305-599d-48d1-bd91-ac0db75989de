<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->decimal('balance', 15, 2)->default(0)->after('password');
            $table->decimal('total_earnings', 15, 2)->default(0)->after('balance');
            $table->string('referral_code', 20)->unique()->nullable()->after('total_earnings');
            $table->foreignId('referred_by')->nullable()->constrained('users')->after('referral_code');
            $table->boolean('welcome_bonus_claimed')->default(false)->after('referred_by');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropForeign(['referred_by']);
            $table->dropColumn([
                'balance',
                'total_earnings',
                'referral_code',
                'referred_by',
                'welcome_bonus_claimed'
            ]);
        });
    }
};
