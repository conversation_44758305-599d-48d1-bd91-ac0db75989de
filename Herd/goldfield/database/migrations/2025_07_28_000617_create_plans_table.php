<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->decimal('min_deposit', 15, 2);
            $table->decimal('max_deposit', 15, 2)->nullable();
            
            // Plan type: fixed or flexible
            $table->enum('type', ['fixed', 'flexible'])->default('fixed');
            
            // For fixed plans
            $table->decimal('return', 8, 2)->nullable(); // Fixed return amount or percentage
            $table->enum('return_type', ['fixed', 'percent'])->default('percent');
            
            // For flexible plans (if type is flexible)
            $table->json('return_periods')->nullable(); // { days: return_percentage }
            
            // Plan duration in days (0 means no expiration)
            $table->integer('duration')->default(0);
            
            // Plan features as JSON
            $table->json('features')->nullable();
            
            // Plan status
            $table->boolean('is_active')->default(true);
            
            // Referral bonus
            $table->decimal('referral_bonus', 5, 2)->default(0);
            
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
