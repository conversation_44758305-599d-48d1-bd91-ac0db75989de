<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_gateway_settings', function (Blueprint $table) {
            if (!Schema::hasColumn('payment_gateway_settings', 'gateway_name')) {
                $table->string('gateway_name')->after('id');
            }
            
            if (!Schema::hasColumn('payment_gateway_settings', 'gateway_type')) {
                $table->string('gateway_type')->after('gateway_name');
            }
            
            if (!Schema::hasColumn('payment_gateway_settings', 'is_active')) {
                $table->boolean('is_active')->default(false)->after('gateway_type');
            }
            
            if (!Schema::hasColumn('payment_gateway_settings', 'credentials')) {
                $table->json('credentials')->nullable()->after('is_active');
            }
            
            if (!Schema::hasColumn('payment_gateway_settings', 'additional_data')) {
                $table->json('additional_data')->nullable()->after('credentials');
            }
            
            if (!Schema::hasColumn('payment_gateway_settings', 'deleted_at')) {
                $table->softDeletes();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_gateway_settings', function (Blueprint $table) {
            $columns = [
                'gateway_name',
                'gateway_type',
                'is_active',
                'credentials',
                'additional_data',
                'deleted_at'
            ];
            
            foreach ($columns as $column) {
                if (Schema::hasColumn('payment_gateway_settings', $column)) {
                    $table->dropColumn($column);
                }
            }
        });
    }
};
