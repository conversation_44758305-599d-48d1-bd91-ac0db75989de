<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            // Drop columns that are no longer needed
            $table->dropColumn(['min_deposit', 'max_deposit', 'type', 'return', 'return_type', 'return_periods', 'features', 'referral_bonus']);
            
            // Add new columns
            $table->decimal('price', 15, 2)->after('description');
            $table->decimal('daily_income', 15, 2)->after('price');
            $table->decimal('total_return', 15, 2)->after('daily_income');
            $table->string('icon')->nullable()->after('description');
            $table->string('color')->default('gray')->after('icon');
            
            // Modify existing columns
            $table->integer('duration')->default(60)->change();
            $table->boolean('is_active')->default(true)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plans', function (Blueprint $table) {
            // Reverse the changes if needed
            $table->decimal('min_deposit', 15, 2)->after('description');
            $table->decimal('max_deposit', 15, 2)->nullable()->after('min_deposit');
            $table->enum('type', ['fixed', 'flexible'])->default('fixed')->after('max_deposit');
            $table->decimal('return', 8, 2)->nullable()->after('type');
            $table->enum('return_type', ['fixed', 'percent'])->default('percent')->after('return');
            $table->json('return_periods')->nullable()->after('return_type');
            $table->json('features')->nullable()->after('duration');
            $table->decimal('referral_bonus', 5, 2)->default(0)->after('features');
            
            // Drop the new columns
            $table->dropColumn(['price', 'daily_income', 'total_return', 'icon', 'color']);
            
            // Revert duration and is_active to their original defaults
            $table->integer('duration')->default(0)->change();
        });
    }
};
