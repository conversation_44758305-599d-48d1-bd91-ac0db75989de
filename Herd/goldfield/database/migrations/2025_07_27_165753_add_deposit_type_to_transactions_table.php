<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            // Drop the existing enum constraint and recreate with deposit included
            $table->dropColumn('type');
        });

        Schema::table('transactions', function (Blueprint $table) {
            $table->enum('type', [
                'deposit',
                'investment',
                'withdrawal',
                'daily_income',
                'referral_bonus',
                'gift_code',
                'welcome_bonus'
            ])->after('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn('type');
        });

        Schema::table('transactions', function (Blueprint $table) {
            $table->enum('type', [
                'investment',
                'withdrawal',
                'daily_income',
                'referral_bonus',
                'gift_code',
                'welcome_bonus'
            ])->after('user_id');
        });
    }
};
