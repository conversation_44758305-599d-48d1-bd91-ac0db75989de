<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\InvestmentPackage;

class InvestmentPackageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $packages = [
            [
                'name' => 'Bronze',
                'price' => 5000.00,
                'daily_income' => 250.00,
                'duration_days' => 60,
                'total_return' => 15000.00,
                'is_active' => true,
            ],
            [
                'name' => 'Silver',
                'price' => 10000.00,
                'daily_income' => 550.00,
                'duration_days' => 60,
                'total_return' => 33000.00,
                'is_active' => true,
            ],
            [
                'name' => 'Gold',
                'price' => 25000.00,
                'daily_income' => 1500.00,
                'duration_days' => 60,
                'total_return' => 90000.00,
                'is_active' => true,
            ],
            [
                'name' => 'Sapphire',
                'price' => 50000.00,
                'daily_income' => 3250.00,
                'duration_days' => 60,
                'total_return' => 195000.00,
                'is_active' => true,
            ],
            [
                'name' => 'Ruby',
                'price' => 100000.00,
                'daily_income' => 7000.00,
                'duration_days' => 60,
                'total_return' => 420000.00,
                'is_active' => true,
            ],
            [
                'name' => 'Emerald',
                'price' => 250000.00,
                'daily_income' => 18750.00,
                'duration_days' => 60,
                'total_return' => 1125000.00,
                'is_active' => true,
            ],
            [
                'name' => 'Diamond',
                'price' => 500000.00,
                'daily_income' => 40000.00,
                'duration_days' => 60,
                'total_return' => 2400000.00,
                'is_active' => true,
            ],
        ];

        foreach ($packages as $package) {
            InvestmentPackage::updateOrCreate(
                ['name' => $package['name']],
                $package
            );
        }

        $this->command->info('Investment packages seeded successfully!');
    }
}
