<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user
        $this->call([
            AdminUserSeeder::class,
        ]);

        // Create test users
        $this->call([
            UserSeeder::class,
        ]);

        // Seed other data
        $this->call([
            GiftCodeSeeder::class,
            InvestmentPackageSeeder::class,
            PlanSeeder::class,
        ]);
    }
}
