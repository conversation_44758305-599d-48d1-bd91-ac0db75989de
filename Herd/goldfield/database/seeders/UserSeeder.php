<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create a regular user
        User::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'is_admin' => false,
                'balance' => 100000, // Starting balance for testing
                'total_earnings' => 0,
                'email_verified_at' => now(),
                'referral_code' => 'TEST123',
            ]
        );

        $this->command->info('Test user created successfully!');
        $this->command->info('Email: <EMAIL>');
        $this->command->info('Password: password123');
        
        // Create additional test users if needed
        for ($i = 1; $i <= 5; $i++) {
            User::updateOrCreate(
                ['email' => "user{$i}@example.com"],
                [
                    'name' => "Test User {$i}",
                    'email' => "user{$i}@example.com",
                    'password' => Hash::make('password123'),
                    'is_admin' => false,
                    'balance' => 100000, // Starting balance for testing
                    'total_earnings' => 0,
                    'email_verified_at' => now(),
                    'referral_code' => 'USER' . strtoupper(uniqid()),
                ]
            );
        }
        
        $this->command->info('5 additional test users created with email pattern: user1@example.<NAME_EMAIL>');
        $this->command->info('Password for all test users: password123');
    }
}
