<?php

namespace Database\Seeders;

use App\Models\GiftCode;
use Carbon\Carbon;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class GiftCodeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key constraints
        \DB::statement('SET FOREIGN_KEY_CHECKS=0');
        
        // Clear existing data
        \DB::table('gift_code_user')->truncate();
        \DB::table('gift_codes')->truncate();
        
        // Re-enable foreign key constraints
        \DB::statement('SET FOREIGN_KEY_CHECKS=1');
        
        // Create test gift codes
        $giftCodes = [
            [
                'code' => 'WELCOME10',
                'amount' => 10.00,
                'max_uses' => 100,
                'expires_at' => Carbon::now()->addYear(),
                'is_active' => true,
            ],
            [
                'code' => 'SUMMER25',
                'amount' => 25.00,
                'max_uses' => 50,
                'expires_at' => Carbon::now()->addMonths(3),
                'is_active' => true,
            ],
            [
                'code' => 'VIP50',
                'amount' => 50.00,
                'max_uses' => 10,
                'expires_at' => Carbon::now()->addMonth(),
                'is_active' => true,
            ],
            [
                'code' => 'EXPIRED20',
                'amount' => 20.00,
                'max_uses' => 100,
                'expires_at' => Carbon::now()->subMonth(),
                'is_active' => true,
            ],
            [
                'code' => 'INACTIVE15',
                'amount' => 15.00,
                'max_uses' => 50,
                'expires_at' => Carbon::now()->addYear(),
                'is_active' => false,
            ],
        ];

        foreach ($giftCodes as $giftCode) {
            GiftCode::create($giftCode);
        }

        $this->command->info('Gift codes seeded successfully!');
    }
}
