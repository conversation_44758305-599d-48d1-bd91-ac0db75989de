<?php

namespace Database\Seeders;

use App\Models\PaymentGatewaySetting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentGatewaySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create or update Monnify payment gateway
        PaymentGatewaySetting::updateOrCreate(
            ['gateway_name' => 'Monnify'],
            [
                'gateway_type' => 'monnify',
                'is_active' => false,
                'credentials' => [
                    'api_key' => '',
                    'secret_key' => '',
                    'contract_code' => '',
                ],
                'additional_data' => [
                    'description' => 'Monnify Payment Gateway',
                    'logo' => 'https://monnify.com/logo.png',
                ]
            ]
        );
    }
}
