<?php

namespace Database\Seeders;

use App\Models\Plan;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key checks to avoid constraint issues
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        // Truncate the table to avoid duplicate entries
        DB::table('plans')->truncate();
        
        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        $plans = [
            [
                'name' => 'BRONZE',
                'description' => 'Entry level investment plan',
                'price' => 3000,
                'daily_income' => 1000,
                'total_return' => 60000,
                'duration' => 60,
                'is_active' => true,
                'icon' => 'award',
                'color' => 'orange',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'SILVER',
                'description' => 'Standard investment plan',
                'price' => 6000,
                'daily_income' => 2000,
                'total_return' => 120000,
                'duration' => 60,
                'is_active' => true,
                'icon' => 'shield',
                'color' => 'gray',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'GOLD',
                'description' => 'Premium investment plan',
                'price' => 12000,
                'daily_income' => 4000,
                'total_return' => 240000,
                'duration' => 60,
                'is_active' => true,
                'icon' => 'star',
                'color' => 'yellow',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'SAPPHIRE',
                'description' => 'High-yield investment plan',
                'price' => 30000,
                'daily_income' => 10000,
                'total_return' => 600000,
                'duration' => 60,
                'is_active' => true,
                'icon' => 'gem',
                'color' => 'blue',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'RUBY',
                'description' => 'Exclusive investment plan',
                'price' => 60000,
                'daily_income' => 20000,
                'total_return' => 1200000,
                'duration' => 60,
                'is_active' => true,
                'icon' => 'gem',
                'color' => 'red',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'EMERALD',
                'description' => 'Premium elite investment plan',
                'price' => 120000,
                'daily_income' => 40000,
                'total_return' => 2400000,
                'duration' => 60,
                'is_active' => true,
                'icon' => 'gem',
                'color' => 'green',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'DIAMOND',
                'description' => 'Ultimate investment plan',
                'price' => 240000,
                'daily_income' => 80000,
                'total_return' => 4800000,
                'duration' => 60,
                'is_active' => true,
                'icon' => 'gem',
                'color' => 'blue',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        // Use DB facade to insert directly to avoid model events
        DB::table('plans')->insert($plans);
    }
}
