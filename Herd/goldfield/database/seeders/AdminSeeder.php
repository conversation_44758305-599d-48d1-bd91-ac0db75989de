<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        Admin::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Super Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role' => 'super_admin',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        Admin::updateOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Support Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('support123'),
                'role' => 'admin',
                'is_active' => true,
                'email_verified_at' => now(),
            ]
        );

        $this->command->info('Admin users created successfully!');
        $this->command->info('Super Admin - Email: <EMAIL>, Password: admin123');
        $this->command->info('Support Admin - Email: <EMAIL>, Password: support123');
    }
}
