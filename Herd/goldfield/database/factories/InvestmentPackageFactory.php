<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\InvestmentPackage;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\InvestmentPackage>
 */
class InvestmentPackageFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $price = $this->faker->randomFloat(2, 1000, 100000);
        $dailyIncome = $price * 0.05; // 5% daily return
        $durationDays = 60;
        $totalReturn = $dailyIncome * $durationDays;

        return [
            'name' => $this->faker->randomElement(InvestmentPackage::PACKAGE_NAMES),
            'price' => $price,
            'daily_income' => $dailyIncome,
            'duration_days' => $durationDays,
            'total_return' => $totalReturn,
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the package is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }
}
