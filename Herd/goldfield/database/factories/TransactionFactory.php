<?php

namespace Database\Factories;

use App\Models\Transaction;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Transaction>
 */
class TransactionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'type' => $this->faker->randomElement(Transaction::getTypes()),
            'amount' => $this->faker->randomFloat(2, 10, 10000),
            'description' => $this->faker->sentence(),
            'reference_id' => null,
            'status' => $this->faker->randomElement(Transaction::getStatuses()),
        ];
    }

    /**
     * Indicate that the transaction is for an investment.
     */
    public function investment()
    {
        return $this->state(fn (array $attributes) => [
            'type' => Transaction::TYPE_INVESTMENT,
        ]);
    }

    /**
     * Indicate that the transaction is for daily income.
     */
    public function dailyIncome()
    {
        return $this->state(fn (array $attributes) => [
            'type' => Transaction::TYPE_DAILY_INCOME,
        ]);
    }

    /**
     * Indicate that the transaction is for a withdrawal.
     */
    public function withdrawal()
    {
        return $this->state(fn (array $attributes) => [
            'type' => Transaction::TYPE_WITHDRAWAL,
        ]);
    }

    /**
     * Indicate that the transaction is for a referral bonus.
     */
    public function referralBonus()
    {
        return $this->state(fn (array $attributes) => [
            'type' => Transaction::TYPE_REFERRAL_BONUS,
        ]);
    }

    /**
     * Indicate that the transaction is for a gift code.
     */
    public function giftCode()
    {
        return $this->state(fn (array $attributes) => [
            'type' => Transaction::TYPE_GIFT_CODE,
        ]);
    }

    /**
     * Indicate that the transaction is for a welcome bonus.
     */
    public function welcomeBonus()
    {
        return $this->state(fn (array $attributes) => [
            'type' => Transaction::TYPE_WELCOME_BONUS,
        ]);
    }

    /**
     * Indicate that the transaction is completed.
     */
    public function completed()
    {
        return $this->state(fn (array $attributes) => [
            'status' => Transaction::STATUS_COMPLETED,
        ]);
    }

    /**
     * Indicate that the transaction is pending.
     */
    public function pending()
    {
        return $this->state(fn (array $attributes) => [
            'status' => Transaction::STATUS_PENDING,
        ]);
    }

    /**
     * Indicate that the transaction is failed.
     */
    public function failed()
    {
        return $this->state(fn (array $attributes) => [
            'status' => Transaction::STATUS_FAILED,
        ]);
    }
}
