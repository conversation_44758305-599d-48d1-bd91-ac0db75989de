<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Investment;
use App\Models\User;
use App\Models\InvestmentPackage;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Investment>
 */
class InvestmentFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('-30 days', 'now');
        $startDateCarbon = Carbon::parse($startDate);
        $endDate = $startDateCarbon->copy()->addDays(60);
        $dailyIncome = $this->faker->randomFloat(2, 50, 1000);

        return [
            'user_id' => User::factory(),
            'package_id' => InvestmentPackage::factory(),
            'amount' => $this->faker->randomFloat(2, 1000, 100000),
            'daily_income' => $dailyIncome,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'status' => $this->faker->randomElement(Investment::STATUSES),
            'total_earned' => $this->faker->randomFloat(2, 0, $dailyIncome * 60),
        ];
    }

    /**
     * Indicate that the investment is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Investment::STATUS_ACTIVE,
        ]);
    }

    /**
     * Indicate that the investment is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Investment::STATUS_COMPLETED,
        ]);
    }

    /**
     * Indicate that the investment is cancelled.
     */
    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Investment::STATUS_CANCELLED,
        ]);
    }
}
