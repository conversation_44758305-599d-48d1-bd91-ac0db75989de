<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\User;
use App\Models\DailyGiftCode;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\GiftCodeClaim>
 */
class GiftCodeClaimFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'gift_code_id' => DailyGiftCode::factory(),
            'amount' => $this->faker->randomFloat(2, 50, 200),
        ];
    }
}
