<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\DailyGiftCode;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\DailyGiftCode>
 */
class DailyGiftCodeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'code' => DailyGiftCode::generateCode(),
            'amount' => $this->faker->randomFloat(2, 50, 200),
            'valid_date' => Carbon::today(),
            'is_active' => true,
            'usage_limit' => 1000,
            'used_count' => 0,
        ];
    }

    /**
     * Indicate that the gift code is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn(array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the gift code is for yesterday.
     */
    public function yesterday(): static
    {
        return $this->state(fn(array $attributes) => [
            'valid_date' => Carbon::yesterday(),
        ]);
    }

    /**
     * Indicate that the gift code has reached its limit.
     */
    public function limitReached(): static
    {
        return $this->state(fn(array $attributes) => [
            'usage_limit' => 100,
            'used_count' => 100,
        ]);
    }
}
