<?php

namespace Database\Factories;

use App\Models\Referral;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Referral>
 */
class ReferralFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Referral::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $level = $this->faker->randomElement([1, 2]);

        return [
            'referrer_id' => User::factory(),
            'referee_id' => User::factory(),
            'level' => $level,
            'bonus_percentage' => $level === 1 ? 30.00 : 5.00,
            'total_earned' => $this->faker->randomFloat(2, 0, 1000),
        ];
    }

    /**
     * Indicate that the referral is level 1.
     */
    public function level1(): static
    {
        return $this->state(fn (array $attributes) => [
            'level' => 1,
            'bonus_percentage' => 30.00,
        ]);
    }

    /**
     * Indicate that the referral is level 2.
     */
    public function level2(): static
    {
        return $this->state(fn (array $attributes) => [
            'level' => 2,
            'bonus_percentage' => 5.00,
        ]);
    }
}
