# Requirements Document

## Introduction

The GOLDFIELD User Dashboard is a comprehensive interface for users to manage their mining investments, track earnings, view investment packages, and monitor their account activities. GOLDFIELD is an international mining organization that offers various investment packages with different durations, prices, and daily returns. The dashboard will provide users with real-time information about their investments, earnings, referral bonuses, and withdrawal capabilities.

## Requirements

### Requirement 1

**User Story:** As a GOLDFIELD investor, I want to view my current investment portfolio, so that I can track my active mining packages and their performance.

#### Acceptance Criteria

1. WHEN a user accesses the dashboard THEN the system SHALL display all active investment packages with their details
2. WHEN displaying investment packages THEN the system SHALL show package name, investment amount, daily income, remaining days, and total expected return
3. WHEN a user has multiple active investments THEN the system SHALL display them in a organized list or card format
4. WHEN an investment package expires THEN the system SHALL update the status to completed

### Requirement 2

**User Story:** As a GOLDFIELD investor, I want to see my total earnings and account balance, so that I can understand my financial position.

#### Acceptance Criteria

1. WHEN a user views the dashboard THEN the system SHALL display current account balance
2. WHEN displaying earnings THEN the system SHALL show total earnings, daily earnings, and pending withdrawals
3. WHEN calculating earnings THEN the system SHALL include investment returns and referral bonuses
4. WHEN earnings are updated THEN the system SHALL reflect changes in real-time

### Requirement 3

**User Story:** As a GOLDFIELD investor, I want to view available investment packages, so that I can make informed decisions about new investments.

#### Acceptance Criteria

1. WHEN a user accesses investment options THEN the system SHALL display all available packages (Bronze, Silver, Gold, Sapphire, Ruby, Emerald, Diamond)
2. WHEN displaying packages THEN the system SHALL show duration (60 days), price, daily income, and total return for each
3. WHEN a user selects a package THEN the system SHALL provide investment confirmation process
4. WHEN insufficient balance exists THEN the system SHALL prevent investment and show appropriate message

### Requirement 4

**User Story:** As a GOLDFIELD investor, I want to manage my withdrawals, so that I can access my earnings when needed.

#### Acceptance Criteria

1. WHEN a user requests withdrawal THEN the system SHALL verify minimum withdrawal amount (N1000)
2. WHEN processing withdrawal THEN the system SHALL apply 10% withdrawal charges
3. WHEN withdrawal is successful THEN the system SHALL update account balance and transaction history
4. WHEN withdrawal fails THEN the system SHALL display appropriate error message

### Requirement 5

**User Story:** As a GOLDFIELD investor, I want to track my referral earnings, so that I can monitor my referral program benefits.

#### Acceptance Criteria

1. WHEN a user views referral section THEN the system SHALL display referral statistics
2. WHEN showing referral earnings THEN the system SHALL display 30% first-level and 5% second-level bonuses
3. WHEN a referral makes investment THEN the system SHALL calculate and credit appropriate bonus
4. WHEN displaying referral history THEN the system SHALL show referral details and earned amounts

### Requirement 6

**User Story:** As a GOLDFIELD investor, I want to receive daily miners gift codes, so that I can claim additional bonuses.

#### Acceptance Criteria

1. WHEN daily gift time arrives (5:30 PM) THEN the system SHALL generate gift code
2. WHEN a user claims gift code THEN the system SHALL validate timing and eligibility
3. WHEN gift code is valid THEN the system SHALL credit bonus to user account
4. WHEN gift code is already claimed THEN the system SHALL prevent duplicate claims

### Requirement 7

**User Story:** As a GOLDFIELD investor, I want to view my transaction history, so that I can track all account activities.

#### Acceptance Criteria

1. WHEN a user accesses transaction history THEN the system SHALL display all account transactions
2. WHEN displaying transactions THEN the system SHALL show date, type, amount, and status
3. WHEN filtering transactions THEN the system SHALL allow filtering by type and date range
4. WHEN exporting history THEN the system SHALL provide downloadable transaction records

### Requirement 8

**User Story:** As a new GOLDFIELD investor, I want to receive a welcome bonus, so that I can start with additional funds.

#### Acceptance Criteria

1. WHEN a new user registers THEN the system SHALL credit N1,500 welcome bonus
2. WHEN welcome bonus is credited THEN the system SHALL update account balance
3. WHEN welcome bonus is already claimed THEN the system SHALL prevent duplicate bonuses
4. WHEN displaying bonus THEN the system SHALL show welcome bonus in transaction history