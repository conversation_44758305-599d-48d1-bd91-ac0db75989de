# Design Document

## Overview

The GOLDFIELD User Dashboard is a comprehensive web application built on Laravel with React/TypeScript frontend using Inertia.js. The system manages mining investment packages, user earnings, referral programs, and withdrawal processes. The application follows a modern full-stack architecture with server-side rendering capabilities and real-time data updates.

## Architecture

### Technology Stack
- **Backend**: Laravel 12 (PHP 8.2+)
- **Frontend**: React 19 with TypeScript
- **Bridge**: Inertia.js for seamless SPA experience
- **Database**: MySQL
- **Styling**: Tailwind CSS with Radix UI components
- **Build Tool**: Vite
- **State Management**: Inertia.js built-in state management

### System Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Client  │◄──►│   Inertia.js    │◄──►│  Laravel API    │
│   (Dashboard)   │    │   (Bridge)      │    │  (Controllers)  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                               ┌─────────────────┐
                                               │   Database      │
                                               │   (Models)      │
                                               └─────────────────┘
```

## Components and Interfaces

### Backend Components

#### Models
1. **User Model** (Extended)
   - Existing user model with additional fields for GOLDFIELD
   - Relationships to investments, transactions, referrals

2. **Investment Model**
   - Represents user investment packages
   - Tracks package type, amount, start date, status

3. **InvestmentPackage Model**
   - Defines available investment packages (Bronze, Silver, etc.)
   - Contains package details: price, daily_income, duration

4. **Transaction Model**
   - Records all financial transactions
   - Types: investment, withdrawal, referral_bonus, daily_income, gift_code

5. **Referral Model**
   - Manages referral relationships and bonuses
   - Tracks referrer, referee, and bonus levels

6. **DailyGiftCode Model**
   - Manages daily gift code generation and claims
   - Tracks code validity and user claims

#### Controllers
1. **DashboardController**
   - Main dashboard data aggregation
   - Returns dashboard statistics and recent activities

2. **InvestmentController**
   - Handles investment package purchases
   - Manages investment lifecycle

3. **WithdrawalController**
   - Processes withdrawal requests
   - Applies withdrawal charges and validations

4. **ReferralController**
   - Manages referral system
   - Calculates and distributes referral bonuses

5. **TransactionController**
   - Handles transaction history
   - Provides filtering and export capabilities

6. **GiftCodeController**
   - Manages daily gift code system
   - Handles code generation and claiming

### Frontend Components

#### Page Components
1. **Dashboard Page**
   - Main dashboard with overview cards
   - Investment summary and earnings display
   - Quick actions and notifications

2. **Investments Page**
   - Available investment packages display
   - Active investments management
   - Investment history

3. **Transactions Page**
   - Transaction history with filtering
   - Export functionality
   - Transaction details modal

4. **Referrals Page**
   - Referral statistics and earnings
   - Referral link sharing
   - Referral tree visualization

5. **Withdrawals Page**
   - Withdrawal request form
   - Withdrawal history
   - Balance and limits display

#### UI Components
1. **Investment Package Card**
   - Displays package details (price, daily income, total return)
   - Investment action button
   - Progress indicators for active investments

2. **Earnings Summary Card**
   - Total balance, daily earnings, pending withdrawals
   - Visual progress indicators
   - Quick withdrawal action

3. **Transaction Item**
   - Transaction details with icons and status
   - Amount formatting with currency
   - Date and time display

4. **Referral Stats Card**
   - Referral count and earnings
   - Level-based bonus breakdown
   - Referral link with copy functionality

5. **Gift Code Claim**
   - Daily gift code timer
   - Claim button with validation
   - Claimed status indicator

## Data Models

### Database Schema

#### Users Table (Extended)
```sql
users:
  id: bigint (primary key)
  name: varchar(255)
  email: varchar(255) unique
  email_verified_at: timestamp nullable
  password: varchar(255)
  balance: decimal(15,2) default 0
  total_earnings: decimal(15,2) default 0
  referral_code: varchar(20) unique
  referred_by: bigint nullable (foreign key to users.id)
  welcome_bonus_claimed: boolean default false
  created_at: timestamp
  updated_at: timestamp
```

#### Investment Packages Table
```sql
investment_packages:
  id: bigint (primary key)
  name: varchar(50) (Bronze, Silver, Gold, etc.)
  price: decimal(15,2)
  daily_income: decimal(15,2)
  duration_days: integer (60)
  total_return: decimal(15,2)
  is_active: boolean default true
  created_at: timestamp
  updated_at: timestamp
```

#### Investments Table
```sql
investments:
  id: bigint (primary key)
  user_id: bigint (foreign key to users.id)
  package_id: bigint (foreign key to investment_packages.id)
  amount: decimal(15,2)
  daily_income: decimal(15,2)
  start_date: date
  end_date: date
  status: enum('active', 'completed', 'cancelled')
  total_earned: decimal(15,2) default 0
  created_at: timestamp
  updated_at: timestamp
```

#### Transactions Table
```sql
transactions:
  id: bigint (primary key)
  user_id: bigint (foreign key to users.id)
  type: enum('investment', 'withdrawal', 'daily_income', 'referral_bonus', 'gift_code', 'welcome_bonus')
  amount: decimal(15,2)
  description: text
  reference_id: bigint nullable (investment_id, referral_id, etc.)
  status: enum('pending', 'completed', 'failed')
  created_at: timestamp
  updated_at: timestamp
```

#### Referrals Table
```sql
referrals:
  id: bigint (primary key)
  referrer_id: bigint (foreign key to users.id)
  referee_id: bigint (foreign key to users.id)
  level: integer (1 or 2)
  bonus_percentage: decimal(5,2) (30.00 or 5.00)
  total_earned: decimal(15,2) default 0
  created_at: timestamp
  updated_at: timestamp
```

#### Daily Gift Codes Table
```sql
daily_gift_codes:
  id: bigint (primary key)
  code: varchar(50) unique
  amount: decimal(15,2)
  valid_date: date
  valid_time: time (17:30:00)
  is_active: boolean default true
  created_at: timestamp
  updated_at: timestamp
```

#### Gift Code Claims Table
```sql
gift_code_claims:
  id: bigint (primary key)
  user_id: bigint (foreign key to users.id)
  gift_code_id: bigint (foreign key to daily_gift_codes.id)
  claimed_at: timestamp
  created_at: timestamp
  updated_at: timestamp
```

## Error Handling

### Backend Error Handling
1. **Validation Errors**
   - Form request validation for all user inputs
   - Custom validation rules for investment amounts and withdrawal limits
   - API response formatting with error messages

2. **Business Logic Errors**
   - Insufficient balance checks
   - Investment package availability validation
   - Withdrawal limit and timing validations
   - Gift code claim eligibility checks

3. **Database Errors**
   - Transaction rollbacks for failed operations
   - Constraint violation handling
   - Connection error recovery

### Frontend Error Handling
1. **Form Validation**
   - Real-time input validation
   - Error message display with user-friendly messages
   - Disabled states for invalid forms

2. **API Error Handling**
   - Network error handling with retry mechanisms
   - Server error display with appropriate messages
   - Loading states during API calls

3. **User Experience**
   - Toast notifications for success/error states
   - Graceful degradation for failed components
   - Offline state handling

## Testing Strategy

### Backend Testing
1. **Unit Tests**
   - Model relationships and methods
   - Service class business logic
   - Helper functions and utilities

2. **Feature Tests**
   - Controller endpoints with authentication
   - Investment purchase flow
   - Withdrawal process
   - Referral bonus calculations
   - Gift code claiming system

3. **Integration Tests**
   - Database transactions
   - Email notifications
   - Scheduled tasks (daily income, gift codes)

### Frontend Testing
1. **Component Tests**
   - Individual component rendering
   - User interaction handling
   - Props and state management

2. **Integration Tests**
   - Page-level functionality
   - Form submissions
   - API integration
   - Navigation flows

3. **End-to-End Tests**
   - Complete user workflows
   - Investment purchase process
   - Withdrawal request flow
   - Referral system functionality

### Performance Considerations
1. **Database Optimization**
   - Proper indexing on frequently queried columns
   - Query optimization for dashboard statistics
   - Pagination for transaction history

2. **Frontend Optimization**
   - Component lazy loading
   - Memoization for expensive calculations
   - Efficient re-rendering strategies

3. **Caching Strategy**
   - Investment package caching
   - User statistics caching
   - Daily gift code caching

### Security Measures
1. **Authentication & Authorization**
   - Laravel Sanctum for API authentication
   - Role-based access control
   - CSRF protection

2. **Data Validation**
   - Server-side validation for all inputs
   - SQL injection prevention
   - XSS protection

3. **Financial Security**
   - Transaction logging and auditing
   - Withdrawal verification processes
   - Balance consistency checks
