<?php

// Test account name cleaning
echo "🧹 TESTING ACCOUNT NAME CLEANING\n";
echo "=================================\n\n";

require_once __DIR__ . '/vendor/autoload.php';
$app = require_once __DIR__ . '/bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    // Get a virtual account to see the current account name format
    $virtualAccount = App\Models\VirtualAccount::first();
    
    if ($virtualAccount) {
        echo "📋 CURRENT VIRTUAL ACCOUNT DATA:\n";
        echo "   Raw account name: '{$virtualAccount->account_name}'\n";
        echo "   Account number: {$virtualAccount->account_number}\n";
        echo "   Bank: {$virtualAccount->bank_name}\n\n";
        
        // Simulate the JavaScript cleaning function in PHP
        function cleanAccountName($accountName) {
            // Remove the "[Voozoo Halibut Enterprise](OT-PAY)" suffix and clean up
            $cleaned = preg_replace('/\s*-\s*\[.*?\]\(.*?\)\s*$/i', '', $accountName);
            $cleaned = preg_replace('/\s*\[.*?\]\s*$/i', '', $cleaned);
            return trim($cleaned);
        }
        
        $cleanedName = cleanAccountName($virtualAccount->account_name);
        
        echo "🧹 ACCOUNT NAME CLEANING RESULT:\n";
        echo "   Original: '{$virtualAccount->account_name}'\n";
        echo "   Cleaned:  '{$cleanedName}'\n\n";
        
        if ($cleanedName !== $virtualAccount->account_name) {
            echo "✅ SUCCESS: Account name cleaned successfully!\n";
            echo "   Removed: '" . str_replace($cleanedName, '', $virtualAccount->account_name) . "'\n";
        } else {
            echo "ℹ️  INFO: Account name doesn't contain the enterprise suffix\n";
        }
        
        echo "\n📱 USER EXPERIENCE IMPROVEMENT:\n";
        echo "   Before: User sees '{$virtualAccount->account_name}'\n";
        echo "   After:  User sees '{$cleanedName}'\n";
        echo "   Result: Cleaner, more professional display\n\n";
        
        // Test with different account name formats
        echo "🧪 TESTING DIFFERENT FORMATS:\n";
        $testNames = [
            'John Doe - [Voozoo Halibut Enterprise](OT-PAY)',
            'Jane Smith - [Voozoo Halibut Enterprise]',
            'Bob Johnson [Enterprise Name]',
            'Alice Brown',
            'Mike Wilson - [Some Company](PROVIDER)',
        ];
        
        foreach ($testNames as $testName) {
            $cleaned = cleanAccountName($testName);
            echo "   '{$testName}' → '{$cleaned}'\n";
        }
        
        echo "\n🎯 IMPLEMENTATION COMPLETE!\n";
        echo "The virtual account payment page will now show clean account names.\n";
        
    } else {
        echo "❌ No virtual accounts found in database\n";
        echo "Create a deposit first to test account name cleaning\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
